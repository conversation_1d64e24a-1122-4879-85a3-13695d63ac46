'use client';

import { memo } from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { FileNode } from '@/types';
import {
  Code,
  FileText,
  Image as ImageIcon,
  Settings,
  GitBranch
} from 'lucide-react';
import { clsx } from 'clsx';

export const FileNodeComponent = memo(({ data, selected }: { data: { fileNode: FileNode; isSelected: boolean; onSelect: (nodeId: string) => void }; selected?: boolean }) => {
  const { fileNode, isSelected, onSelect } = data;

  const getFileIcon = () => {
    const extension = fileNode.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
      case 'py':
      case 'java':
      case 'cpp':
      case 'c':
      case 'cs':
      case 'php':
      case 'rb':
      case 'go':
      case 'rs':
      case 'swift':
      case 'kt':
        return Code;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
      case 'webp':
        return ImageIcon;
      case 'json':
      case 'xml':
      case 'yaml':
      case 'yml':
      case 'toml':
      case 'ini':
        return Settings;
      default:
        return FileText;
    }
  };

  const getLanguageColor = (language?: string): string => {
    const colors: Record<string, string> = {
      javascript: 'border-yellow-400 bg-yellow-50',
      typescript: 'border-blue-400 bg-blue-50',
      python: 'border-green-400 bg-green-50',
      java: 'border-red-400 bg-red-50',
      cpp: 'border-purple-400 bg-purple-50',
      c: 'border-gray-400 bg-gray-50',
      csharp: 'border-indigo-400 bg-indigo-50',
      php: 'border-purple-500 bg-purple-50',
      ruby: 'border-red-500 bg-red-50',
      go: 'border-cyan-400 bg-cyan-50',
      rust: 'border-orange-400 bg-orange-50',
      swift: 'border-orange-500 bg-orange-50',
      kotlin: 'border-purple-600 bg-purple-50',
      html: 'border-orange-500 bg-orange-50',
      css: 'border-blue-500 bg-blue-50',
      scss: 'border-pink-500 bg-pink-50',
      json: 'border-yellow-500 bg-yellow-50',
      markdown: 'border-gray-600 bg-gray-50',
    };
    return colors[language || ''] || 'border-gray-300 bg-white';
  };

  const Icon = getFileIcon();
  const colorClass = getLanguageColor(fileNode.language);
  const hasDependencies = fileNode.dependencies && fileNode.dependencies.length > 0;

  return (
    <div
      className={clsx(
        'px-4 py-3 shadow-md rounded-lg border-2 cursor-pointer transition-all duration-200 min-w-[180px] max-w-[220px]',
        colorClass,
        isSelected || selected 
          ? 'ring-2 ring-blue-500 ring-offset-2 shadow-lg scale-105' 
          : 'hover:shadow-lg hover:scale-102'
      )}
      onClick={() => onSelect(fileNode.id)}
    >
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500 !border-2 !border-white"
      />

      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          <Icon className="w-5 h-5 text-gray-600" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {fileNode.name}
          </div>
          
          {fileNode.language && (
            <div className="text-xs text-gray-600 mt-1">
              {fileNode.language}
            </div>
          )}
          
          {fileNode.size && (
            <div className="text-xs text-gray-500 mt-1">
              {(fileNode.size / 1024).toFixed(1)} KB
            </div>
          )}
          
          {hasDependencies && (
            <div className="flex items-center space-x-1 mt-2">
              <GitBranch className="w-3 h-3 text-blue-600" />
              <span className="text-xs text-blue-600 font-medium">
                {fileNode.dependencies!.length} bağımlılık
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-green-500 !border-2 !border-white"
      />
    </div>
  );
});

FileNodeComponent.displayName = 'FileNodeComponent';
