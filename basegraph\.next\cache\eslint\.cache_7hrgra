[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\api\\analyze\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeEditor.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeGraphViewer.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\GitHubRepoInput.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\NodeGraphView.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\nodes\\FileNodeComponent.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\RepositoryInfo.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\TreeView.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\button.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\input.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\codeAnalyzer.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\github.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\types\\index.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\aiCodeAnalyzer.ts": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\AIProviderConfig.tsx": "17"}, {"size": 5074, "mtime": 1753356711983, "results": "18", "hashOfConfig": "19"}, {"size": 689, "mtime": 1753355069907, "results": "20", "hashOfConfig": "19"}, {"size": 3843, "mtime": 1753357281121, "results": "21", "hashOfConfig": "19"}, {"size": 5632, "mtime": 1753355651108, "results": "22", "hashOfConfig": "19"}, {"size": 2020, "mtime": 1753355613571, "results": "23", "hashOfConfig": "19"}, {"size": 5397, "mtime": 1753357578994, "results": "24", "hashOfConfig": "19"}, {"size": 11055, "mtime": 1753357201269, "results": "25", "hashOfConfig": "19"}, {"size": 4259, "mtime": 1753356193793, "results": "26", "hashOfConfig": "19"}, {"size": 7141, "mtime": 1753356870160, "results": "27", "hashOfConfig": "19"}, {"size": 12830, "mtime": 1753356761785, "results": "28", "hashOfConfig": "19"}, {"size": 1726, "mtime": 1753355166689, "results": "29", "hashOfConfig": "19"}, {"size": 856, "mtime": 1753356009702, "results": "30", "hashOfConfig": "19"}, {"size": 15221, "mtime": 1753357173120, "results": "31", "hashOfConfig": "19"}, {"size": 4572, "mtime": 1753357608773, "results": "32", "hashOfConfig": "19"}, {"size": 1497, "mtime": 1753356389001, "results": "33", "hashOfConfig": "19"}, {"size": 6267, "mtime": 1753356984470, "results": "34", "hashOfConfig": "19"}, {"size": 11843, "mtime": 1753357268585, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t8b5ag", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\api\\analyze\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeEditor.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeGraphViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\GitHubRepoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\NodeGraphView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\nodes\\FileNodeComponent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\RepositoryInfo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\TreeView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\codeAnalyzer.ts", ["87", "88"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\github.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\aiCodeAnalyzer.ts", ["89"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\AIProviderConfig.tsx", [], [], {"ruleId": "90", "severity": 1, "message": "91", "line": 1, "column": 50, "nodeType": null, "messageId": "92", "endLine": 1, "endColumn": 62}, {"ruleId": "90", "severity": 1, "message": "93", "line": 210, "column": 64, "nodeType": null, "messageId": "92", "endLine": 210, "endColumn": 72}, {"ruleId": "90", "severity": 1, "message": "91", "line": 2, "column": 24, "nodeType": null, "messageId": "92", "endLine": 2, "endColumn": 36}, "@typescript-eslint/no-unused-vars", "'FunctionCall' is defined but never used.", "unusedVar", "'language' is defined but never used."]