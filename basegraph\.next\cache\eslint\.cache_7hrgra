[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\api\\analyze\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeEditor.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeGraphViewer.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\GitHubRepoInput.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\NodeGraphView.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\nodes\\FileNodeComponent.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\RepositoryInfo.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\TreeView.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\button.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\input.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\codeAnalyzer.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\github.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\types\\index.ts": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\aiCodeAnalyzer.ts": "16"}, {"size": 5074, "mtime": 1753356711983, "results": "17", "hashOfConfig": "18"}, {"size": 689, "mtime": 1753355069907, "results": "19", "hashOfConfig": "18"}, {"size": 2558, "mtime": 1753355986372, "results": "20", "hashOfConfig": "18"}, {"size": 5632, "mtime": 1753355651108, "results": "21", "hashOfConfig": "18"}, {"size": 2020, "mtime": 1753355613571, "results": "22", "hashOfConfig": "18"}, {"size": 2723, "mtime": 1753356063662, "results": "23", "hashOfConfig": "18"}, {"size": 11055, "mtime": 1753356835795, "results": "24", "hashOfConfig": "18"}, {"size": 4259, "mtime": 1753356193793, "results": "25", "hashOfConfig": "18"}, {"size": 7141, "mtime": 1753356870160, "results": "26", "hashOfConfig": "18"}, {"size": 12830, "mtime": 1753356761785, "results": "27", "hashOfConfig": "18"}, {"size": 1726, "mtime": 1753355166689, "results": "28", "hashOfConfig": "18"}, {"size": 856, "mtime": 1753356009702, "results": "29", "hashOfConfig": "18"}, {"size": 13649, "mtime": 1753356664067, "results": "30", "hashOfConfig": "18"}, {"size": 2178, "mtime": 1753355902363, "results": "31", "hashOfConfig": "18"}, {"size": 1497, "mtime": 1753356389001, "results": "32", "hashOfConfig": "18"}, {"size": 5722, "mtime": 1753356562392, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t8b5ag", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\api\\analyze\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeEditor.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeGraphViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\GitHubRepoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\NodeGraphView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\nodes\\FileNodeComponent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\RepositoryInfo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\TreeView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\codeAnalyzer.ts", ["82", "83"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\github.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\aiCodeAnalyzer.ts", ["84"], [], {"ruleId": "85", "severity": 1, "message": "86", "line": 1, "column": 50, "nodeType": null, "messageId": "87", "endLine": 1, "endColumn": 62}, {"ruleId": "85", "severity": 1, "message": "88", "line": 175, "column": 64, "nodeType": null, "messageId": "87", "endLine": 175, "endColumn": 72}, {"ruleId": "85", "severity": 1, "message": "86", "line": 2, "column": 24, "nodeType": null, "messageId": "87", "endLine": 2, "endColumn": 36}, "@typescript-eslint/no-unused-vars", "'FunctionCall' is defined but never used.", "unusedVar", "'language' is defined but never used."]