[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\api\\analyze\\route.ts": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeEditor.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeGraphViewer.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\GitHubRepoInput.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\NodeGraphView.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\nodes\\FileNodeComponent.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\RepositoryInfo.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\TreeView.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\button.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\input.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\codeAnalyzer.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\github.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\types\\index.ts": "15"}, {"size": 4447, "mtime": 1753355485150, "results": "16", "hashOfConfig": "17"}, {"size": 689, "mtime": 1753355069907, "results": "18", "hashOfConfig": "17"}, {"size": 2558, "mtime": 1753355986372, "results": "19", "hashOfConfig": "17"}, {"size": 5632, "mtime": 1753355651108, "results": "20", "hashOfConfig": "17"}, {"size": 2020, "mtime": 1753355613571, "results": "21", "hashOfConfig": "17"}, {"size": 2723, "mtime": 1753356063662, "results": "22", "hashOfConfig": "17"}, {"size": 7819, "mtime": 1753356119331, "results": "23", "hashOfConfig": "17"}, {"size": 4259, "mtime": 1753356193793, "results": "24", "hashOfConfig": "17"}, {"size": 6180, "mtime": 1753355950891, "results": "25", "hashOfConfig": "17"}, {"size": 9436, "mtime": 1753355926854, "results": "26", "hashOfConfig": "17"}, {"size": 1726, "mtime": 1753355166689, "results": "27", "hashOfConfig": "17"}, {"size": 856, "mtime": 1753356009702, "results": "28", "hashOfConfig": "17"}, {"size": 7479, "mtime": 1753355229938, "results": "29", "hashOfConfig": "17"}, {"size": 2178, "mtime": 1753355902363, "results": "30", "hashOfConfig": "17"}, {"size": 988, "mtime": 1753355183475, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t8b5ag", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\api\\analyze\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeEditor.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\CodeGraphViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\GitHubRepoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\NodeGraphView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\nodes\\FileNodeComponent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\RepositoryInfo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\TreeView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\codeAnalyzer.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\lib\\github.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\BaseGraph\\basegraph\\src\\types\\index.ts", [], []]