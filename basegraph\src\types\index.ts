export interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  owner: {
    login: string;
    avatar_url: string;
  };
  description: string | null;
  html_url: string;
  clone_url: string;
  default_branch: string;
  language: string | null;
  languages_url: string;
  contents_url: string;
}

export interface FunctionInfo {
  name: string;
  startLine: number;
  endLine: number;
  parameters: string[];
  returnType?: string;
  isExported?: boolean;
  isAsync?: boolean;
  calls?: FunctionCall[];
}

export interface FunctionCall {
  functionName: string;
  targetFile?: string;
  line: number;
  isExternal: boolean;
}

export interface FileNode {
  id: string;
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  language?: string;
  content?: string;
  dependencies?: string[];
  functions?: FunctionInfo[];
  children?: FileNode[];
}

export interface DependencyEdge {
  id: string;
  source: string;
  target: string;
  type: 'import' | 'require' | 'include' | 'reference' | 'function_call';
  label?: string;
  sourceFunction?: string;
  targetFunction?: string;
  line?: number;
}

export interface CodeGraph {
  nodes: FileNode[];
  edges: DependencyEdge[];
}

export interface AnalysisResult {
  repo: GitHubRepo;
  graph: CodeGraph;
  languages: Record<string, number>;
  stats: {
    totalFiles: number;
    totalDirectories: number;
    totalDependencies: number;
    totalFunctions: number;
    totalFunctionCalls: number;
  };
}
