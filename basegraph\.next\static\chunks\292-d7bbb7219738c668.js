(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[292],{171:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("git-fork",[["circle",{cx:"12",cy:"18",r:"3",key:"1mpf1b"}],["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["path",{d:"M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9",key:"1uq4wg"}],["path",{d:"M12 12v3",key:"158kv8"}]])},381:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},435:(t,e,n)=>{"use strict";function r(t,e){if(Object.is(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;if(t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(let[n,r]of t)if(!Object.is(r,e.get(n)))return!1;return!0}if(t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0}let n=Object.keys(t);if(n.length!==Object.keys(e).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(e,r)||!Object.is(t[r],e[r]))return!1;return!0}n.d(e,{x:()=>r})},901:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(8229)._(n(2115)).default.createContext(null)},1193:(t,e)=>{"use strict";function n(t){var e;let{config:n,src:r,width:i,quality:o}=t,a=o||(null==(e=n.qualities)?void 0:e.reduce((t,e)=>Math.abs(e-75)<Math.abs(t-75)?e:t))||75;return n.path+"?url="+encodeURIComponent(r)+"&w="+i+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return r}}),n.__next_img_default=!0;let r=n},1284:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1380:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},1469:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{default:function(){return l},getImageProps:function(){return u}});let r=n(8229),i=n(8883),o=n(3063),a=r._(n(1193));function u(t){let{props:e}=(0,i.getImgProps)(t,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,n]of Object.entries(e))void 0===n&&delete e[t];return{props:e}}let l=o.Image},1539:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1788:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2138:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2436:(t,e,n)=>{"use strict";var r=n(2115),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=r.useState,a=r.useEffect,u=r.useLayoutEffect,l=r.useDebugValue;function s(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!i(t,n)}catch(t){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var n=e(),r=o({inst:{value:n,getSnapshot:e}}),i=r[0].inst,c=r[1];return u(function(){i.value=n,i.getSnapshot=e,s(i)&&c({inst:i})},[t,n,e]),a(function(){return s(i)&&c({inst:i}),t(function(){s(i)&&c({inst:i})})},[t]),l(n),n};e.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},2464:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=n(8229)._(n(2115)).default.createContext({})},2596:(t,e,n)=>{"use strict";function r(){for(var t,e,n=0,r="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=function t(e){var n,r,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(n=0;n<o;n++)e[n]&&(r=t(e[n]))&&(i&&(i+=" "),i+=r)}else for(r in e)e[r]&&(i&&(i+=" "),i+=r);return i}(t))&&(r&&(r+=" "),r+=e);return r}n.d(e,{$:()=>r})},2657:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2775:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("git-branch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]])},3036:(t,e,n)=>{"use strict";n.d(e,{h:()=>f,n:()=>s});var r=n(2115),i=n(5643);let o=t=>{let e,n=new Set,r=(t,r)=>{let i="function"==typeof t?t(e):t;if(!Object.is(i,e)){let t=e;e=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},e,i),n.forEach(n=>n(e,t))}},i=()=>e,o={setState:r,getState:i,getInitialState:()=>a,subscribe:t=>(n.add(t),()=>n.delete(t)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=e=t(r,i,o);return o},{useDebugValue:a}=r,{useSyncExternalStoreWithSelector:u}=i,l=t=>t;function s(t,e=l,n){let r=u(t.subscribe,t.getState,t.getServerState||t.getInitialState,e,n);return a(r),r}let c=(t,e)=>{let n=(t=>t?o(t):o)(t),r=(t,r=e)=>s(n,t,r);return Object.assign(r,n),r},f=(t,e)=>t?c(t,e):c},3052:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3063:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Image",{enumerable:!0,get:function(){return w}});let r=n(8229),i=n(6966),o=n(5155),a=i._(n(2115)),u=r._(n(7650)),l=r._(n(5564)),s=n(8883),c=n(5840),f=n(6752);n(3230);let h=n(901),d=r._(n(1193)),p=n(6654),y={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function m(t,e,n,r,i,o,a){let u=null==t?void 0:t.src;t&&t["data-loaded-src"]!==u&&(t["data-loaded-src"]=u,("decode"in t?t.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(t.parentElement&&t.isConnected){if("empty"!==e&&i(!0),null==n?void 0:n.current){let e=new Event("load");Object.defineProperty(e,"target",{writable:!1,value:t});let r=!1,i=!1;n.current({...e,nativeEvent:e,currentTarget:t,target:t,isDefaultPrevented:()=>r,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{r=!0,e.preventDefault()},stopPropagation:()=>{i=!0,e.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(t)}}))}function g(t){return a.use?{fetchPriority:t}:{fetchpriority:t}}let v=(0,a.forwardRef)((t,e)=>{let{src:n,srcSet:r,sizes:i,height:u,width:l,decoding:s,className:c,style:f,fetchPriority:h,placeholder:d,loading:y,unoptimized:v,fill:x,onLoadRef:w,onLoadingCompleteRef:b,setBlurComplete:_,setShowAltText:M,sizesInput:k,onLoad:E,onError:A,...z}=t,S=(0,a.useCallback)(t=>{t&&(A&&(t.src=t.src),t.complete&&m(t,d,w,b,_,v,k))},[n,d,w,b,_,A,v,k]),P=(0,p.useMergedRef)(e,S);return(0,o.jsx)("img",{...z,...g(h),loading:y,width:l,height:u,decoding:s,"data-nimg":x?"fill":"1",className:c,style:f,sizes:i,srcSet:r,src:n,ref:P,onLoad:t=>{m(t.currentTarget,d,w,b,_,v,k)},onError:t=>{M(!0),"empty"!==d&&_(!0),A&&A(t)}})});function x(t){let{isAppRouter:e,imgAttributes:n}=t,r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...g(n.fetchPriority)};return e&&u.default.preload?(u.default.preload(n.src,r),null):(0,o.jsx)(l.default,{children:(0,o.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}let w=(0,a.forwardRef)((t,e)=>{let n=(0,a.useContext)(h.RouterContext),r=(0,a.useContext)(f.ImageConfigContext),i=(0,a.useMemo)(()=>{var t;let e=y||r||c.imageConfigDefault,n=[...e.deviceSizes,...e.imageSizes].sort((t,e)=>t-e),i=e.deviceSizes.sort((t,e)=>t-e),o=null==(t=e.qualities)?void 0:t.sort((t,e)=>t-e);return{...e,allSizes:n,deviceSizes:i,qualities:o}},[r]),{onLoad:u,onLoadingComplete:l}=t,p=(0,a.useRef)(u);(0,a.useEffect)(()=>{p.current=u},[u]);let m=(0,a.useRef)(l);(0,a.useEffect)(()=>{m.current=l},[l]);let[g,w]=(0,a.useState)(!1),[b,_]=(0,a.useState)(!1),{props:M,meta:k}=(0,s.getImgProps)(t,{defaultLoader:d.default,imgConf:i,blurComplete:g,showAltText:b});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v,{...M,unoptimized:k.unoptimized,placeholder:k.placeholder,fill:k.fill,onLoadRef:p,onLoadingCompleteRef:m,setBlurComplete:w,setShowAltText:_,sizesInput:t.sizes,ref:e}),k.priority?(0,o.jsx)(x,{isAppRouter:!n,imgAttributes:M}):null]})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},3717:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4229:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4395:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},4416:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5029:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return a}});let r=n(2115),i=r.useLayoutEffect,o=r.useEffect;function a(t){let{headManager:e,reduceComponentsToState:n}=t;function a(){if(e&&e.mountedInstances){let i=r.Children.toArray(Array.from(e.mountedInstances).filter(Boolean));e.updateHead(n(i,t))}}return i(()=>{var n;return null==e||null==(n=e.mountedInstances)||n.add(t.children),()=>{var n;null==e||null==(n=e.mountedInstances)||n.delete(t.children)}}),i(()=>(e&&(e._pendingUpdate=a),()=>{e&&(e._pendingUpdate=a)})),o(()=>(e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null),()=>{e&&e._pendingUpdate&&(e._pendingUpdate(),e._pendingUpdate=null)})),null}},5100:(t,e)=>{"use strict";function n(t){let{widthInt:e,heightInt:n,blurWidth:r,blurHeight:i,blurDataURL:o,objectFit:a}=t,u=r?40*r:e,l=i?40*i:n,s=u&&l?"viewBox='0 0 "+u+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},5169:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5196:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5339:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5564:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{default:function(){return y},defaultHead:function(){return f}});let r=n(8229),i=n(6966),o=n(5155),a=i._(n(2115)),u=r._(n(5029)),l=n(2464),s=n(2830),c=n(7544);function f(t){void 0===t&&(t=!1);let e=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return t||e.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),e}function h(t,e){return"string"==typeof e||"number"==typeof e?t:e.type===a.default.Fragment?t.concat(a.default.Children.toArray(e.props.children).reduce((t,e)=>"string"==typeof e||"number"==typeof e?t:t.concat(e),[])):t.concat(e)}n(3230);let d=["name","httpEquiv","charSet","itemProp"];function p(t,e){let{inAmpMode:n}=e;return t.reduce(h,[]).reverse().concat(f(n).reverse()).filter(function(){let t=new Set,e=new Set,n=new Set,r={};return i=>{let o=!0,a=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){a=!0;let e=i.key.slice(i.key.indexOf("$")+1);t.has(e)?o=!1:t.add(e)}switch(i.type){case"title":case"base":e.has(i.type)?o=!1:e.add(i.type);break;case"meta":for(let t=0,e=d.length;t<e;t++){let e=d[t];if(i.props.hasOwnProperty(e))if("charSet"===e)n.has(e)?o=!1:n.add(e);else{let t=i.props[e],n=r[e]||new Set;("name"!==e||!a)&&n.has(t)?o=!1:(n.add(t),r[e]=n)}}}return o}}()).reverse().map((t,e)=>{let n=t.key||e;return a.default.cloneElement(t,{key:n})})}let y=function(t){let{children:e}=t,n=(0,a.useContext)(l.AmpStateContext),r=(0,a.useContext)(s.HeadManagerContext);return(0,o.jsx)(u.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(n),children:e})};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},5643:(t,e,n)=>{"use strict";t.exports=n(6115)},5694:(t,e,n)=>{"use strict";n.d(e,{A:()=>function t(e){if("string"==typeof e||"number"==typeof e)return""+e;let n="";if(Array.isArray(e))for(let r=0,i;r<e.length;r++)""!==(i=t(e[r]))&&(n+=(n&&" ")+i);else for(let t in e)e[t]&&(n+=(n&&" ")+t);return n}})},5840:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return r}});let n=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6115:(t,e,n)=>{"use strict";var r=n(2115),i=n(9033),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},a=i.useSyncExternalStore,u=r.useRef,l=r.useEffect,s=r.useMemo,c=r.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,n,r,i){var f=u(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=a(t,(f=s(function(){function t(t){if(!l){if(l=!0,a=t,t=r(t),void 0!==i&&h.hasValue){var e=h.value;if(i(e,t))return u=e}return u=t}if(e=u,o(a,t))return e;var n=r(t);return void 0!==i&&i(e,n)?(a=t,e):(a=t,u=n)}var a,u,l=!1,s=void 0===n?null:n;return[function(){return t(e())},null===s?void 0:function(){return t(s())}]},[e,n,r,i]))[0],f[1]);return l(function(){h.hasValue=!0,h.value=d},[d]),c(d),d}},6419:()=>{},6474:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6654:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(2115);function i(t,e){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let t=n.current;t&&(n.current=null,t());let e=i.current;e&&(i.current=null,e())}else t&&(n.current=o(t,r)),e&&(i.current=o(e,r))},[t,e])}function o(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let n=t(e);return"function"==typeof n?n:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6752:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ImageConfigContext",{enumerable:!0,get:function(){return o}});let r=n(8229)._(n(2115)),i=n(5840),o=r.default.createContext(i.imageConfigDefault)},6766:(t,e,n)=>{"use strict";n.d(e,{default:()=>i.a});var r=n(1469),i=n.n(r)},7213:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7434:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7544:(t,e)=>{"use strict";function n(t){let{ampFirst:e=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===t?{}:t;return e||n&&r}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isInAmpMode",{enumerable:!0,get:function(){return n}})},8239:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("folder-tree",[["path",{d:"M20 10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-2.5a1 1 0 0 1-.8-.4l-.9-1.2A1 1 0 0 0 15 3h-2a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z",key:"hod4my"}],["path",{d:"M20 21a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-2.9a1 1 0 0 1-.88-.55l-.42-.85a1 1 0 0 0-.92-.6H13a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z",key:"w4yl2u"}],["path",{d:"M3 5a2 2 0 0 0 2 2h3",key:"f2jnh7"}],["path",{d:"M3 3v13a2 2 0 0 0 2 2h3",key:"k8epm1"}]])},8564:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8883:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getImgProps",{enumerable:!0,get:function(){return l}}),n(3230);let r=n(5100),i=n(5840),o=["-moz-initial","fill","none","scale-down",void 0];function a(t){return void 0!==t.default}function u(t){return void 0===t?t:"number"==typeof t?Number.isFinite(t)?t:NaN:"string"==typeof t&&/^[0-9]+$/.test(t)?parseInt(t,10):NaN}function l(t,e){var n,l;let s,c,f,{src:h,sizes:d,unoptimized:p=!1,priority:y=!1,loading:m,className:g,quality:v,width:x,height:w,fill:b=!1,style:_,overrideSrc:M,onLoad:k,onLoadingComplete:E,placeholder:A="empty",blurDataURL:z,fetchPriority:S,decoding:P="async",layout:$,objectFit:N,objectPosition:j,lazyBoundary:O,lazyRoot:C,...I}=t,{imgConf:T,showAltText:H,blurComplete:R,defaultLoader:L}=e,B=T||i.imageConfigDefault;if("allSizes"in B)s=B;else{let t=[...B.deviceSizes,...B.imageSizes].sort((t,e)=>t-e),e=B.deviceSizes.sort((t,e)=>t-e),r=null==(n=B.qualities)?void 0:n.sort((t,e)=>t-e);s={...B,allSizes:t,deviceSizes:e,qualities:r}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let q=I.loader||L;delete I.loader,delete I.srcSet;let D="__next_img_default"in q;if(D){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let t=q;q=e=>{let{config:n,...r}=e;return t(r)}}if($){"fill"===$&&(b=!0);let t={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[$];t&&(_={..._,...t});let e={responsive:"100vw",fill:"100vw"}[$];e&&!d&&(d=e)}let V="",X=u(x),Y=u(w);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let t=a(h)?h.default:h;if(!t.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!t.height||!t.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(t)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=t.blurWidth,f=t.blurHeight,z=z||t.blurDataURL,V=t.src,!b)if(X||Y){if(X&&!Y){let e=X/t.width;Y=Math.round(t.height*e)}else if(!X&&Y){let e=Y/t.height;X=Math.round(t.width*e)}}else X=t.width,Y=t.height}let F=!y&&("lazy"===m||void 0===m);(!(h="string"==typeof h?h:V)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,F=!1),s.unoptimized&&(p=!0),D&&!s.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let U=u(v),W=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:N,objectPosition:j}:{},H?{}:{color:"transparent"},_),Z=R||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:X,heightInt:Y,blurWidth:c,blurHeight:f,blurDataURL:z||"",objectFit:W.objectFit})+'")':'url("'+A+'")',K=o.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,G=Z?{backgroundSize:K,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Z}:{},Q=function(t){let{config:e,src:n,unoptimized:r,width:i,quality:o,sizes:a,loader:u}=t;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:l,kind:s}=function(t,e,n){let{deviceSizes:r,allSizes:i}=t;if(n){let t=/(^|\s)(1?\d?\d)vw/g,e=[];for(let r;r=t.exec(n);)e.push(parseInt(r[2]));if(e.length){let t=.01*Math.min(...e);return{widths:i.filter(e=>e>=r[0]*t),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof e?{widths:r,kind:"w"}:{widths:[...new Set([e,2*e].map(t=>i.find(e=>e>=t)||i[i.length-1]))],kind:"x"}}(e,i,a),c=l.length-1;return{sizes:a||"w"!==s?a:"100vw",srcSet:l.map((t,r)=>u({config:e,src:n,quality:o,width:t})+" "+("w"===s?t:r+1)+s).join(", "),src:u({config:e,src:n,quality:o,width:l[c]})}}({config:s,src:h,unoptimized:p,width:X,quality:U,sizes:d,loader:q});return{props:{...I,loading:F?"lazy":m,fetchPriority:S,width:X,height:Y,decoding:P,className:g,style:{...W,...G},sizes:Q.sizes,srcSet:Q.srcSet,src:M||Q.src},meta:{unoptimized:p,priority:y,placeholder:A,fill:b}}}},9033:(t,e,n)=>{"use strict";t.exports=n(2436)},9099:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9219:(t,e,n)=>{"use strict";n.d(e,{Do:()=>u,WZ:()=>i,TG:()=>l,ny:()=>o,yX:()=>s,xN:()=>c,Qc:()=>a,I$:()=>rl,aQ:()=>rd,di:()=>rp,kO:()=>r_,rN:()=>nY,bi:()=>n8,_s:()=>nz,aE:()=>nt,Hm:()=>n5,tM:()=>eQ,KE:()=>nm,tn:()=>eG,xc:()=>eZ,us:()=>nA,IO:()=>e7,e_:()=>nR,Fp:()=>nq,Mi:()=>nh,HF:()=>e1,Eo:()=>nN,b5:()=>nQ,Tq:()=>ne,qX:()=>nV,q1:()=>nT,oj:()=>nj,aZ:()=>e9,aW:()=>n2,uD:()=>nk,Jo:()=>e4,U$:()=>e8,X6:()=>nd,oN:()=>nK,ah:()=>nF,R4:()=>nb,r8:()=>re,ZO:()=>eK,bK:()=>eJ,b$:()=>e2,uj:()=>nX,v5:()=>nC,Ue:()=>n_,Er:()=>nI,oB:()=>e5,kf:()=>ny,mW:()=>np,Q6:()=>nP,QE:()=>nE,kM:()=>nc,No:()=>rr,Ff:()=>nv,zj:()=>nx,s_:()=>ng,vS:()=>n9,qn:()=>ro,uL:()=>rn,YN:()=>nS});var r,i,o,a,u,l,s,c,f={value:()=>{}};function h(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new d(r)}function d(t){this._=t}function p(t,e,n){for(var r=0,i=t.length;r<i;++r)if(t[r].name===e){t[r]=f,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=n&&t.push({name:e,value:n}),t}function y(){}function m(t){return null==t?y:function(){return this.querySelector(t)}}function g(){return[]}function v(t){return null==t?g:function(){return this.querySelectorAll(t)}}function x(t){return function(){return this.matches(t)}}function w(t){return function(e){return e.matches(t)}}d.prototype=h.prototype={constructor:d,on:function(t,e){var n,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),o=-1,a=i.length;if(arguments.length<2){for(;++o<a;)if((n=(t=i[o]).type)&&(n=function(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}(r[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++o<a;)if(n=(t=i[o]).type)r[n]=p(r[n],t.name,e);else if(null==e)for(n in r)r[n]=p(r[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new d(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,n=r.length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};var b=Array.prototype.find;function _(){return this.firstElementChild}var M=Array.prototype.filter;function k(){return Array.from(this.children)}function E(t){return Array(t.length)}function A(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function z(t,e,n,r,i,o){for(var a,u=0,l=e.length,s=o.length;u<s;++u)(a=e[u])?(a.__data__=o[u],r[u]=a):n[u]=new A(t,o[u]);for(;u<l;++u)(a=e[u])&&(i[u]=a)}function S(t,e,n,r,i,o,a){var u,l,s,c=new Map,f=e.length,h=o.length,d=Array(f);for(u=0;u<f;++u)(l=e[u])&&(d[u]=s=a.call(l,l.__data__,u,e)+"",c.has(s)?i[u]=l:c.set(s,l));for(u=0;u<h;++u)s=a.call(t,o[u],u,o)+"",(l=c.get(s))?(r[u]=l,l.__data__=o[u],c.delete(s)):n[u]=new A(t,o[u]);for(u=0;u<f;++u)(l=e[u])&&c.get(d[u])===l&&(i[u]=l)}function P(t){return t.__data__}function $(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}A.prototype={constructor:A,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var N="http://www.w3.org/1999/xhtml";let j={svg:"http://www.w3.org/2000/svg",xhtml:N,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function O(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),j.hasOwnProperty(e)?{space:j[e],local:t}:t}function C(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function I(t,e){return t.style.getPropertyValue(e)||C(t).getComputedStyle(t,null).getPropertyValue(e)}function T(t){return t.trim().split(/^|\s+/)}function H(t){return t.classList||new R(t)}function R(t){this._node=t,this._names=T(t.getAttribute("class")||"")}function L(t,e){for(var n=H(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function B(t,e){for(var n=H(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function q(){this.textContent=""}function D(){this.innerHTML=""}function V(){this.nextSibling&&this.parentNode.appendChild(this)}function X(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Y(t){var e=O(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===N&&e.documentElement.namespaceURI===N?e.createElement(t):e.createElementNS(n,t)}})(e)}function F(){return null}function U(){var t=this.parentNode;t&&t.removeChild(this)}function W(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Z(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function K(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)(n=e[r],t.type&&n.type!==t.type||n.name!==t.name)?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function G(t,e,n){return function(){var r,i=this.__on,o=function(t){e.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),r.value=e;return}}this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}function Q(t,e,n){var r=C(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}R.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var J=[null];function tt(t,e){this._groups=t,this._parents=e}function te(){return new tt([[document.documentElement]],J)}function tn(t){return"string"==typeof t?new tt([[document.querySelector(t)]],[document.documentElement]):new tt([[t]],J)}function tr(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}tt.prototype=te.prototype={constructor:tt,select:function(t){"function"!=typeof t&&(t=m(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a,u=e[i],l=u.length,s=r[i]=Array(l),c=0;c<l;++c)(o=u[c])&&(a=t.call(o,o.__data__,c,u))&&("__data__"in o&&(a.__data__=o.__data__),s[c]=a);return new tt(r,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=v(t);for(var n=this._groups,r=n.length,i=[],o=[],a=0;a<r;++a)for(var u,l=n[a],s=l.length,c=0;c<s;++c)(u=l[c])&&(i.push(t.call(u,u.__data__,c,l)),o.push(u));return new tt(i,o)},selectChild:function(t){var e;return this.select(null==t?_:(e="function"==typeof t?t:w(t),function(){return b.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?k:(e="function"==typeof t?t:w(t),function(){return M.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=x(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],u=a.length,l=r[i]=[],s=0;s<u;++s)(o=a[s])&&t.call(o,o.__data__,s,a)&&l.push(o);return new tt(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,P);var n=e?S:z,r=this._parents,i=this._groups;"function"!=typeof t&&(v=t,t=function(){return v});for(var o=i.length,a=Array(o),u=Array(o),l=Array(o),s=0;s<o;++s){var c=r[s],f=i[s],h=f.length,d="object"==typeof(g=t.call(c,c&&c.__data__,s,r))&&"length"in g?g:Array.from(g),p=d.length,y=u[s]=Array(p),m=a[s]=Array(p);n(c,f,y,m,l[s]=Array(h),d,e);for(var g,v,x,w,b=0,_=0;b<p;++b)if(x=y[b]){for(b>=_&&(_=b+1);!(w=m[_])&&++_<p;);x._next=w||null}}return(a=new tt(a,r))._enter=u,a._exit=l,a},enter:function(){return new tt(this._enter||this._groups.map(E),this._parents)},exit:function(){return new tt(this._exit||this._groups.map(E),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,a=Math.min(i,o),u=Array(i),l=0;l<a;++l)for(var s,c=n[l],f=r[l],h=c.length,d=u[l]=Array(h),p=0;p<h;++p)(s=c[p]||f[p])&&(d[p]=s);for(;l<i;++l)u[l]=n[l];return new tt(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=$);for(var n=this._groups,r=n.length,i=Array(r),o=0;o<r;++o){for(var a,u=n[o],l=u.length,s=i[o]=Array(l),c=0;c<l;++c)(a=u[c])&&(s[c]=a);s.sort(e)}return new tt(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,e){var n=O(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:function(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):I(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=T(t+"");if(arguments.length<2){for(var r=H(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?L:B)(this,t)}}:e?function(t){return function(){L(this,t)}}:function(t){return function(){B(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?q:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?D:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(V)},lower:function(){return this.each(X)},append:function(t){var e="function"==typeof t?t:Y(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:Y(t),r=null==e?F:"function"==typeof e?e:m(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(U)},clone:function(t){return this.select(t?Z:W)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var l,s=0,c=u.length;s<c;++s)for(r=0,l=u[s];r<a;++r)if((i=o[r]).type===l.type&&i.name===l.name)return l.value}return}for(r=0,u=e?G:K;r<a;++r)this.each(u(o[r],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return Q(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return Q(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let ti={passive:!1},to={capture:!0,passive:!1};function ta(t){t.stopImmediatePropagation()}function tu(t){t.preventDefault(),t.stopImmediatePropagation()}function tl(t){var e=t.document.documentElement,n=tn(t).on("dragstart.drag",tu,to);"onselectstart"in e?n.on("selectstart.drag",tu,to):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function ts(t,e){var n=t.document.documentElement,r=tn(t).on("dragstart.drag",null);e&&(r.on("click.drag",tu,to),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}let tc=t=>()=>t;function tf(t,{sourceEvent:e,subject:n,target:r,identifier:i,active:o,x:a,y:u,dx:l,dy:s,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:s,enumerable:!0,configurable:!0},_:{value:c}})}function th(t){return!t.ctrlKey&&!t.button}function td(){return this.parentNode}function tp(t,e){return null==e?{x:t.x,y:t.y}:e}function ty(){return navigator.maxTouchPoints||"ontouchstart"in this}function tm(t){return((t=Math.exp(t))+1/t)/2}tf.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};let tg=function t(e,n,r){function i(t,i){var o,a,u=t[0],l=t[1],s=t[2],c=i[0],f=i[1],h=i[2],d=c-u,p=f-l,y=d*d+p*p;if(y<1e-12)a=Math.log(h/s)/e,o=function(t){return[u+t*d,l+t*p,s*Math.exp(e*t*a)]};else{var m=Math.sqrt(y),g=(h*h-s*s+r*y)/(2*s*n*m),v=(h*h-s*s-r*y)/(2*h*n*m),x=Math.log(Math.sqrt(g*g+1)-g);a=(Math.log(Math.sqrt(v*v+1)-v)-x)/e,o=function(t){var r,i,o=t*a,c=tm(x),f=s/(n*m)*(c*(((r=Math.exp(2*(r=e*o+x)))-1)/(r+1))-((i=Math.exp(i=x))-1/i)/2);return[u+f*d,l+f*p,s*c/tm(e*o+x)]}}return o.duration=1e3*a*e/Math.SQRT2,o}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4);var tv,tx,tw=0,tb=0,t_=0,tM=0,tk=0,tE=0,tA="object"==typeof performance&&performance.now?performance:Date,tz="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function tS(){return tk||(tz(tP),tk=tA.now()+tE)}function tP(){tk=0}function t$(){this._call=this._time=this._next=null}function tN(t,e,n){var r=new t$;return r.restart(t,e,n),r}function tj(){tk=(tM=tA.now())+tE,tw=tb=0;try{tS(),++tw;for(var t,e=tv;e;)(t=tk-e._time)>=0&&e._call.call(void 0,t),e=e._next;--tw}finally{tw=0,function(){for(var t,e,n=tv,r=1/0;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:tv=e);tx=t,tC(r)}(),tk=0}}function tO(){var t=tA.now(),e=t-tM;e>1e3&&(tE-=e,tM=t)}function tC(t){!tw&&(tb&&(tb=clearTimeout(tb)),t-tk>24?(t<1/0&&(tb=setTimeout(tj,t-tA.now()-tE)),t_&&(t_=clearInterval(t_))):(t_||(tM=tA.now(),t_=setInterval(tO,1e3)),tw=1,tz(tj)))}function tI(t,e,n){var r=new t$;return e=null==e?0:+e,r.restart(n=>{r.stop(),t(n+e)},e,n),r}t$.prototype=tN.prototype={constructor:t$,restart:function(t,e,n){if("function"!=typeof t)throw TypeError("callback is not a function");n=(null==n?tS():+n)+(null==e?0:+e),this._next||tx===this||(tx?tx._next=this:tv=this,tx=this),this._call=t,this._time=n,tC()},stop:function(){this._call&&(this._call=null,this._time=1/0,tC())}};var tT=h("start","end","cancel","interrupt"),tH=[];function tR(t,e,n,r,i,o){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(l){var s,c,f,h;if(1!==n.state)return u();for(s in i)if((h=i[s]).name===n.name){if(3===h.state)return tI(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[s]):+s<e&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[s])}if(tI(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(l))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(s=0,n.state=3,r=Array(f=n.tween.length),c=-1;s<f;++s)(h=n.tween[s].value.call(t,t.__data__,n.index,n.group))&&(r[++c]=h);r.length=c+1}}function a(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(u),n.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=tN(function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)},0,n.time)}(t,n,{name:e,index:r,group:i,on:tT,tween:tH,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function tL(t,e){var n=tq(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function tB(t,e){var n=tq(t,e);if(n.state>3)throw Error("too late; already running");return n}function tq(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function tD(t,e){var n,r,i,o=t.__transition,a=!0;if(o){for(i in e=null==e?null:e+"",o){if((n=o[i]).name!==e){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]}a&&delete t.__transition}}function tV(t,e){return t*=1,e*=1,function(n){return t*(1-n)+e*n}}var tX=180/Math.PI,tY={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function tF(t,e,n,r,i,o){var a,u,l;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(l=t*n+e*r)&&(n-=t*l,r-=e*l),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,l/=u),t*r<e*n&&(t=-t,e=-e,l=-l,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*tX,skewX:Math.atan(l)*tX,scaleX:a,scaleY:u}}function tU(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,l,s,c,f=[],h=[];return o=t(o),a=t(a),!function(t,r,i,o,a,u){if(t!==i||r!==o){var l=a.push("translate(",null,e,null,n);u.push({i:l-4,x:tV(t,i)},{i:l-2,x:tV(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,f,h),u=o.rotate,l=a.rotate,u!==l?(u-l>180?l+=360:l-u>180&&(u+=360),h.push({i:f.push(i(f)+"rotate(",null,r)-2,x:tV(u,l)})):l&&f.push(i(f)+"rotate("+l+r),s=o.skewX,c=a.skewX,s!==c?h.push({i:f.push(i(f)+"skewX(",null,r)-2,x:tV(s,c)}):c&&f.push(i(f)+"skewX("+c+r),!function(t,e,n,r,o,a){if(t!==n||e!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:tV(t,n)},{i:u-2,x:tV(e,r)})}else(1!==n||1!==r)&&o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,f,h),o=a=null,function(t){for(var e,n=-1,r=h.length;++n<r;)f[(e=h[n]).i]=e.x(t);return f.join("")}}}var tW=tU(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?tY:tF(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),tZ=tU(function(t){return null==t?tY:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?tF((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):tY},", ",")",")");function tK(t,e,n){var r=t._id;return t.each(function(){var t=tB(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return tq(t,r).value[e]}}function tG(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function tQ(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function tJ(){}var t0="\\s*([+-]?\\d+)\\s*",t1="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",t2="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",t5=/^#([0-9a-f]{3,8})$/,t3=RegExp(`^rgb\\(${t0},${t0},${t0}\\)$`),t6=RegExp(`^rgb\\(${t2},${t2},${t2}\\)$`),t4=RegExp(`^rgba\\(${t0},${t0},${t0},${t1}\\)$`),t9=RegExp(`^rgba\\(${t2},${t2},${t2},${t1}\\)$`),t8=RegExp(`^hsl\\(${t1},${t2},${t2}\\)$`),t7=RegExp(`^hsla\\(${t1},${t2},${t2},${t1}\\)$`),et={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function ee(){return this.rgb().formatHex()}function en(){return this.rgb().formatRgb()}function er(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=t5.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?ei(e):3===n?new eu(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?eo(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?eo(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=t3.exec(t))?new eu(e[1],e[2],e[3],1):(e=t6.exec(t))?new eu(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=t4.exec(t))?eo(e[1],e[2],e[3],e[4]):(e=t9.exec(t))?eo(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=t8.exec(t))?ed(e[1],e[2]/100,e[3]/100,1):(e=t7.exec(t))?ed(e[1],e[2]/100,e[3]/100,e[4]):et.hasOwnProperty(t)?ei(et[t]):"transparent"===t?new eu(NaN,NaN,NaN,0):null}function ei(t){return new eu(t>>16&255,t>>8&255,255&t,1)}function eo(t,e,n,r){return r<=0&&(t=e=n=NaN),new eu(t,e,n,r)}function ea(t,e,n,r){var i;return 1==arguments.length?((i=t)instanceof tJ||(i=er(i)),i)?new eu((i=i.rgb()).r,i.g,i.b,i.opacity):new eu:new eu(t,e,n,null==r?1:r)}function eu(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function el(){return`#${eh(this.r)}${eh(this.g)}${eh(this.b)}`}function es(){let t=ec(this.opacity);return`${1===t?"rgb(":"rgba("}${ef(this.r)}, ${ef(this.g)}, ${ef(this.b)}${1===t?")":`, ${t})`}`}function ec(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ef(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function eh(t){return((t=ef(t))<16?"0":"")+t.toString(16)}function ed(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new ey(t,e,n,r)}function ep(t){if(t instanceof ey)return new ey(t.h,t.s,t.l,t.opacity);if(t instanceof tJ||(t=er(t)),!t)return new ey;if(t instanceof ey)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),a=NaN,u=o-i,l=(o+i)/2;return u?(a=e===o?(n-r)/u+(n<r)*6:n===o?(r-e)/u+2:(e-n)/u+4,u/=l<.5?o+i:2-o-i,a*=60):u=l>0&&l<1?0:a,new ey(a,u,l,t.opacity)}function ey(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function em(t){return(t=(t||0)%360)<0?t+360:t}function eg(t){return Math.max(0,Math.min(1,t||0))}function ev(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}function ex(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}tG(tJ,er,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ee,formatHex:ee,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ep(this).formatHsl()},formatRgb:en,toString:en}),tG(eu,ea,tQ(tJ,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new eu(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new eu(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new eu(ef(this.r),ef(this.g),ef(this.b),ec(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:el,formatHex:el,formatHex8:function(){return`#${eh(this.r)}${eh(this.g)}${eh(this.b)}${eh((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:es,toString:es})),tG(ey,function(t,e,n,r){return 1==arguments.length?ep(t):new ey(t,e,n,null==r?1:r)},tQ(tJ,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new ey(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new ey(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new eu(ev(t>=240?t-240:t+120,i,r),ev(t,i,r),ev(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new ey(em(this.h),eg(this.s),eg(this.l),ec(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=ec(this.opacity);return`${1===t?"hsl(":"hsla("}${em(this.h)}, ${100*eg(this.s)}%, ${100*eg(this.l)}%${1===t?")":`, ${t})`}`}}));let ew=t=>()=>t;function eb(t,e){var n=e-t;return n?function(e){return t+e*n}:ew(isNaN(t)?e:t)}let e_=function t(e){var n,r=1==(n=+e)?eb:function(t,e){var r,i,o;return e-t?(r=t,i=e,r=Math.pow(r,o=n),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):ew(isNaN(t)?e:t)};function i(t,e){var n=r((t=ea(t)).r,(e=ea(e)).r),i=r(t.g,e.g),o=r(t.b,e.b),a=eb(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function eM(t){return function(e){var n,r,i=e.length,o=Array(i),a=Array(i),u=Array(i);for(n=0;n<i;++n)r=ea(e[n]),o[n]=r.r||0,a[n]=r.g||0,u[n]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}eM(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<e-1?t[r+2]:2*o-i;return ex((n-r/e)*e,a,i,o,u)}}),eM(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],o=t[r%e],a=t[(r+1)%e],u=t[(r+2)%e];return ex((n-r/e)*e,i,o,a,u)}});var ek=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eE=RegExp(ek.source,"g");function eA(t,e){var n,r,i,o,a,u=ek.lastIndex=eE.lastIndex=0,l=-1,s=[],c=[];for(t+="",e+="";(i=ek.exec(t))&&(o=eE.exec(e));)(a=o.index)>u&&(a=e.slice(u,a),s[l]?s[l]+=a:s[++l]=a),(i=i[0])===(o=o[0])?s[l]?s[l]+=o:s[++l]=o:(s[++l]=null,c.push({i:l,x:tV(i,o)})),u=eE.lastIndex;return u<e.length&&(a=e.slice(u),s[l]?s[l]+=a:s[++l]=a),s.length<2?c[0]?(n=c[0].x,function(t){return n(t)+""}):(r=e,function(){return r}):(e=c.length,function(t){for(var n,r=0;r<e;++r)s[(n=c[r]).i]=n.x(t);return s.join("")})}function ez(t,e){var n;return("number"==typeof e?tV:e instanceof er?e_:(n=er(e))?(e=n,e_):eA)(t,e)}var eS=te.prototype.constructor;function eP(t){return function(){this.style.removeProperty(t)}}var e$=0;function eN(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}var ej=te.prototype;eN.prototype=(function(t){return te().transition(t)}).prototype={constructor:eN,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=m(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,l,s=r[a],c=s.length,f=o[a]=Array(c),h=0;h<c;++h)(u=s[h])&&(l=t.call(u,u.__data__,h,s))&&("__data__"in u&&(l.__data__=u.__data__),f[h]=l,tR(f[h],e,n,h,f,tq(u,n)));return new eN(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=v(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var l,s=r[u],c=s.length,f=0;f<c;++f)if(l=s[f]){for(var h,d=t.call(l,l.__data__,f,s),p=tq(l,n),y=0,m=d.length;y<m;++y)(h=d[y])&&tR(h,e,n,y,d,p);o.push(d),a.push(l)}return new eN(o,a,e,n)},selectChild:ej.selectChild,selectChildren:ej.selectChildren,filter:function(t){"function"!=typeof t&&(t=x(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],u=a.length,l=r[i]=[],s=0;s<u;++s)(o=a[s])&&t.call(o,o.__data__,s,a)&&l.push(o);return new eN(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var l,s=e[u],c=n[u],f=s.length,h=a[u]=Array(f),d=0;d<f;++d)(l=s[d]||c[d])&&(h[d]=l);for(;u<r;++u)a[u]=e[u];return new eN(a,this._parents,this._name,this._id)},selection:function(){return new eS(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++e$,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],l=u.length,s=0;s<l;++s)if(a=u[s]){var c=tq(a,e);tR(a,t,n,s,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new eN(r,this._parents,t,n)},call:ej.call,nodes:ej.nodes,node:ej.node,size:ej.size,empty:ej.empty,each:ej.each,on:function(t,e){var n,r,i,o,a,u,l=this._id;return arguments.length<2?tq(this.node(),l).on.on(t):this.each((n=l,r=t,i=e,u=(r+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?tL:tB,function(){var t=u(this,n),e=t.on;e!==o&&(a=(o=e).copy()).on(r,i),t.on=a}))},attr:function(t,e){var n=O(t),r="transform"===n?tZ:ez;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var r,i,o;return function(){var a,u,l=n(this);return null==l?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=l+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,l))}}:function(t,e,n){var r,i,o;return function(){var a,u,l=n(this);return null==l?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=l+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,l))}})(n,r,tK(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=e(r=a,n)}}:function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=e(r=a,n)}})(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var r=O(t);return this.tween(n,(r.local?function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttributeNS(t.space,t.local,i.call(this,e))}),n}return i._value=e,i}:function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttribute(t,i.call(this,e))}),n}return i._value=e,i})(r,e))},style:function(t,e,n){var r,i,o,a,u,l,s,c,f,h,d,p,y,m,g,v,x,w,b,_,M,k="transform"==(t+="")?tW:ez;return null==e?this.styleTween(t,(r=t,function(){var t=I(this,r),e=(this.style.removeProperty(r),I(this,r));return t===e?null:t===i&&e===o?a:a=k(i=t,o=e)})).on("end.style."+t,eP(t)):"function"==typeof e?this.styleTween(t,(u=t,l=tK(this,"style."+t,e),function(){var t=I(this,u),e=l(this),n=e+"";return null==e&&(this.style.removeProperty(u),n=e=I(this,u)),t===n?null:t===s&&n===c?f:(c=n,f=k(s=t,e))})).each((h=this._id,x="end."+(v="style."+(d=t)),function(){var t=tB(this,h),e=t.on,n=null==t.value[v]?g||(g=eP(d)):void 0;(e!==p||m!==n)&&(y=(p=e).copy()).on(x,m=n),t.on=y})):this.styleTween(t,(w=t,M=e+"",function(){var t=I(this,w);return t===M?null:t===b?_:_=k(b=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw Error();return this.tween(r,function(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&function(e){this.style.setProperty(t,o.call(this,e),n)}),r}return o._value=e,o}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=tK(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){this.textContent=r.call(this,t)}),e}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=tq(this.node(),n).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?function(t,e){var n,r;return function(){var i=tB(this,t),o=i.tween;if(o!==n){r=n=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,e,n){var r,i;if("function"!=typeof n)throw Error();return function(){var o=tB(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:e,value:n},l=0,s=i.length;l<s;++l)if(i[l].name===e){i[l]=u;break}l===s&&i.push(u)}o.tween=i}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){tL(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){tL(this,t).delay=e}})(e,t)):tq(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){tB(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){tB(this,t).duration=e}})(e,t)):tq(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){tB(this,t).ease=e}}(e,t)):tq(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();tB(this,e).ease=n}))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,a){var u={value:a},l={value:function(){0==--i&&o()}};n.each(function(){var n=tB(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(u),e._.interrupt.push(u),e._.end.push(l)),n.on=e}),0===i&&o()})},[Symbol.iterator]:ej[Symbol.iterator]};var eO={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};te.prototype.interrupt=function(t){return this.each(function(){tD(this,t)})},te.prototype.transition=function(t){var e,n;t instanceof eN?(e=t._id,t=t._name):(e=++e$,(n=eO).time=tS(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],l=u.length,s=0;s<l;++s)(a=u[s])&&tR(a,t,e,s,u,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(a,e));return new eN(r,this._parents,t,e)};let eC=t=>()=>t;function eI(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function eT(t,e,n){this.k=t,this.x=e,this.y=n}eT.prototype={constructor:eT,scale:function(t){return 1===t?this:new eT(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new eT(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var eH=new eT(1,0,0);function eR(t){for(;!t.__zoom;)if(!(t=t.parentNode))return eH;return t.__zoom}function eL(t){t.stopImmediatePropagation()}function eB(t){t.preventDefault(),t.stopImmediatePropagation()}function eq(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function eD(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function eV(){return this.__zoom||eH}function eX(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function eY(){return navigator.maxTouchPoints||"ontouchstart"in this}function eF(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function eU(){var t,e,n,r=eq,i=eD,o=eF,a=eX,u=eY,l=[0,1/0],s=[[-1/0,-1/0],[1/0,1/0]],c=250,f=tg,d=h("start","zoom","end"),p=0,y=10;function m(t){t.property("__zoom",eV).on("wheel.zoom",M,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",E).filter(u).on("touchstart.zoom",A).on("touchmove.zoom",z).on("touchend.zoom touchcancel.zoom",S).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(t,e){return(e=Math.max(l[0],Math.min(l[1],e)))===t.k?t:new eT(e,t.x,t.y)}function v(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new eT(t.k,r,i)}function x(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function w(t,e,n,r){t.on("start.zoom",function(){b(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=b(this,t).event(r),a=i.apply(this,t),u=null==n?x(a):"function"==typeof n?n.apply(this,t):n,l=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),s=this.__zoom,c="function"==typeof e?e.apply(this,t):e,h=f(s.invert(u).concat(l/s.k),c.invert(u).concat(l/c.k));return function(t){if(1===t)t=c;else{var e=h(t),n=l/e[2];t=new eT(n,u[0]-e[0]*n,u[1]-e[1]*n)}o.zoom(null,t)}})}function b(t,e,n){return!n&&t.__zooming||new _(t,e)}function _(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,e),this.taps=0}function M(t,...e){if(r.apply(this,arguments)){var n=b(this,e).event(t),i=this.__zoom,u=Math.max(l[0],Math.min(l[1],i.k*Math.pow(2,a.apply(this,arguments)))),c=tr(t);if(n.wheel)(n.mouse[0][0]!==c[0]||n.mouse[0][1]!==c[1])&&(n.mouse[1]=i.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(i.k===u)return;n.mouse=[c,i.invert(c)],tD(this),n.start()}eB(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",o(v(g(i,u),n.mouse[0],n.mouse[1]),n.extent,s))}}function k(t,...e){if(!n&&r.apply(this,arguments)){var i=t.currentTarget,a=b(this,e,!0).event(t),u=tn(t.view).on("mousemove.zoom",function(t){if(eB(t),!a.moved){var e=t.clientX-c,n=t.clientY-f;a.moved=e*e+n*n>p}a.event(t).zoom("mouse",o(v(a.that.__zoom,a.mouse[0]=tr(t,i),a.mouse[1]),a.extent,s))},!0).on("mouseup.zoom",function(t){u.on("mousemove.zoom mouseup.zoom",null),ts(t.view,a.moved),eB(t),a.event(t).end()},!0),l=tr(t,i),c=t.clientX,f=t.clientY;tl(t.view),eL(t),a.mouse=[l,this.__zoom.invert(l)],tD(this),a.start()}}function E(t,...e){if(r.apply(this,arguments)){var n=this.__zoom,a=tr(t.changedTouches?t.changedTouches[0]:t,this),u=n.invert(a),l=n.k*(t.shiftKey?.5:2),f=o(v(g(n,l),a,u),i.apply(this,e),s);eB(t),c>0?tn(this).transition().duration(c).call(w,f,a,t):tn(this).call(m.transform,f,a,t)}}function A(n,...i){if(r.apply(this,arguments)){var o,a,u,l,s=n.touches,c=s.length,f=b(this,i,n.changedTouches.length===c).event(n);for(eL(n),a=0;a<c;++a)l=[l=tr(u=s[a],this),this.__zoom.invert(l),u.identifier],f.touch0?f.touch1||f.touch0[2]===l[2]||(f.touch1=l,f.taps=0):(f.touch0=l,o=!0,f.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(f.taps<2&&(e=l[0],t=setTimeout(function(){t=null},500)),tD(this),f.start())}}function z(t,...e){if(this.__zooming){var n,r,i,a,u=b(this,e).event(t),l=t.changedTouches,c=l.length;for(eB(t),n=0;n<c;++n)i=tr(r=l[n],this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var f=u.touch0[0],h=u.touch0[1],d=u.touch1[0],p=u.touch1[1],y=(y=d[0]-f[0])*y+(y=d[1]-f[1])*y,m=(m=p[0]-h[0])*m+(m=p[1]-h[1])*m;r=g(r,Math.sqrt(y/m)),i=[(f[0]+d[0])/2,(f[1]+d[1])/2],a=[(h[0]+p[0])/2,(h[1]+p[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],a=u.touch0[1]}u.zoom("touch",o(v(r,i,a),u.extent,s))}}function S(t,...r){if(this.__zooming){var i,o,a=b(this,r).event(t),u=t.changedTouches,l=u.length;for(eL(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),i=0;i<l;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=tr(o,this),Math.hypot(e[0]-o[0],e[1]-o[1])<y)){var s=tn(this).on("dblclick.zoom");s&&s.apply(this,arguments)}}}return m.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",eV),t!==i?w(t,e,n,r):i.interrupt().each(function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},m.scaleBy=function(t,e,n,r){m.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,r)},m.scaleTo=function(t,e,n,r){m.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,a=null==n?x(t):"function"==typeof n?n.apply(this,arguments):n,u=r.invert(a),l="function"==typeof e?e.apply(this,arguments):e;return o(v(g(r,l),a,u),t,s)},n,r)},m.translateBy=function(t,e,n,r){m.transform(t,function(){return o(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),i.apply(this,arguments),s)},null,r)},m.translateTo=function(t,e,n,r,a){m.transform(t,function(){var t=i.apply(this,arguments),a=this.__zoom,u=null==r?x(t):"function"==typeof r?r.apply(this,arguments):r;return o(eH.translate(u[0],u[1]).scale(a.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,s)},r,a)},_.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=tn(this.that).datum();d.call(t,this.that,new eI(t,{sourceEvent:this.sourceEvent,target:m,type:t,transform:this.that.__zoom,dispatch:d}),e)}},m.wheelDelta=function(t){return arguments.length?(a="function"==typeof t?t:eC(+t),m):a},m.filter=function(t){return arguments.length?(r="function"==typeof t?t:eC(!!t),m):r},m.touchable=function(t){return arguments.length?(u="function"==typeof t?t:eC(!!t),m):u},m.extent=function(t){return arguments.length?(i="function"==typeof t?t:eC([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),m):i},m.scaleExtent=function(t){return arguments.length?(l[0]=+t[0],l[1]=+t[1],m):[l[0],l[1]]},m.translateExtent=function(t){return arguments.length?(s[0][0]=+t[0][0],s[1][0]=+t[1][0],s[0][1]=+t[0][1],s[1][1]=+t[1][1],m):[[s[0][0],s[0][1]],[s[1][0],s[1][1]]]},m.constrain=function(t){return arguments.length?(o=t,m):o},m.duration=function(t){return arguments.length?(c=+t,m):c},m.interpolate=function(t){return arguments.length?(f=t,m):f},m.on=function(){var t=d.on.apply(d,arguments);return t===d?m:t},m.clickDistance=function(t){return arguments.length?(p=(t*=1)*t,m):Math.sqrt(p)},m.tapDistance=function(t){return arguments.length?(y=+t,m):y},m}function eW(t,e){var n,r,i=typeof e;return null==e||"boolean"===i?ew(e):("number"===i?tV:"string"===i?(r=er(e))?(e=r,e_):eA:e instanceof er?e_:e instanceof Date?function(t,e){var n=new Date;return t*=1,e*=1,function(r){return n.setTime(t*(1-r)+e*r),n}}:!ArrayBuffer.isView(n=e)||n instanceof DataView?Array.isArray(e)?function(t,e){var n,r=e?e.length:0,i=t?Math.min(r,t.length):0,o=Array(i),a=Array(r);for(n=0;n<i;++n)o[n]=eW(t[n],e[n]);for(;n<r;++n)a[n]=e[n];return function(t){for(n=0;n<i;++n)a[n]=o[n](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var n,r={},i={};for(n in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)n in t?r[n]=eW(t[n],e[n]):i[n]=e[n];return function(t){for(n in r)i[n]=r[n](t);return i}}:tV:function(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(n=0;n<r;++n)i[n]=t[n]*(1-o)+e[n]*o;return i}})(t,e)}eR.prototype=eT.prototype;let eZ={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:t=>`Node type "${t}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:t=>`The old edge with id=${t} does not exist.`,error009:t=>`Marker type "${t}" doesn't exist.`,error008:(t,{id:e,sourceHandle:n,targetHandle:r})=>`Couldn't create edge for ${t} handle id: "${"source"===t?n:r}", edge id: ${e}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:t=>`Edge type "${t}" not found. Using fallback type "default".`,error012:t=>`Node with id "${t}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(t="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${t}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},eK=[[-1/0,-1/0],[1/0,1/0]],eG=["Enter"," ","Escape"],eQ={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:t,x:e,y:n})=>`Moved selected node ${t}. New position, x: ${e}, y: ${n}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};!function(t){t.Strict="strict",t.Loose="loose"}(i||(i={})),function(t){t.Free="free",t.Vertical="vertical",t.Horizontal="horizontal"}(o||(o={})),function(t){t.Partial="partial",t.Full="full"}(a||(a={}));let eJ={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};!function(t){t.Bezier="default",t.Straight="straight",t.Step="step",t.SmoothStep="smoothstep",t.SimpleBezier="simplebezier"}(u||(u={})),function(t){t.Arrow="arrow",t.ArrowClosed="arrowclosed"}(l||(l={})),function(t){t.Left="left",t.Top="top",t.Right="right",t.Bottom="bottom"}(s||(s={}));let e0={[s.Left]:s.Right,[s.Right]:s.Left,[s.Top]:s.Bottom,[s.Bottom]:s.Top};function e1(t){return null===t?null:t?"valid":"invalid"}let e2=t=>"id"in t&&"source"in t&&"target"in t,e5=t=>"id"in t&&"position"in t&&!("source"in t)&&!("target"in t),e3=t=>"id"in t&&"internals"in t&&!("source"in t)&&!("target"in t),e6=(t,e=[0,0])=>{let{width:n,height:r}=nk(t),i=t.origin??e,o=n*i[0],a=r*i[1];return{x:t.position.x-o,y:t.position.y-a}},e4=(t,e={nodeOrigin:[0,0]})=>0===t.length?{x:0,y:0,width:0,height:0}:ns(t.reduce((t,n)=>{let r="string"==typeof n,i=e.nodeLookup||r?void 0:n;return e.nodeLookup&&(i=r?e.nodeLookup.get(n):e3(n)?n:e.nodeLookup.get(n.id)),nu(t,i?nf(i,e.nodeOrigin):{x:0,y:0,x2:0,y2:0})},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),e9=(t,e={})=>{if(0===t.size)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return t.forEach(t=>{if(void 0===e.filter||e.filter(t)){let e=nf(t);n=nu(n,e)}}),ns(n)},e8=(t,e,[n,r,i]=[0,0,1],o=!1,a=!1)=>{let u={...nv(e,[n,r,i]),width:e.width/i,height:e.height/i},l=[];for(let e of t.values()){let{measured:t,selectable:n=!0,hidden:r=!1}=e;if(a&&!n||r)continue;let i=t.width??e.width??e.initialWidth??null,s=t.height??e.height??e.initialHeight??null,c=nd(u,nc(e)),f=(i??0)*(s??0),h=o&&c>0;(!e.internals.handleBounds||h||c>=f||e.dragging)&&l.push(e)}return l};async function e7({nodes:t,width:e,height:n,panZoom:r,minZoom:i,maxZoom:o},a){if(0===t.size)return Promise.resolve(!0);let u=nb(e9(function(t,e){let n=new Map,r=e?.nodes?new Set(e.nodes.map(t=>t.id)):null;return t.forEach(t=>{t.measured.width&&t.measured.height&&(e?.includeHiddenNodes||!t.hidden)&&(!r||r.has(t.id))&&n.set(t.id,t)}),n}(t,a)),e,n,a?.minZoom??i,a?.maxZoom??o,a?.padding??.1);return await r.setViewport(u,{duration:a?.duration,ease:a?.ease,interpolate:a?.interpolate}),Promise.resolve(!0)}function nt({nodeId:t,nextPosition:e,nodeLookup:n,nodeOrigin:r=[0,0],nodeExtent:i,onError:o}){let a=n.get(t),u=a.parentId?n.get(a.parentId):void 0,{x:l,y:s}=u?u.internals.positionAbsolute:{x:0,y:0},c=a.origin??r,f=a.extent||i;if("parent"!==a.extent||a.expandParent)u&&nM(a.extent)&&(f=[[a.extent[0][0]+l,a.extent[0][1]+s],[a.extent[1][0]+l,a.extent[1][1]+s]]);else if(u){let t=u.measured.width,e=u.measured.height;t&&e&&(f=[[l,s],[l+t,s+e]])}else o?.("005",eZ.error005());let h=nM(f)?nr(e,f,a.measured):e;return(void 0===a.measured.width||void 0===a.measured.height)&&o?.("015",eZ.error015()),{position:{x:h.x-l+(a.measured.width??0)*c[0],y:h.y-s+(a.measured.height??0)*c[1]},positionAbsolute:h}}async function ne({nodesToRemove:t=[],edgesToRemove:e=[],nodes:n,edges:r,onBeforeDelete:i}){let o=new Set(t.map(t=>t.id)),a=[];for(let t of n){if(!1===t.deletable)continue;let e=o.has(t.id),n=!e&&t.parentId&&a.find(e=>e.id===t.parentId);(e||n)&&a.push(t)}let u=new Set(e.map(t=>t.id)),l=r.filter(t=>!1!==t.deletable),s=((t,e)=>{let n=new Set;return t.forEach(t=>{n.add(t.id)}),e.filter(t=>n.has(t.source)||n.has(t.target))})(a,l);for(let t of l)u.has(t.id)&&!s.find(e=>e.id===t.id)&&s.push(t);if(!i)return{edges:s,nodes:a};let c=await i({nodes:a,edges:s});return"boolean"==typeof c?c?{edges:s,nodes:a}:{edges:[],nodes:[]}:c}let nn=(t,e=0,n=1)=>Math.min(Math.max(t,e),n),nr=(t={x:0,y:0},e,n)=>({x:nn(t.x,e[0][0],e[1][0]-(n?.width??0)),y:nn(t.y,e[0][1],e[1][1]-(n?.height??0))});function ni(t,e,n){let{width:r,height:i}=nk(n),{x:o,y:a}=n.internals.positionAbsolute;return nr(t,[[o,a],[o+r,a+i]],e)}let no=(t,e,n)=>t<e?nn(Math.abs(t-e),1,e)/e:t>n?-nn(Math.abs(t-n),1,e)/e:0,na=(t,e,n=15,r=40)=>[no(t.x,r,e.width-r)*n,no(t.y,r,e.height-r)*n],nu=(t,e)=>({x:Math.min(t.x,e.x),y:Math.min(t.y,e.y),x2:Math.max(t.x2,e.x2),y2:Math.max(t.y2,e.y2)}),nl=({x:t,y:e,width:n,height:r})=>({x:t,y:e,x2:t+n,y2:e+r}),ns=({x:t,y:e,x2:n,y2:r})=>({x:t,y:e,width:n-t,height:r-e}),nc=(t,e=[0,0])=>{let{x:n,y:r}=e3(t)?t.internals.positionAbsolute:e6(t,e);return{x:n,y:r,width:t.measured?.width??t.width??t.initialWidth??0,height:t.measured?.height??t.height??t.initialHeight??0}},nf=(t,e=[0,0])=>{let{x:n,y:r}=e3(t)?t.internals.positionAbsolute:e6(t,e);return{x:n,y:r,x2:n+(t.measured?.width??t.width??t.initialWidth??0),y2:r+(t.measured?.height??t.height??t.initialHeight??0)}},nh=(t,e)=>ns(nu(nl(t),nl(e))),nd=(t,e)=>Math.ceil(Math.max(0,Math.min(t.x+t.width,e.x+e.width)-Math.max(t.x,e.x))*Math.max(0,Math.min(t.y+t.height,e.y+e.height)-Math.max(t.y,e.y))),np=t=>ny(t.width)&&ny(t.height)&&ny(t.x)&&ny(t.y),ny=t=>!isNaN(t)&&isFinite(t),nm=(t,e)=>{},ng=(t,e=[1,1])=>({x:e[0]*Math.round(t.x/e[0]),y:e[1]*Math.round(t.y/e[1])}),nv=({x:t,y:e},[n,r,i],o=!1,a=[1,1])=>{let u={x:(t-n)/i,y:(e-r)/i};return o?ng(u,a):u},nx=({x:t,y:e},[n,r,i])=>({x:t*i+n,y:e*i+r});function nw(t,e){if("number"==typeof t)return Math.floor((e-e/(1+t))*.5);if("string"==typeof t&&t.endsWith("px")){let e=parseFloat(t);if(!Number.isNaN(e))return Math.floor(e)}if("string"==typeof t&&t.endsWith("%")){let n=parseFloat(t);if(!Number.isNaN(n))return Math.floor(e*n*.01)}return console.error(`[React Flow] The padding value "${t}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}let nb=(t,e,n,r,i,o)=>{let a=function(t,e,n){if("string"==typeof t||"number"==typeof t){let r=nw(t,n),i=nw(t,e);return{top:r,right:i,bottom:r,left:i,x:2*i,y:2*r}}if("object"==typeof t){let r=nw(t.top??t.y??0,n),i=nw(t.bottom??t.y??0,n),o=nw(t.left??t.x??0,e),a=nw(t.right??t.x??0,e);return{top:r,right:a,bottom:i,left:o,x:o+a,y:r+i}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}(o,e,n),u=nn(Math.min((e-a.x)/t.width,(n-a.y)/t.height),r,i),l=t.x+t.width/2,s=t.y+t.height/2,c=e/2-l*u,f=n/2-s*u,h=function(t,e,n,r,i,o){let{x:a,y:u}=nx(t,[e,n,r]),{x:l,y:s}=nx({x:t.x+t.width,y:t.y+t.height},[e,n,r]);return{left:Math.floor(a),top:Math.floor(u),right:Math.floor(i-l),bottom:Math.floor(o-s)}}(t,c,f,u,e,n),d={left:Math.min(h.left-a.left,0),top:Math.min(h.top-a.top,0),right:Math.min(h.right-a.right,0),bottom:Math.min(h.bottom-a.bottom,0)};return{x:c-d.left+d.right,y:f-d.top+d.bottom,zoom:u}},n_=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function nM(t){return void 0!==t&&"parent"!==t}function nk(t){return{width:t.measured?.width??t.width??t.initialWidth??0,height:t.measured?.height??t.height??t.initialHeight??0}}function nE(t){return(t.measured?.width??t.width??t.initialWidth)!==void 0&&(t.measured?.height??t.height??t.initialHeight)!==void 0}function nA(t,e={width:0,height:0},n,r,i){let o={...t},a=r.get(n);if(a){let t=a.origin||i;o.x+=a.internals.positionAbsolute.x-(e.width??0)*t[0],o.y+=a.internals.positionAbsolute.y-(e.height??0)*t[1]}return o}function nz(t,e){if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0}function nS(){let t,e;return{promise:new Promise((n,r)=>{t=n,e=r}),resolve:t,reject:e}}function nP(t){return{...eQ,...t||{}}}function n$(t,{snapGrid:e=[0,0],snapToGrid:n=!1,transform:r,containerBounds:i}){let{x:o,y:a}=nT(t),u=nv({x:o-(i?.left??0),y:a-(i?.top??0)},r),{x:l,y:s}=n?ng(u,e):u;return{xSnapped:l,ySnapped:s,...u}}let nN=t=>({width:t.offsetWidth,height:t.offsetHeight}),nj=t=>t?.getRootNode?.()||window?.document,nO=["INPUT","SELECT","TEXTAREA"];function nC(t){let e=t.composedPath?.()?.[0]||t.target;return e?.nodeType===1&&(nO.includes(e.nodeName)||e.hasAttribute("contenteditable")||!!e.closest(".nokey"))}let nI=t=>"clientX"in t,nT=(t,e)=>{let n=nI(t),r=n?t.clientX:t.touches?.[0].clientX,i=n?t.clientY:t.touches?.[0].clientY;return{x:r-(e?.left??0),y:i-(e?.top??0)}},nH=(t,e,n,r,i)=>{let o=e.querySelectorAll(`.${t}`);return o&&o.length?Array.from(o).map(e=>{let o=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),type:t,nodeId:i,position:e.getAttribute("data-handlepos"),x:(o.left-n.left)/r,y:(o.top-n.top)/r,...nN(e)}}):null};function nR({sourceX:t,sourceY:e,targetX:n,targetY:r,sourceControlX:i,sourceControlY:o,targetControlX:a,targetControlY:u}){let l=.125*t+.375*i+.375*a+.125*n,s=.125*e+.375*o+.375*u+.125*r,c=Math.abs(l-t),f=Math.abs(s-e);return[l,s,c,f]}function nL(t,e){return t>=0?.5*t:25*e*Math.sqrt(-t)}function nB({pos:t,x1:e,y1:n,x2:r,y2:i,c:o}){switch(t){case s.Left:return[e-nL(e-r,o),n];case s.Right:return[e+nL(r-e,o),n];case s.Top:return[e,n-nL(n-i,o)];case s.Bottom:return[e,n+nL(i-n,o)]}}function nq({sourceX:t,sourceY:e,sourcePosition:n=s.Bottom,targetX:r,targetY:i,targetPosition:o=s.Top,curvature:a=.25}){let[u,l]=nB({pos:n,x1:t,y1:e,x2:r,y2:i,c:a}),[c,f]=nB({pos:o,x1:r,y1:i,x2:t,y2:e,c:a}),[h,d,p,y]=nR({sourceX:t,sourceY:e,targetX:r,targetY:i,sourceControlX:u,sourceControlY:l,targetControlX:c,targetControlY:f});return[`M${t},${e} C${u},${l} ${c},${f} ${r},${i}`,h,d,p,y]}function nD({sourceX:t,sourceY:e,targetX:n,targetY:r}){let i=Math.abs(n-t)/2,o=Math.abs(r-e)/2;return[n<t?n+i:n-i,r<e?r+o:r-o,i,o]}function nV({sourceNode:t,targetNode:e,selected:n=!1,zIndex:r,elevateOnSelect:i=!1}){return void 0!==r?r:(i&&n?1e3:0)+Math.max(t.parentId?t.internals.z:0,e.parentId?e.internals.z:0)}function nX({sourceNode:t,targetNode:e,width:n,height:r,transform:i}){let o=nu(nf(t),nf(e));return o.x===o.x2&&(o.x2+=1),o.y===o.y2&&(o.y2+=1),nd({x:-i[0]/i[2],y:-i[1]/i[2],width:n/i[2],height:r/i[2]},ns(o))>0}let nY=(t,e)=>{let n;return t.source&&t.target?((t,e)=>e.some(e=>e.source===t.source&&e.target===t.target&&(e.sourceHandle===t.sourceHandle||!e.sourceHandle&&!t.sourceHandle)&&(e.targetHandle===t.targetHandle||!e.targetHandle&&!t.targetHandle)))(n=e2(t)?{...t}:{...t,id:(({source:t,sourceHandle:e,target:n,targetHandle:r})=>`xy-edge__${t}${e||""}-${n}${r||""}`)(t)},e)?e:(null===n.sourceHandle&&delete n.sourceHandle,null===n.targetHandle&&delete n.targetHandle,e.concat(n)):(nm("006",eZ.error006()),e)};function nF({sourceX:t,sourceY:e,targetX:n,targetY:r}){let[i,o,a,u]=nD({sourceX:t,sourceY:e,targetX:n,targetY:r});return[`M ${t},${e}L ${n},${r}`,i,o,a,u]}let nU={[s.Left]:{x:-1,y:0},[s.Right]:{x:1,y:0},[s.Top]:{x:0,y:-1},[s.Bottom]:{x:0,y:1}},nW=({source:t,sourcePosition:e=s.Bottom,target:n})=>e===s.Left||e===s.Right?t.x<n.x?{x:1,y:0}:{x:-1,y:0}:t.y<n.y?{x:0,y:1}:{x:0,y:-1},nZ=(t,e)=>Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2));function nK({sourceX:t,sourceY:e,sourcePosition:n=s.Bottom,targetX:r,targetY:i,targetPosition:o=s.Top,borderRadius:a=5,centerX:u,centerY:l,offset:c=20,stepPosition:f=.5}){let[h,d,p,y,m]=function({source:t,sourcePosition:e=s.Bottom,target:n,targetPosition:r=s.Top,center:i,offset:o,stepPosition:a}){let u,l,c=nU[e],f=nU[r],h={x:t.x+c.x*o,y:t.y+c.y*o},d={x:n.x+f.x*o,y:n.y+f.y*o},p=nW({source:h,sourcePosition:e,target:d}),y=0!==p.x?"x":"y",m=p[y],g=[],v={x:0,y:0},x={x:0,y:0},[,,w,b]=nD({sourceX:t.x,sourceY:t.y,targetX:n.x,targetY:n.y});if(c[y]*f[y]==-1){"x"===y?(u=i.x??h.x+(d.x-h.x)*a,l=i.y??(h.y+d.y)/2):(u=i.x??(h.x+d.x)/2,l=i.y??h.y+(d.y-h.y)*a);let t=[{x:u,y:h.y},{x:u,y:d.y}],e=[{x:h.x,y:l},{x:d.x,y:l}];g=c[y]===m?"x"===y?t:e:"x"===y?e:t}else{let i=[{x:h.x,y:d.y}],a=[{x:d.x,y:h.y}];if(g="x"===y?c.x===m?a:i:c.y===m?i:a,e===r){let e=Math.abs(t[y]-n[y]);if(e<=o){let r=Math.min(o-1,o-e);c[y]===m?v[y]=(h[y]>t[y]?-1:1)*r:x[y]=(d[y]>n[y]?-1:1)*r}}if(e!==r){let t="x"===y?"y":"x",e=c[y]===f[t],n=h[t]>d[t],r=h[t]<d[t];(1===c[y]&&(!e&&n||e&&r)||1!==c[y]&&(!e&&r||e&&n))&&(g="x"===y?i:a)}let s={x:h.x+v.x,y:h.y+v.y},p={x:d.x+x.x,y:d.y+x.y};Math.max(Math.abs(s.x-g[0].x),Math.abs(p.x-g[0].x))>=Math.max(Math.abs(s.y-g[0].y),Math.abs(p.y-g[0].y))?(u=(s.x+p.x)/2,l=g[0].y):(u=g[0].x,l=(s.y+p.y)/2)}return[[t,{x:h.x+v.x,y:h.y+v.y},...g,{x:d.x+x.x,y:d.y+x.y},n],u,l,w,b]}({source:{x:t,y:e},sourcePosition:n,target:{x:r,y:i},targetPosition:o,center:{x:u,y:l},offset:c,stepPosition:f});return[h.reduce((t,e,n)=>t+(n>0&&n<h.length-1?function(t,e,n,r){let i=Math.min(nZ(t,e)/2,nZ(e,n)/2,r),{x:o,y:a}=e;if(t.x===o&&o===n.x||t.y===a&&a===n.y)return`L${o} ${a}`;if(t.y===a){let e=t.x<n.x?-1:1,r=t.y<n.y?1:-1;return`L ${o+i*e},${a}Q ${o},${a} ${o},${a+i*r}`}let u=t.x<n.x?1:-1,l=t.y<n.y?-1:1;return`L ${o},${a+i*l}Q ${o},${a} ${o+i*u},${a}`}(h[n-1],e,h[n+1],a):`${0===n?"M":"L"}${e.x} ${e.y}`),""),d,p,y,m]}function nG(t){return t&&!!(t.internals.handleBounds||t.handles?.length)&&!!(t.measured.width||t.width||t.initialWidth)}function nQ(t){let{sourceNode:e,targetNode:n}=t;if(!nG(e)||!nG(n))return null;let r=e.internals.handleBounds||nJ(e.handles),o=n.internals.handleBounds||nJ(n.handles),a=n1(r?.source??[],t.sourceHandle),u=n1(t.connectionMode===i.Strict?o?.target??[]:(o?.target??[]).concat(o?.source??[]),t.targetHandle);if(!a||!u)return t.onError?.("008",eZ.error008(!a?"source":"target",{id:t.id,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle})),null;let l=a?.position||s.Bottom,c=u?.position||s.Top,f=n0(e,a,l),h=n0(n,u,c);return{sourceX:f.x,sourceY:f.y,targetX:h.x,targetY:h.y,sourcePosition:l,targetPosition:c}}function nJ(t){if(!t)return null;let e=[],n=[];for(let r of t)r.width=r.width??1,r.height=r.height??1,"source"===r.type?e.push(r):"target"===r.type&&n.push(r);return{source:e,target:n}}function n0(t,e,n=s.Left,r=!1){let i=(e?.x??0)+t.internals.positionAbsolute.x,o=(e?.y??0)+t.internals.positionAbsolute.y,{width:a,height:u}=e??nk(t);if(r)return{x:i+a/2,y:o+u/2};switch(e?.position??n){case s.Top:return{x:i+a/2,y:o};case s.Right:return{x:i+a,y:o+u/2};case s.Bottom:return{x:i+a/2,y:o+u};case s.Left:return{x:i,y:o+u/2}}}function n1(t,e){return t&&(e?t.find(t=>t.id===e):t[0])||null}function n2(t,e){if(!t)return"";if("string"==typeof t)return t;let n=e?`${e}__`:"";return`${n}${Object.keys(t).sort().map(e=>`${e}=${t[e]}`).join("&")}`}function n5(t,{id:e,defaultColor:n,defaultMarkerStart:r,defaultMarkerEnd:i}){let o=new Set;return t.reduce((t,a)=>([a.markerStart||r,a.markerEnd||i].forEach(r=>{if(r&&"object"==typeof r){let i=n2(r,e);o.has(i)||(t.push({id:i,color:r.color||n,...r}),o.add(i))}}),t),[]).sort((t,e)=>t.id.localeCompare(e.id))}let n3={nodeOrigin:[0,0],nodeExtent:eK,elevateNodesOnSelect:!0,defaults:{}},n6={...n3,checkEquality:!0};function n4(t,e){let n={...t};for(let t in e)void 0!==e[t]&&(n[t]=e[t]);return n}function n9(t,e,n){let r=n4(n3,n);for(let n of t.values())if(n.parentId)n7(n,t,e,r);else{let t=nr(e6(n,r.nodeOrigin),nM(n.extent)?n.extent:r.nodeExtent,nk(n));n.internals.positionAbsolute=t}}function n8(t,e,n,r){let i=n4(n6,r),o=t.length>0,a=new Map(e),u=1e3*!!i?.elevateNodesOnSelect;for(let l of(e.clear(),n.clear(),t)){let t=a.get(l.id);if(i.checkEquality&&l===t?.internals.userNode)e.set(l.id,t);else{let n=nr(e6(l,i.nodeOrigin),nM(l.extent)?l.extent:i.nodeExtent,nk(l));t={...i.defaults,...l,measured:{width:l.measured?.width,height:l.measured?.height},internals:{positionAbsolute:n,handleBounds:l.measured?t?.internals.handleBounds:void 0,z:rt(l,u),userNode:l}},e.set(l.id,t)}void 0!==t.measured&&void 0!==t.measured.width&&void 0!==t.measured.height||t.hidden||(o=!1),l.parentId&&n7(t,e,n,r)}return o}function n7(t,e,n,r){let{elevateNodesOnSelect:i,nodeOrigin:o,nodeExtent:a}=n4(n3,r),u=t.parentId,l=e.get(u);if(!l)return void console.warn(`Parent node ${u} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);!function(t,e){if(!t.parentId)return;let n=e.get(t.parentId);n?n.set(t.id,t):e.set(t.parentId,new Map([[t.id,t]]))}(t,n);let{x:s,y:c,z:f}=function(t,e,n,r,i){let{x:o,y:a}=e.internals.positionAbsolute,u=nk(t),l=e6(t,n),s=nM(t.extent)?nr(l,t.extent,u):l,c=nr({x:o+s.x,y:a+s.y},r,u);"parent"===t.extent&&(c=ni(c,u,e));let f=rt(t,i),h=e.internals.z??0;return{x:c.x,y:c.y,z:h>=f?h+1:f}}(t,l,o,a,1e3*!!i),{positionAbsolute:h}=t.internals,d=s!==h.x||c!==h.y;(d||f!==t.internals.z)&&e.set(t.id,{...t,internals:{...t.internals,positionAbsolute:d?{x:s,y:c}:h,z:f}})}function rt(t,e){return(ny(t.zIndex)?t.zIndex:0)+(t.selected?e:0)}function re(t,e,n,r=[0,0]){let i=[],o=new Map;for(let n of t){let t=e.get(n.parentId);if(!t)continue;let r=nh(o.get(n.parentId)?.expandedRect??nc(t),n.rect);o.set(n.parentId,{expandedRect:r,parent:t})}return o.size>0&&o.forEach(({expandedRect:e,parent:o},a)=>{let u=o.internals.positionAbsolute,l=nk(o),s=o.origin??r,c=e.x<u.x?Math.round(Math.abs(u.x-e.x)):0,f=e.y<u.y?Math.round(Math.abs(u.y-e.y)):0,h=Math.max(l.width,Math.round(e.width)),d=Math.max(l.height,Math.round(e.height)),p=(h-l.width)*s[0],y=(d-l.height)*s[1];(c>0||f>0||p||y)&&(i.push({id:a,type:"position",position:{x:o.position.x-c+p,y:o.position.y-f+y}}),n.get(a)?.forEach(e=>{t.some(t=>t.id===e.id)||i.push({id:e.id,type:"position",position:{x:e.position.x+c,y:e.position.y+f}})})),(l.width<e.width||l.height<e.height||c||f)&&i.push({id:a,type:"dimensions",setAttributes:!0,dimensions:{width:h+(c?s[0]*c-p:0),height:d+(f?s[1]*f-y:0)}})}),i}function rn(t,e,n,r,i,o){let a=r?.querySelector(".xyflow__viewport"),u=!1;if(!a)return{changes:[],updatedInternals:u};let l=[],s=window.getComputedStyle(a),{m22:c}=new window.DOMMatrixReadOnly(s.transform),f=[];for(let r of t.values()){let t=e.get(r.id);if(!t)continue;if(t.hidden){e.set(t.id,{...t,internals:{...t.internals,handleBounds:void 0}}),u=!0;continue}let a=nN(r.nodeElement),s=t.measured.width!==a.width||t.measured.height!==a.height;if(a.width&&a.height&&(s||!t.internals.handleBounds||r.force)){let h=r.nodeElement.getBoundingClientRect(),d=nM(t.extent)?t.extent:o,{positionAbsolute:p}=t.internals;t.parentId&&"parent"===t.extent?p=ni(p,a,e.get(t.parentId)):d&&(p=nr(p,d,a));let y={...t,measured:a,internals:{...t.internals,positionAbsolute:p,handleBounds:{source:nH("source",r.nodeElement,h,c,t.id),target:nH("target",r.nodeElement,h,c,t.id)}}};e.set(t.id,y),t.parentId&&n7(y,e,n,{nodeOrigin:i}),u=!0,s&&(l.push({id:t.id,type:"dimensions",dimensions:a}),t.expandParent&&t.parentId&&f.push({id:t.id,parentId:t.parentId,rect:nc(y,i)}))}}if(f.length>0){let t=re(f,e,n,i);l.push(...t)}return{changes:l,updatedInternals:u}}async function rr({delta:t,panZoom:e,transform:n,translateExtent:r,width:i,height:o}){if(!e||!t.x&&!t.y)return Promise.resolve(!1);let a=await e.setViewportConstrained({x:n[0]+t.x,y:n[1]+t.y,zoom:n[2]},[[0,0],[i,o]],r);return Promise.resolve(!!a&&(a.x!==n[0]||a.y!==n[1]||a.k!==n[2]))}function ri(t,e,n,r,i,o){let a=i,u=r.get(a)||new Map;r.set(a,u.set(n,e)),a=`${i}-${t}`;let l=r.get(a)||new Map;if(r.set(a,l.set(n,e)),o){a=`${i}-${t}-${o}`;let u=r.get(a)||new Map;r.set(a,u.set(n,e))}}function ro(t,e,n){for(let r of(t.clear(),e.clear(),n)){let{source:n,target:i,sourceHandle:o=null,targetHandle:a=null}=r,u={edgeId:r.id,source:n,target:i,sourceHandle:o,targetHandle:a},l=`${n}-${o}--${i}-${a}`;ri("source",u,`${i}-${a}--${n}-${o}`,t,n,o),ri("target",u,l,t,i,a),e.set(r.id,r)}}function ra(t,e,n){let r=t;do{if(r?.matches?.(e))return!0;if(r===n)break;r=r?.parentElement}while(r);return!1}function ru({nodeId:t,dragItems:e,nodeLookup:n,dragging:r=!0}){let i=[];for(let[t,o]of e){let e=n.get(t)?.internals.userNode;e&&i.push({...e,position:o.position,dragging:r})}if(!t)return[i[0],i];let o=n.get(t)?.internals.userNode;return[o?{...o,position:e.get(t)?.position||o.position,dragging:r}:i[0],i]}function rl({onNodeMouseDown:t,getStoreItems:e,onDragStart:n,onDrag:r,onDragStop:i}){let o={x:null,y:null},a=0,u=new Map,l=!1,s={x:0,y:0},c=null,f=!1,d=null,p=!1,y=!1;return{update:function({noDragClassName:m,handleSelector:g,domNode:v,isSelectable:x,nodeId:w,nodeClickDistance:b=0}){function _({x:t,y:n},i){let{nodeLookup:a,nodeExtent:l,snapGrid:s,snapToGrid:c,nodeOrigin:f,onNodeDrag:h,onSelectionDrag:d,onError:p,updateNodePositions:m}=e();o={x:t,y:n};let g=!1,v={x:0,y:0,x2:0,y2:0};for(let[e,r]of(u.size>1&&l&&(v=nl(e9(u))),u)){if(!a.has(e))continue;let i={x:t-r.distance.x,y:n-r.distance.y};c&&(i=ng(i,s));let o=[[l[0][0],l[0][1]],[l[1][0],l[1][1]]];if(u.size>1&&l&&!r.extent){let{positionAbsolute:t}=r.internals,e=t.x-v.x+l[0][0],n=t.x+r.measured.width-v.x2+l[1][0];o=[[e,t.y-v.y+l[0][1]],[n,t.y+r.measured.height-v.y2+l[1][1]]]}let{position:h,positionAbsolute:d}=nt({nodeId:e,nextPosition:i,nodeLookup:a,nodeExtent:o,nodeOrigin:f,onError:p});g=g||r.position.x!==h.x||r.position.y!==h.y,r.position=h,r.internals.positionAbsolute=d}if(y=y||g,g&&(m(u,!0),i&&(r||h||!w&&d))){let[t,e]=ru({nodeId:w,dragItems:u,nodeLookup:a});r?.(i,u,t,e),h?.(i,t,e),w||d?.(i,e)}}async function M(){if(!c)return;let{transform:t,panBy:n,autoPanSpeed:r,autoPanOnNodeDrag:i}=e();if(!i){l=!1,cancelAnimationFrame(a);return}let[u,f]=na(s,c,r);(0!==u||0!==f)&&(o.x=(o.x??0)-u/t[2],o.y=(o.y??0)-f/t[2],await n({x:u,y:f})&&_(o,null)),a=requestAnimationFrame(M)}function k(r){let{nodeLookup:i,multiSelectionActive:a,nodesDraggable:l,transform:s,snapGrid:h,snapToGrid:d,selectNodesOnDrag:p,onNodeDragStart:y,onSelectionDragStart:m,unselectNodesAndEdges:g}=e();f=!0,p&&x||a||!w||i.get(w)?.selected||g(),x&&p&&w&&t?.(w);let v=n$(r.sourceEvent,{transform:s,snapGrid:h,snapToGrid:d,containerBounds:c});if(o=v,(u=function(t,e,n,r){let i=new Map;for(let[o,a]of t)if((a.selected||a.id===r)&&(!a.parentId||!function t(e,n){if(!e.parentId)return!1;let r=n.get(e.parentId);return!!r&&(!!r.selected||t(r,n))}(a,t))&&(a.draggable||e&&void 0===a.draggable)){let e=t.get(o);e&&i.set(o,{id:o,position:e.position||{x:0,y:0},distance:{x:n.x-e.internals.positionAbsolute.x,y:n.y-e.internals.positionAbsolute.y},extent:e.extent,parentId:e.parentId,origin:e.origin,expandParent:e.expandParent,internals:{positionAbsolute:e.internals.positionAbsolute||{x:0,y:0}},measured:{width:e.measured.width??0,height:e.measured.height??0}})}return i}(i,l,v,w)).size>0&&(n||y||!w&&m)){let[t,e]=ru({nodeId:w,dragItems:u,nodeLookup:i});n?.(r.sourceEvent,u,t,e),y?.(r.sourceEvent,t,e),w||m?.(r.sourceEvent,e)}}d=tn(v);let E=(function(){var t,e,n,r,i=th,o=td,a=tp,u=ty,l={},s=h("start","drag","end"),c=0,f=0;function d(t){t.on("mousedown.drag",p).filter(u).on("touchstart.drag",g).on("touchmove.drag",v,ti).on("touchend.drag touchcancel.drag",x).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(a,u){if(!r&&i.call(this,a,u)){var l=w(this,o.call(this,a,u),a,u,"mouse");l&&(tn(a.view).on("mousemove.drag",y,to).on("mouseup.drag",m,to),tl(a.view),ta(a),n=!1,t=a.clientX,e=a.clientY,l("start",a))}}function y(r){if(tu(r),!n){var i=r.clientX-t,o=r.clientY-e;n=i*i+o*o>f}l.mouse("drag",r)}function m(t){tn(t.view).on("mousemove.drag mouseup.drag",null),ts(t.view,n),tu(t),l.mouse("end",t)}function g(t,e){if(i.call(this,t,e)){var n,r,a=t.changedTouches,u=o.call(this,t,e),l=a.length;for(n=0;n<l;++n)(r=w(this,u,t,e,a[n].identifier,a[n]))&&(ta(t),r("start",t,a[n]))}}function v(t){var e,n,r=t.changedTouches,i=r.length;for(e=0;e<i;++e)(n=l[r[e].identifier])&&(tu(t),n("drag",t,r[e]))}function x(t){var e,n,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),e=0;e<o;++e)(n=l[i[e].identifier])&&(ta(t),n("end",t,i[e]))}function w(t,e,n,r,i,o){var u,f,h,p=s.copy(),y=tr(o||n,e);if(null!=(h=a.call(t,new tf("beforestart",{sourceEvent:n,target:d,identifier:i,active:c,x:y[0],y:y[1],dx:0,dy:0,dispatch:p}),r)))return u=h.x-y[0]||0,f=h.y-y[1]||0,function n(o,a,s){var m,g=y;switch(o){case"start":l[i]=n,m=c++;break;case"end":delete l[i],--c;case"drag":y=tr(s||a,e),m=c}p.call(o,t,new tf(o,{sourceEvent:a,subject:h,target:d,identifier:i,active:m,x:y[0]+u,y:y[1]+f,dx:y[0]-g[0],dy:y[1]-g[1],dispatch:p}),r)}}return d.filter=function(t){return arguments.length?(i="function"==typeof t?t:tc(!!t),d):i},d.container=function(t){return arguments.length?(o="function"==typeof t?t:tc(t),d):o},d.subject=function(t){return arguments.length?(a="function"==typeof t?t:tc(t),d):a},d.touchable=function(t){return arguments.length?(u="function"==typeof t?t:tc(!!t),d):u},d.on=function(){var t=s.on.apply(s,arguments);return t===s?d:t},d.clickDistance=function(t){return arguments.length?(f=(t*=1)*t,d):Math.sqrt(f)},d})().clickDistance(b).on("start",t=>{let{domNode:n,nodeDragThreshold:r,transform:i,snapGrid:a,snapToGrid:u}=e();c=n?.getBoundingClientRect()||null,p=!1,y=!1,0===r&&k(t),o=n$(t.sourceEvent,{transform:i,snapGrid:a,snapToGrid:u,containerBounds:c}),s=nT(t.sourceEvent,c)}).on("drag",t=>{let{autoPanOnNodeDrag:n,transform:r,snapGrid:i,snapToGrid:a,nodeDragThreshold:h,nodeLookup:d}=e(),y=n$(t.sourceEvent,{transform:r,snapGrid:i,snapToGrid:a,containerBounds:c});if(("touchmove"===t.sourceEvent.type&&t.sourceEvent.touches.length>1||w&&!d.has(w))&&(p=!0),!p){if(!l&&n&&f&&(l=!0,M()),!f){let e=y.xSnapped-(o.x??0),n=y.ySnapped-(o.y??0);Math.sqrt(e*e+n*n)>h&&k(t)}(o.x!==y.xSnapped||o.y!==y.ySnapped)&&u&&f&&(s=nT(t.sourceEvent,c),_(y,t.sourceEvent))}}).on("end",t=>{if(f&&!p&&(l=!1,f=!1,cancelAnimationFrame(a),u.size>0)){let{nodeLookup:n,updateNodePositions:r,onNodeDragStop:o,onSelectionDragStop:a}=e();if(y&&(r(u,!1),y=!1),i||o||!w&&a){let[e,r]=ru({nodeId:w,dragItems:u,nodeLookup:n,dragging:!1});i?.(t.sourceEvent,u,e,r),o?.(t.sourceEvent,e,r),w||a?.(t.sourceEvent,r)}}}).filter(t=>{let e=t.target;return!t.button&&(!m||!ra(e,`.${m}`,v))&&(!g||ra(e,g,v))});d.call(E)},destroy:function(){d?.on(".drag",null)}}}function rs(t,e,n,r,i,o=!1){let a=r.get(t);if(!a)return null;let u="strict"===i?a.internals.handleBounds?.[e]:[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]],l=(n?u?.find(t=>t.id===n):u?.[0])??null;return l&&o?{...l,...n0(a,l,l.position,!0)}:l}function rc(t,e){return t?t:e?.classList.contains("target")?"target":e?.classList.contains("source")?"source":null}let rf=()=>!0;function rh(t,{handle:e,connectionMode:n,fromNodeId:r,fromHandleId:o,fromType:a,doc:u,lib:l,flowId:s,isValidConnection:c=rf,nodeLookup:f}){let h="target"===a,d=e?u.querySelector(`.${l}-flow__handle[data-id="${s}-${e?.nodeId}-${e?.id}-${e?.type}"]`):null,{x:p,y}=nT(t),m=u.elementFromPoint(p,y),g=m?.classList.contains(`${l}-flow__handle`)?m:d,v={handleDomNode:g,isValid:!1,connection:null,toHandle:null};if(g){let t=rc(void 0,g),e=g.getAttribute("data-nodeid"),a=g.getAttribute("data-handleid"),u=g.classList.contains("connectable"),l=g.classList.contains("connectableend");if(!e||!t)return v;let s={source:h?e:r,sourceHandle:h?a:o,target:h?r:e,targetHandle:h?o:a};v.connection=s,v.isValid=u&&l&&(n===i.Strict?h&&"source"===t||!h&&"target"===t:e!==r||a!==o)&&c(s),v.toHandle=rs(e,t,a,f,n,!0)}return v}let rd={onPointerDown:function(t,{connectionMode:e,connectionRadius:n,handleId:r,nodeId:i,edgeUpdaterType:o,isTarget:a,domNode:u,nodeLookup:l,lib:c,autoPanOnConnect:f,flowId:h,panBy:d,cancelConnection:p,onConnectStart:y,onConnect:m,onConnectEnd:g,isValidConnection:v=rf,onReconnectEnd:x,updateConnection:w,getTransform:b,getFromHandle:_,autoPanSpeed:M,dragThreshold:k=1}){let E,A=nj(t.target),z=0,{x:S,y:P}=nT(t),$=rc(o,A?.elementFromPoint(S,P)),N=u?.getBoundingClientRect(),j=!1;if(!N||!$)return;let O=rs(i,$,r,l,e);if(!O)return;let C=nT(t,N),I=!1,T=null,H=!1,R=null,L={...O,nodeId:i,type:$,position:O.position},B=l.get(i),q={inProgress:!0,isValid:null,from:n0(B,L,s.Left,!0),fromHandle:L,fromPosition:L.position,fromNode:B,to:C,toHandle:null,toPosition:e0[L.position],toNode:null};function D(){j=!0,w(q),y?.(t,{nodeId:i,handleId:r,handleType:$})}function V(t){var o,u;let s;if(!j){let{x:e,y:n}=nT(t),r=e-S,i=n-P;if(!(r*r+i*i>k*k))return;D()}if(!_()||!L)return void X(t);let p=b();E=function(t,e,n,r){let i=[],o=1/0;for(let a of function(t,e,n){let r=[],i={x:t.x-n,y:t.y-n,width:2*n,height:2*n};for(let t of e.values())nd(i,nc(t))>0&&r.push(t);return r}(t,n,e+250))for(let n of[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]]){if(r.nodeId===n.nodeId&&r.type===n.type&&r.id===n.id)continue;let{x:u,y:l}=n0(a,n,n.position,!0),s=Math.sqrt(Math.pow(u-t.x,2)+Math.pow(l-t.y,2));s>e||(s<o?(i=[{...n,x:u,y:l}],o=s):s===o&&i.push({...n,x:u,y:l}))}if(!i.length)return null;if(i.length>1){let t="source"===r.type?"target":"source";return i.find(e=>e.type===t)??i[0]}return i[0]}(nv(C=nT(t,N),p,!1,[1,1]),n,l,L),I||(!function t(){if(!f||!N)return;let[e,n]=na(C,N,M);d({x:e,y:n}),z=requestAnimationFrame(t)}(),I=!0);let y=rh(t,{handle:E,connectionMode:e,fromNodeId:i,fromHandleId:r,fromType:a?"target":"source",isValidConnection:v,doc:A,lib:c,flowId:h,nodeLookup:l});R=y.handleDomNode,T=y.connection,o=!!E,u=y.isValid,s=null,u?s=!0:o&&!u&&(s=!1),H=s;let m={...q,isValid:H,to:y.toHandle&&H?nx({x:y.toHandle.x,y:y.toHandle.y},p):C,toHandle:y.toHandle,toPosition:H&&y.toHandle?y.toHandle.position:e0[L.position],toNode:y.toHandle?l.get(y.toHandle.nodeId):null};H&&E&&q.toHandle&&m.toHandle&&q.toHandle.type===m.toHandle.type&&q.toHandle.nodeId===m.toHandle.nodeId&&q.toHandle.id===m.toHandle.id&&q.to.x===m.to.x&&q.to.y===m.to.y||(w(m),q=m)}function X(t){if(j){(E||R)&&T&&H&&m?.(T);let{inProgress:e,...n}=q,r={...n,toPosition:q.toHandle?q.toPosition:null};g?.(t,r),o&&x?.(t,r)}p(),cancelAnimationFrame(z),I=!1,H=!1,T=null,R=null,A.removeEventListener("mousemove",V),A.removeEventListener("mouseup",X),A.removeEventListener("touchmove",V),A.removeEventListener("touchend",X)}0===k&&D(),A.addEventListener("mousemove",V),A.addEventListener("mouseup",X),A.addEventListener("touchmove",V),A.addEventListener("touchend",X)},isValid:rh};function rp({domNode:t,panZoom:e,getTransform:n,getViewScale:r}){let i=tn(t);return{update:function({translateExtent:t,width:o,height:a,zoomStep:u=10,pannable:l=!0,zoomable:s=!0,inversePan:c=!1}){let f=[0,0],h=eU().on("start",t=>{("mousedown"===t.sourceEvent.type||"touchstart"===t.sourceEvent.type)&&(f=[t.sourceEvent.clientX??t.sourceEvent.touches[0].clientX,t.sourceEvent.clientY??t.sourceEvent.touches[0].clientY])}).on("zoom",l?i=>{let u=n();if("mousemove"!==i.sourceEvent.type&&"touchmove"!==i.sourceEvent.type||!e)return;let l=[i.sourceEvent.clientX??i.sourceEvent.touches[0].clientX,i.sourceEvent.clientY??i.sourceEvent.touches[0].clientY],s=[l[0]-f[0],l[1]-f[1]];f=l;let h=r()*Math.max(u[2],Math.log(u[2]))*(c?-1:1),d={x:u[0]-s[0]*h,y:u[1]-s[1]*h};e.setViewportConstrained({x:d.x,y:d.y,zoom:u[2]},[[0,0],[o,a]],t)}:null).on("zoom.wheel",s?t=>{let r=n();if("wheel"!==t.sourceEvent.type||!e)return;let i=-t.sourceEvent.deltaY*(1===t.sourceEvent.deltaMode?.05:t.sourceEvent.deltaMode?1:.002)*u,o=r[2]*Math.pow(2,i);e.scaleTo(o)}:null);i.call(h,{})},destroy:function(){i.on("zoom",null)},pointer:tr}}let ry=t=>({x:t.x,y:t.y,zoom:t.k}),rm=({x:t,y:e,zoom:n})=>eH.translate(t,e).scale(n),rg=(t,e)=>t.target.closest(`.${e}`),rv=(t,e)=>2===e&&Array.isArray(t)&&t.includes(2),rx=t=>((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2,rw=(t,e=0,n=rx,r=()=>{})=>{let i="number"==typeof e&&e>0;return i||r(),i?t.transition().duration(e).ease(n).on("end",r):t},rb=t=>{let e=t.ctrlKey&&n_()?10:1;return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*e};function r_({domNode:t,minZoom:e,maxZoom:n,paneClickDistance:r,translateExtent:i,viewport:a,onPanZoom:u,onPanZoomStart:l,onPanZoomEnd:s,onDraggingChange:c}){let f={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},h=t.getBoundingClientRect(),d=eU().clickDistance(!ny(r)||r<0?0:r).scaleExtent([e,n]).translateExtent(i),p=tn(t).call(d);x({x:a.x,y:a.y,zoom:nn(a.zoom,e,n)},[[0,0],[h.width,h.height]],i);let y=p.on("wheel.zoom"),m=p.on("dblclick.zoom");function g(t,e){return p?new Promise(n=>{d?.interpolate(e?.interpolate==="linear"?eW:tg).transform(rw(p,e?.duration,e?.ease,()=>n(!0)),t)}):Promise.resolve(!1)}function v(){d.on("zoom",null)}async function x(t,e,n){let r=rm(t),i=d?.constrain()(r,e,n);return i&&await g(i),new Promise(t=>t(i))}return d.wheelDelta(rb),{update:function({noWheelClassName:t,noPanClassName:e,onPaneContextMenu:n,userSelectionActive:r,panOnScroll:i,panOnDrag:a,panOnScrollMode:h,panOnScrollSpeed:g,preventScrolling:x,zoomOnPinch:w,zoomOnScroll:b,zoomOnDoubleClick:_,zoomActivationKeyPressed:M,lib:k,onTransformChange:E}){r&&!f.isZoomingOrPanning&&v();let A=!i||M||r?function({noWheelClassName:t,preventScrolling:e,d3ZoomHandler:n}){return function(r,i){let o="wheel"===r.type,a=!e&&o&&!r.ctrlKey,u=rg(r,t);if(r.ctrlKey&&o&&u&&r.preventDefault(),a||u)return null;r.preventDefault(),n.call(this,r,i)}}({noWheelClassName:t,preventScrolling:x,d3ZoomHandler:y}):function({zoomPanValues:t,noWheelClassName:e,d3Selection:n,d3Zoom:r,panOnScrollMode:i,panOnScrollSpeed:a,zoomOnPinch:u,onPanZoomStart:l,onPanZoom:s,onPanZoomEnd:c}){return f=>{if(rg(f,e))return!1;f.preventDefault(),f.stopImmediatePropagation();let h=n.property("__zoom").k||1;if(f.ctrlKey&&u){let t=tr(f),e=h*Math.pow(2,rb(f));r.scaleTo(n,e,t,f);return}let d=1===f.deltaMode?20:1,p=i===o.Vertical?0:f.deltaX*d,y=i===o.Horizontal?0:f.deltaY*d;!n_()&&f.shiftKey&&i!==o.Vertical&&(p=f.deltaY*d,y=0),r.translateBy(n,-(p/h)*a,-(y/h)*a,{internal:!0});let m=ry(n.property("__zoom"));clearTimeout(t.panScrollTimeout),t.isPanScrolling||(t.isPanScrolling=!0,l?.(f,m)),t.isPanScrolling&&(s?.(f,m),t.panScrollTimeout=setTimeout(()=>{c?.(f,m),t.isPanScrolling=!1},150))}}({zoomPanValues:f,noWheelClassName:t,d3Selection:p,d3Zoom:d,panOnScrollMode:h,panOnScrollSpeed:g,zoomOnPinch:w,onPanZoomStart:l,onPanZoom:u,onPanZoomEnd:s});if(p.on("wheel.zoom",A,{passive:!1}),!r){let t=function({zoomPanValues:t,onDraggingChange:e,onPanZoomStart:n}){return r=>{if(r.sourceEvent?.internal)return;let i=ry(r.transform);t.mouseButton=r.sourceEvent?.button||0,t.isZoomingOrPanning=!0,t.prevViewport=i,r.sourceEvent?.type==="mousedown"&&e(!0),n&&n?.(r.sourceEvent,i)}}({zoomPanValues:f,onDraggingChange:c,onPanZoomStart:l});d.on("start",t);let e=function({zoomPanValues:t,panOnDrag:e,onPaneContextMenu:n,onTransformChange:r,onPanZoom:i}){return o=>{t.usedRightMouseButton=!!(n&&rv(e,t.mouseButton??0)),o.sourceEvent?.sync||r([o.transform.x,o.transform.y,o.transform.k]),i&&!o.sourceEvent?.internal&&i?.(o.sourceEvent,ry(o.transform))}}({zoomPanValues:f,panOnDrag:a,onPaneContextMenu:!!n,onPanZoom:u,onTransformChange:E});d.on("zoom",e);let r=function({zoomPanValues:t,panOnDrag:e,panOnScroll:n,onDraggingChange:r,onPanZoomEnd:i,onPaneContextMenu:o}){return a=>{if(!a.sourceEvent?.internal){let u,l;if(t.isZoomingOrPanning=!1,o&&rv(e,t.mouseButton??0)&&!t.usedRightMouseButton&&a.sourceEvent&&o(a.sourceEvent),t.usedRightMouseButton=!1,r(!1),i&&(u=t.prevViewport,l=a.transform,u.x!==l.x||u.y!==l.y||u.zoom!==l.k)){let e=ry(a.transform);t.prevViewport=e,clearTimeout(t.timerId),t.timerId=setTimeout(()=>{i?.(a.sourceEvent,e)},150*!!n)}}}}({zoomPanValues:f,panOnDrag:a,panOnScroll:i,onPaneContextMenu:n,onPanZoomEnd:s,onDraggingChange:c});d.on("end",r)}let z=function({zoomActivationKeyPressed:t,zoomOnScroll:e,zoomOnPinch:n,panOnDrag:r,panOnScroll:i,zoomOnDoubleClick:o,userSelectionActive:a,noWheelClassName:u,noPanClassName:l,lib:s}){return c=>{let f=t||e,h=n&&c.ctrlKey;if(1===c.button&&"mousedown"===c.type&&(rg(c,`${s}-flow__node`)||rg(c,`${s}-flow__edge`)))return!0;if(!r&&!f&&!i&&!o&&!n||a||rg(c,u)&&"wheel"===c.type||rg(c,l)&&("wheel"!==c.type||i&&"wheel"===c.type&&!t)||!n&&c.ctrlKey&&"wheel"===c.type)return!1;if(!n&&"touchstart"===c.type&&c.touches?.length>1)return c.preventDefault(),!1;if(!f&&!i&&!h&&"wheel"===c.type||!r&&("mousedown"===c.type||"touchstart"===c.type)||Array.isArray(r)&&!r.includes(c.button)&&"mousedown"===c.type)return!1;let d=Array.isArray(r)&&r.includes(c.button)||!c.button||c.button<=1;return(!c.ctrlKey||"wheel"===c.type)&&d}}({zoomActivationKeyPressed:M,panOnDrag:a,zoomOnScroll:b,panOnScroll:i,zoomOnDoubleClick:_,zoomOnPinch:w,userSelectionActive:r,noPanClassName:e,noWheelClassName:t,lib:k});d.filter(z),_?p.on("dblclick.zoom",m):p.on("dblclick.zoom",null)},destroy:v,setViewport:async function t(t,e){let n=rm(t);return await g(n,e),new Promise(t=>t(n))},setViewportConstrained:x,getViewport:function(){let t=p?eR(p.node()):{x:0,y:0,k:1};return{x:t.x,y:t.y,zoom:t.k}},scaleTo:function(t,e){return p?new Promise(n=>{d?.interpolate(e?.interpolate==="linear"?eW:tg).scaleTo(rw(p,e?.duration,e?.ease,()=>n(!0)),t)}):Promise.resolve(!1)},scaleBy:function(t,e){return p?new Promise(n=>{d?.interpolate(e?.interpolate==="linear"?eW:tg).scaleBy(rw(p,e?.duration,e?.ease,()=>n(!0)),t)}):Promise.resolve(!1)},setScaleExtent:function(t){d?.scaleExtent(t)},setTranslateExtent:function(t){d?.translateExtent(t)},syncViewport:function(t){if(p){let e=rm(t),n=p.property("__zoom");(n.k!==t.zoom||n.x!==t.x||n.y!==t.y)&&d?.transform(p,e,null,{sync:!0})}},setClickDistance:function(t){let e=!ny(t)||t<0?0:t;d?.clickDistance(e)}}}!function(t){t.Line="line",t.Handle="handle"}(c||(c={}))},9302:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]])},9621:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});let r=(0,n(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9946:(t,e,n)=>{"use strict";n.d(e,{A:()=>l});var r=n(2115);let i=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},o=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,r.forwardRef)((t,e)=>{let{color:n="currentColor",size:i=24,strokeWidth:u=2,absoluteStrokeWidth:l,className:s="",children:c,iconNode:f,...h}=t;return(0,r.createElement)("svg",{ref:e,...a,width:i,height:i,stroke:n,strokeWidth:l?24*Number(u)/Number(i):u,className:o("lucide",s),...!c&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(h)&&{"aria-hidden":"true"},...h},[...f.map(t=>{let[e,n]=t;return(0,r.createElement)(e,n)}),...Array.isArray(c)?c:[c]])}),l=(t,e)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:l,...s}=n;return(0,r.createElement)(u,{ref:a,iconNode:e,className:o("lucide-".concat(i(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),l),...s})});return n.displayName=i(t),n}}}]);