const CHUNK_PUBLIC_PATH = "server/app/api/analyze/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_22c62c55._.js");
runtime.loadChunk("server/chunks/node_modules_@octokit_plugin-rest-endpoint-methods_dist-src_f65126a0._.js");
runtime.loadChunk("server/chunks/node_modules_openai_fbab7b8a._.js");
runtime.loadChunk("server/chunks/node_modules_a055b1d7._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__27a06cc4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/analyze/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
