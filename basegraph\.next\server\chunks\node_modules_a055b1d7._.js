module.exports = {

"[project]/node_modules/universal-user-agent/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getUserAgent": ()=>getUserAgent
});
function getUserAgent() {
    if (typeof navigator === "object" && "userAgent" in navigator) {
        return navigator.userAgent;
    }
    if (typeof process === "object" && process.version !== undefined) {
        return `Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`;
    }
    return "<environment undetectable>";
}
}),
"[project]/node_modules/before-after-hook/lib/register.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// @ts-check
__turbopack_context__.s({
    "register": ()=>register
});
function register(state, name, method, options) {
    if (typeof method !== "function") {
        throw new Error("method for before hook must be a function");
    }
    if (!options) {
        options = {};
    }
    if (Array.isArray(name)) {
        return name.reverse().reduce((callback, name)=>{
            return register.bind(null, state, name, callback, options);
        }, method)();
    }
    return Promise.resolve().then(()=>{
        if (!state.registry[name]) {
            return method(options);
        }
        return state.registry[name].reduce((method, registered)=>{
            return registered.hook.bind(null, method, options);
        }, method)();
    });
}
}),
"[project]/node_modules/before-after-hook/lib/add.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// @ts-check
__turbopack_context__.s({
    "addHook": ()=>addHook
});
function addHook(state, kind, name, hook) {
    const orig = hook;
    if (!state.registry[name]) {
        state.registry[name] = [];
    }
    if (kind === "before") {
        hook = (method, options)=>{
            return Promise.resolve().then(orig.bind(null, options)).then(method.bind(null, options));
        };
    }
    if (kind === "after") {
        hook = (method, options)=>{
            let result;
            return Promise.resolve().then(method.bind(null, options)).then((result_)=>{
                result = result_;
                return orig(result, options);
            }).then(()=>{
                return result;
            });
        };
    }
    if (kind === "error") {
        hook = (method, options)=>{
            return Promise.resolve().then(method.bind(null, options)).catch((error)=>{
                return orig(error, options);
            });
        };
    }
    state.registry[name].push({
        hook: hook,
        orig: orig
    });
}
}),
"[project]/node_modules/before-after-hook/lib/remove.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// @ts-check
__turbopack_context__.s({
    "removeHook": ()=>removeHook
});
function removeHook(state, name, method) {
    if (!state.registry[name]) {
        return;
    }
    const index = state.registry[name].map((registered)=>{
        return registered.orig;
    }).indexOf(method);
    if (index === -1) {
        return;
    }
    state.registry[name].splice(index, 1);
}
}),
"[project]/node_modules/before-after-hook/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// @ts-check
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$register$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/before-after-hook/lib/register.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$add$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/before-after-hook/lib/add.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$remove$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/before-after-hook/lib/remove.js [app-route] (ecmascript)");
;
;
;
// bind with array of arguments: https://stackoverflow.com/a/21792913
const bind = Function.bind;
const bindable = bind.bind(bind);
function bindApi(hook, state, name) {
    const removeHookRef = bindable(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$remove$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["removeHook"], null).apply(null, name ? [
        state,
        name
    ] : [
        state
    ]);
    hook.api = {
        remove: removeHookRef
    };
    hook.remove = removeHookRef;
    [
        "before",
        "error",
        "after",
        "wrap"
    ].forEach((kind)=>{
        const args = name ? [
            state,
            kind,
            name
        ] : [
            state,
            kind
        ];
        hook[kind] = hook.api[kind] = bindable(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$add$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addHook"], null).apply(null, args);
    });
}
function Singular() {
    const singularHookName = Symbol("Singular");
    const singularHookState = {
        registry: {}
    };
    const singularHook = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$register$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["register"].bind(null, singularHookState, singularHookName);
    bindApi(singularHook, singularHookState, singularHookName);
    return singularHook;
}
function Collection() {
    const state = {
        registry: {}
    };
    const hook = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$lib$2f$register$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["register"].bind(null, state);
    bindApi(hook, state);
    return hook;
}
const __TURBOPACK__default__export__ = {
    Singular,
    Collection
};
}),
"[project]/node_modules/@octokit/endpoint/dist-bundle/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// pkg/dist-src/defaults.js
__turbopack_context__.s({
    "endpoint": ()=>endpoint
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/universal-user-agent/index.js [app-route] (ecmascript)");
;
// pkg/dist-src/version.js
var VERSION = "0.0.0-development";
// pkg/dist-src/defaults.js
var userAgent = `octokit-endpoint.js/${VERSION} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserAgent"])()}`;
var DEFAULTS = {
    method: "GET",
    baseUrl: "https://api.github.com",
    headers: {
        accept: "application/vnd.github.v3+json",
        "user-agent": userAgent
    },
    mediaType: {
        format: ""
    }
};
// pkg/dist-src/util/lowercase-keys.js
function lowercaseKeys(object) {
    if (!object) {
        return {};
    }
    return Object.keys(object).reduce((newObj, key)=>{
        newObj[key.toLowerCase()] = object[key];
        return newObj;
    }, {});
}
// pkg/dist-src/util/is-plain-object.js
function isPlainObject(value) {
    if (typeof value !== "object" || value === null) return false;
    if (Object.prototype.toString.call(value) !== "[object Object]") return false;
    const proto = Object.getPrototypeOf(value);
    if (proto === null) return true;
    const Ctor = Object.prototype.hasOwnProperty.call(proto, "constructor") && proto.constructor;
    return typeof Ctor === "function" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);
}
// pkg/dist-src/util/merge-deep.js
function mergeDeep(defaults, options) {
    const result = Object.assign({}, defaults);
    Object.keys(options).forEach((key)=>{
        if (isPlainObject(options[key])) {
            if (!(key in defaults)) Object.assign(result, {
                [key]: options[key]
            });
            else result[key] = mergeDeep(defaults[key], options[key]);
        } else {
            Object.assign(result, {
                [key]: options[key]
            });
        }
    });
    return result;
}
// pkg/dist-src/util/remove-undefined-properties.js
function removeUndefinedProperties(obj) {
    for(const key in obj){
        if (obj[key] === void 0) {
            delete obj[key];
        }
    }
    return obj;
}
// pkg/dist-src/merge.js
function merge(defaults, route, options) {
    if (typeof route === "string") {
        let [method, url] = route.split(" ");
        options = Object.assign(url ? {
            method,
            url
        } : {
            url: method
        }, options);
    } else {
        options = Object.assign({}, route);
    }
    options.headers = lowercaseKeys(options.headers);
    removeUndefinedProperties(options);
    removeUndefinedProperties(options.headers);
    const mergedOptions = mergeDeep(defaults || {}, options);
    if (options.url === "/graphql") {
        if (defaults && defaults.mediaType.previews?.length) {
            mergedOptions.mediaType.previews = defaults.mediaType.previews.filter((preview)=>!mergedOptions.mediaType.previews.includes(preview)).concat(mergedOptions.mediaType.previews);
        }
        mergedOptions.mediaType.previews = (mergedOptions.mediaType.previews || []).map((preview)=>preview.replace(/-preview/, ""));
    }
    return mergedOptions;
}
// pkg/dist-src/util/add-query-parameters.js
function addQueryParameters(url, parameters) {
    const separator = /\?/.test(url) ? "&" : "?";
    const names = Object.keys(parameters);
    if (names.length === 0) {
        return url;
    }
    return url + separator + names.map((name)=>{
        if (name === "q") {
            return "q=" + parameters.q.split("+").map(encodeURIComponent).join("+");
        }
        return `${name}=${encodeURIComponent(parameters[name])}`;
    }).join("&");
}
// pkg/dist-src/util/extract-url-variable-names.js
var urlVariableRegex = /\{[^{}}]+\}/g;
function removeNonChars(variableName) {
    return variableName.replace(/(?:^\W+)|(?:(?<!\W)\W+$)/g, "").split(/,/);
}
function extractUrlVariableNames(url) {
    const matches = url.match(urlVariableRegex);
    if (!matches) {
        return [];
    }
    return matches.map(removeNonChars).reduce((a, b)=>a.concat(b), []);
}
// pkg/dist-src/util/omit.js
function omit(object, keysToOmit) {
    const result = {
        __proto__: null
    };
    for (const key of Object.keys(object)){
        if (keysToOmit.indexOf(key) === -1) {
            result[key] = object[key];
        }
    }
    return result;
}
// pkg/dist-src/util/url-template.js
function encodeReserved(str) {
    return str.split(/(%[0-9A-Fa-f]{2})/g).map(function(part) {
        if (!/%[0-9A-Fa-f]/.test(part)) {
            part = encodeURI(part).replace(/%5B/g, "[").replace(/%5D/g, "]");
        }
        return part;
    }).join("");
}
function encodeUnreserved(str) {
    return encodeURIComponent(str).replace(/[!'()*]/g, function(c) {
        return "%" + c.charCodeAt(0).toString(16).toUpperCase();
    });
}
function encodeValue(operator, value, key) {
    value = operator === "+" || operator === "#" ? encodeReserved(value) : encodeUnreserved(value);
    if (key) {
        return encodeUnreserved(key) + "=" + value;
    } else {
        return value;
    }
}
function isDefined(value) {
    return value !== void 0 && value !== null;
}
function isKeyOperator(operator) {
    return operator === ";" || operator === "&" || operator === "?";
}
function getValues(context, operator, key, modifier) {
    var value = context[key], result = [];
    if (isDefined(value) && value !== "") {
        if (typeof value === "string" || typeof value === "number" || typeof value === "boolean") {
            value = value.toString();
            if (modifier && modifier !== "*") {
                value = value.substring(0, parseInt(modifier, 10));
            }
            result.push(encodeValue(operator, value, isKeyOperator(operator) ? key : ""));
        } else {
            if (modifier === "*") {
                if (Array.isArray(value)) {
                    value.filter(isDefined).forEach(function(value2) {
                        result.push(encodeValue(operator, value2, isKeyOperator(operator) ? key : ""));
                    });
                } else {
                    Object.keys(value).forEach(function(k) {
                        if (isDefined(value[k])) {
                            result.push(encodeValue(operator, value[k], k));
                        }
                    });
                }
            } else {
                const tmp = [];
                if (Array.isArray(value)) {
                    value.filter(isDefined).forEach(function(value2) {
                        tmp.push(encodeValue(operator, value2));
                    });
                } else {
                    Object.keys(value).forEach(function(k) {
                        if (isDefined(value[k])) {
                            tmp.push(encodeUnreserved(k));
                            tmp.push(encodeValue(operator, value[k].toString()));
                        }
                    });
                }
                if (isKeyOperator(operator)) {
                    result.push(encodeUnreserved(key) + "=" + tmp.join(","));
                } else if (tmp.length !== 0) {
                    result.push(tmp.join(","));
                }
            }
        }
    } else {
        if (operator === ";") {
            if (isDefined(value)) {
                result.push(encodeUnreserved(key));
            }
        } else if (value === "" && (operator === "&" || operator === "?")) {
            result.push(encodeUnreserved(key) + "=");
        } else if (value === "") {
            result.push("");
        }
    }
    return result;
}
function parseUrl(template) {
    return {
        expand: expand.bind(null, template)
    };
}
function expand(template, context) {
    var operators = [
        "+",
        "#",
        ".",
        "/",
        ";",
        "?",
        "&"
    ];
    template = template.replace(/\{([^\{\}]+)\}|([^\{\}]+)/g, function(_, expression, literal) {
        if (expression) {
            let operator = "";
            const values = [];
            if (operators.indexOf(expression.charAt(0)) !== -1) {
                operator = expression.charAt(0);
                expression = expression.substr(1);
            }
            expression.split(/,/g).forEach(function(variable) {
                var tmp = /([^:\*]*)(?::(\d+)|(\*))?/.exec(variable);
                values.push(getValues(context, operator, tmp[1], tmp[2] || tmp[3]));
            });
            if (operator && operator !== "+") {
                var separator = ",";
                if (operator === "?") {
                    separator = "&";
                } else if (operator !== "#") {
                    separator = operator;
                }
                return (values.length !== 0 ? operator : "") + values.join(separator);
            } else {
                return values.join(",");
            }
        } else {
            return encodeReserved(literal);
        }
    });
    if (template === "/") {
        return template;
    } else {
        return template.replace(/\/$/, "");
    }
}
// pkg/dist-src/parse.js
function parse(options) {
    let method = options.method.toUpperCase();
    let url = (options.url || "/").replace(/:([a-z]\w+)/g, "{$1}");
    let headers = Object.assign({}, options.headers);
    let body;
    let parameters = omit(options, [
        "method",
        "baseUrl",
        "url",
        "headers",
        "request",
        "mediaType"
    ]);
    const urlVariableNames = extractUrlVariableNames(url);
    url = parseUrl(url).expand(parameters);
    if (!/^http/.test(url)) {
        url = options.baseUrl + url;
    }
    const omittedParameters = Object.keys(options).filter((option)=>urlVariableNames.includes(option)).concat("baseUrl");
    const remainingParameters = omit(parameters, omittedParameters);
    const isBinaryRequest = /application\/octet-stream/i.test(headers.accept);
    if (!isBinaryRequest) {
        if (options.mediaType.format) {
            headers.accept = headers.accept.split(/,/).map((format)=>format.replace(/application\/vnd(\.\w+)(\.v3)?(\.\w+)?(\+json)?$/, `application/vnd$1$2.${options.mediaType.format}`)).join(",");
        }
        if (url.endsWith("/graphql")) {
            if (options.mediaType.previews?.length) {
                const previewsFromAcceptHeader = headers.accept.match(/(?<![\w-])[\w-]+(?=-preview)/g) || [];
                headers.accept = previewsFromAcceptHeader.concat(options.mediaType.previews).map((preview)=>{
                    const format = options.mediaType.format ? `.${options.mediaType.format}` : "+json";
                    return `application/vnd.github.${preview}-preview${format}`;
                }).join(",");
            }
        }
    }
    if ([
        "GET",
        "HEAD"
    ].includes(method)) {
        url = addQueryParameters(url, remainingParameters);
    } else {
        if ("data" in remainingParameters) {
            body = remainingParameters.data;
        } else {
            if (Object.keys(remainingParameters).length) {
                body = remainingParameters;
            }
        }
    }
    if (!headers["content-type"] && typeof body !== "undefined") {
        headers["content-type"] = "application/json; charset=utf-8";
    }
    if ([
        "PATCH",
        "PUT"
    ].includes(method) && typeof body === "undefined") {
        body = "";
    }
    return Object.assign({
        method,
        url,
        headers
    }, typeof body !== "undefined" ? {
        body
    } : null, options.request ? {
        request: options.request
    } : null);
}
// pkg/dist-src/endpoint-with-defaults.js
function endpointWithDefaults(defaults, route, options) {
    return parse(merge(defaults, route, options));
}
// pkg/dist-src/with-defaults.js
function withDefaults(oldDefaults, newDefaults) {
    const DEFAULTS2 = merge(oldDefaults, newDefaults);
    const endpoint2 = endpointWithDefaults.bind(null, DEFAULTS2);
    return Object.assign(endpoint2, {
        DEFAULTS: DEFAULTS2,
        defaults: withDefaults.bind(null, DEFAULTS2),
        merge: merge.bind(null, DEFAULTS2),
        parse
    });
}
// pkg/dist-src/index.js
var endpoint = withDefaults(null, DEFAULTS);
;
}),
"[project]/node_modules/fast-content-type-parse/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
const NullObject = function NullObject() {};
NullObject.prototype = Object.create(null);
/**
 * RegExp to match *( ";" parameter ) in RFC 7231 sec *******
 *
 * parameter     = token "=" ( token / quoted-string )
 * token         = 1*tchar
 * tchar         = "!" / "#" / "$" / "%" / "&" / "'" / "*"
 *               / "+" / "-" / "." / "^" / "_" / "`" / "|" / "~"
 *               / DIGIT / ALPHA
 *               ; any VCHAR, except delimiters
 * quoted-string = DQUOTE *( qdtext / quoted-pair ) DQUOTE
 * qdtext        = HTAB / SP / %x21 / %x23-5B / %x5D-7E / obs-text
 * obs-text      = %x80-FF
 * quoted-pair   = "\" ( HTAB / SP / VCHAR / obs-text )
 */ const paramRE = /; *([!#$%&'*+.^\w`|~-]+)=("(?:[\v\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\v\u0020-\u00ff])*"|[!#$%&'*+.^\w`|~-]+) */gu;
/**
 * RegExp to match quoted-pair in RFC 7230 sec 3.2.6
 *
 * quoted-pair = "\" ( HTAB / SP / VCHAR / obs-text )
 * obs-text    = %x80-FF
 */ const quotedPairRE = /\\([\v\u0020-\u00ff])/gu;
/**
 * RegExp to match type in RFC 7231 sec *******
 *
 * media-type = type "/" subtype
 * type       = token
 * subtype    = token
 */ const mediaTypeRE = /^[!#$%&'*+.^\w|~-]+\/[!#$%&'*+.^\w|~-]+$/u;
// default ContentType to prevent repeated object creation
const defaultContentType = {
    type: '',
    parameters: new NullObject()
};
Object.freeze(defaultContentType.parameters);
Object.freeze(defaultContentType);
/**
 * Parse media type to object.
 *
 * @param {string|object} header
 * @return {Object}
 * @public
 */ function parse(header) {
    if (typeof header !== 'string') {
        throw new TypeError('argument header is required and must be a string');
    }
    let index = header.indexOf(';');
    const type = index !== -1 ? header.slice(0, index).trim() : header.trim();
    if (mediaTypeRE.test(type) === false) {
        throw new TypeError('invalid media type');
    }
    const result = {
        type: type.toLowerCase(),
        parameters: new NullObject()
    };
    // parse parameters
    if (index === -1) {
        return result;
    }
    let key;
    let match;
    let value;
    paramRE.lastIndex = index;
    while(match = paramRE.exec(header)){
        if (match.index !== index) {
            throw new TypeError('invalid parameter format');
        }
        index += match[0].length;
        key = match[1].toLowerCase();
        value = match[2];
        if (value[0] === '"') {
            // remove quotes and escapes
            value = value.slice(1, value.length - 1);
            quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'));
        }
        result.parameters[key] = value;
    }
    if (index !== header.length) {
        throw new TypeError('invalid parameter format');
    }
    return result;
}
function safeParse(header) {
    if (typeof header !== 'string') {
        return defaultContentType;
    }
    let index = header.indexOf(';');
    const type = index !== -1 ? header.slice(0, index).trim() : header.trim();
    if (mediaTypeRE.test(type) === false) {
        return defaultContentType;
    }
    const result = {
        type: type.toLowerCase(),
        parameters: new NullObject()
    };
    // parse parameters
    if (index === -1) {
        return result;
    }
    let key;
    let match;
    let value;
    paramRE.lastIndex = index;
    while(match = paramRE.exec(header)){
        if (match.index !== index) {
            return defaultContentType;
        }
        index += match[0].length;
        key = match[1].toLowerCase();
        value = match[2];
        if (value[0] === '"') {
            // remove quotes and escapes
            value = value.slice(1, value.length - 1);
            quotedPairRE.test(value) && (value = value.replace(quotedPairRE, '$1'));
        }
        result.parameters[key] = value;
    }
    if (index !== header.length) {
        return defaultContentType;
    }
    return result;
}
module.exports.default = {
    parse,
    safeParse
};
module.exports.parse = parse;
module.exports.safeParse = safeParse;
module.exports.defaultContentType = defaultContentType;
}}),
"[project]/node_modules/@octokit/request-error/dist-src/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "RequestError": ()=>RequestError
});
class RequestError extends Error {
    name;
    /**
   * http status code
   */ status;
    /**
   * Request options that lead to the error.
   */ request;
    /**
   * Response object if a response was received
   */ response;
    constructor(message, statusCode, options){
        super(message);
        this.name = "HttpError";
        this.status = Number.parseInt(statusCode);
        if (Number.isNaN(this.status)) {
            this.status = 0;
        }
        if ("response" in options) {
            this.response = options.response;
        }
        const requestCopy = Object.assign({}, options.request);
        if (options.request.headers.authorization) {
            requestCopy.headers = Object.assign({}, options.request.headers, {
                authorization: options.request.headers.authorization.replace(/(?<! ) .*$/, " [REDACTED]")
            });
        }
        requestCopy.url = requestCopy.url.replace(/\bclient_secret=\w+/g, "client_secret=[REDACTED]").replace(/\baccess_token=\w+/g, "access_token=[REDACTED]");
        this.request = requestCopy;
    }
}
;
}),
"[project]/node_modules/@octokit/request/dist-bundle/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// pkg/dist-src/index.js
__turbopack_context__.s({
    "request": ()=>request
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$endpoint$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/endpoint/dist-bundle/index.js [app-route] (ecmascript)");
// pkg/dist-src/defaults.js
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/universal-user-agent/index.js [app-route] (ecmascript)");
// pkg/dist-src/fetch-wrapper.js
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fast$2d$content$2d$type$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fast-content-type-parse/index.js [app-route] (ecmascript)");
// pkg/dist-src/fetch-wrapper.js
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2d$error$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/request-error/dist-src/index.js [app-route] (ecmascript)");
;
;
// pkg/dist-src/version.js
var VERSION = "10.0.3";
// pkg/dist-src/defaults.js
var defaults_default = {
    headers: {
        "user-agent": `octokit-request.js/${VERSION} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserAgent"])()}`
    }
};
;
// pkg/dist-src/is-plain-object.js
function isPlainObject(value) {
    if (typeof value !== "object" || value === null) return false;
    if (Object.prototype.toString.call(value) !== "[object Object]") return false;
    const proto = Object.getPrototypeOf(value);
    if (proto === null) return true;
    const Ctor = Object.prototype.hasOwnProperty.call(proto, "constructor") && proto.constructor;
    return typeof Ctor === "function" && Ctor instanceof Ctor && Function.prototype.call(Ctor) === Function.prototype.call(value);
}
;
async function fetchWrapper(requestOptions) {
    const fetch = requestOptions.request?.fetch || globalThis.fetch;
    if (!fetch) {
        throw new Error("fetch is not set. Please pass a fetch implementation as new Octokit({ request: { fetch }}). Learn more at https://github.com/octokit/octokit.js/#fetch-missing");
    }
    const log = requestOptions.request?.log || console;
    const parseSuccessResponseBody = requestOptions.request?.parseSuccessResponseBody !== false;
    const body = isPlainObject(requestOptions.body) || Array.isArray(requestOptions.body) ? JSON.stringify(requestOptions.body) : requestOptions.body;
    const requestHeaders = Object.fromEntries(Object.entries(requestOptions.headers).map(([name, value])=>[
            name,
            String(value)
        ]));
    let fetchResponse;
    try {
        fetchResponse = await fetch(requestOptions.url, {
            method: requestOptions.method,
            body,
            redirect: requestOptions.request?.redirect,
            headers: requestHeaders,
            signal: requestOptions.request?.signal,
            // duplex must be set if request.body is ReadableStream or Async Iterables.
            // See https://fetch.spec.whatwg.org/#dom-requestinit-duplex.
            ...requestOptions.body && {
                duplex: "half"
            }
        });
    } catch (error) {
        let message = "Unknown Error";
        if (error instanceof Error) {
            if (error.name === "AbortError") {
                error.status = 500;
                throw error;
            }
            message = error.message;
            if (error.name === "TypeError" && "cause" in error) {
                if (error.cause instanceof Error) {
                    message = error.cause.message;
                } else if (typeof error.cause === "string") {
                    message = error.cause;
                }
            }
        }
        const requestError = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2d$error$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RequestError"](message, 500, {
            request: requestOptions
        });
        requestError.cause = error;
        throw requestError;
    }
    const status = fetchResponse.status;
    const url = fetchResponse.url;
    const responseHeaders = {};
    for (const [key, value] of fetchResponse.headers){
        responseHeaders[key] = value;
    }
    const octokitResponse = {
        url,
        status,
        headers: responseHeaders,
        data: ""
    };
    if ("deprecation" in responseHeaders) {
        const matches = responseHeaders.link && responseHeaders.link.match(/<([^<>]+)>; rel="deprecation"/);
        const deprecationLink = matches && matches.pop();
        log.warn(`[@octokit/request] "${requestOptions.method} ${requestOptions.url}" is deprecated. It is scheduled to be removed on ${responseHeaders.sunset}${deprecationLink ? `. See ${deprecationLink}` : ""}`);
    }
    if (status === 204 || status === 205) {
        return octokitResponse;
    }
    if (requestOptions.method === "HEAD") {
        if (status < 400) {
            return octokitResponse;
        }
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2d$error$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RequestError"](fetchResponse.statusText, status, {
            response: octokitResponse,
            request: requestOptions
        });
    }
    if (status === 304) {
        octokitResponse.data = await getResponseData(fetchResponse);
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2d$error$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RequestError"]("Not modified", status, {
            response: octokitResponse,
            request: requestOptions
        });
    }
    if (status >= 400) {
        octokitResponse.data = await getResponseData(fetchResponse);
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2d$error$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["RequestError"](toErrorMessage(octokitResponse.data), status, {
            response: octokitResponse,
            request: requestOptions
        });
    }
    octokitResponse.data = parseSuccessResponseBody ? await getResponseData(fetchResponse) : fetchResponse.body;
    return octokitResponse;
}
async function getResponseData(response) {
    const contentType = response.headers.get("content-type");
    if (!contentType) {
        return response.text().catch(()=>"");
    }
    const mimetype = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fast$2d$content$2d$type$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safeParse"])(contentType);
    if (isJSONResponse(mimetype)) {
        let text = "";
        try {
            text = await response.text();
            return JSON.parse(text);
        } catch (err) {
            return text;
        }
    } else if (mimetype.type.startsWith("text/") || mimetype.parameters.charset?.toLowerCase() === "utf-8") {
        return response.text().catch(()=>"");
    } else {
        return response.arrayBuffer().catch(()=>new ArrayBuffer(0));
    }
}
function isJSONResponse(mimetype) {
    return mimetype.type === "application/json" || mimetype.type === "application/scim+json";
}
function toErrorMessage(data) {
    if (typeof data === "string") {
        return data;
    }
    if (data instanceof ArrayBuffer) {
        return "Unknown error";
    }
    if ("message" in data) {
        const suffix = "documentation_url" in data ? ` - ${data.documentation_url}` : "";
        return Array.isArray(data.errors) ? `${data.message}: ${data.errors.map((v)=>JSON.stringify(v)).join(", ")}${suffix}` : `${data.message}${suffix}`;
    }
    return `Unknown error: ${JSON.stringify(data)}`;
}
// pkg/dist-src/with-defaults.js
function withDefaults(oldEndpoint, newDefaults) {
    const endpoint2 = oldEndpoint.defaults(newDefaults);
    const newApi = function(route, parameters) {
        const endpointOptions = endpoint2.merge(route, parameters);
        if (!endpointOptions.request || !endpointOptions.request.hook) {
            return fetchWrapper(endpoint2.parse(endpointOptions));
        }
        const request2 = (route2, parameters2)=>{
            return fetchWrapper(endpoint2.parse(endpoint2.merge(route2, parameters2)));
        };
        Object.assign(request2, {
            endpoint: endpoint2,
            defaults: withDefaults.bind(null, endpoint2)
        });
        return endpointOptions.request.hook(request2, endpointOptions);
    };
    return Object.assign(newApi, {
        endpoint: endpoint2,
        defaults: withDefaults.bind(null, endpoint2)
    });
}
// pkg/dist-src/index.js
var request = withDefaults(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$endpoint$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["endpoint"], defaults_default);
;
}),
"[project]/node_modules/@octokit/graphql/dist-bundle/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// pkg/dist-src/index.js
__turbopack_context__.s({
    "GraphqlResponseError": ()=>GraphqlResponseError,
    "graphql": ()=>graphql2,
    "withCustomRequest": ()=>withCustomRequest
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/request/dist-bundle/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/universal-user-agent/index.js [app-route] (ecmascript)");
;
;
// pkg/dist-src/version.js
var VERSION = "0.0.0-development";
;
;
// pkg/dist-src/error.js
function _buildMessageForResponseErrors(data) {
    return `Request failed due to following response errors:
` + data.errors.map((e)=>` - ${e.message}`).join("\n");
}
var GraphqlResponseError = class extends Error {
    constructor(request2, headers, response){
        super(_buildMessageForResponseErrors(response));
        this.request = request2;
        this.headers = headers;
        this.response = response;
        this.errors = response.errors;
        this.data = response.data;
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
    name = "GraphqlResponseError";
    errors;
    data;
};
// pkg/dist-src/graphql.js
var NON_VARIABLE_OPTIONS = [
    "method",
    "baseUrl",
    "url",
    "headers",
    "request",
    "query",
    "mediaType",
    "operationName"
];
var FORBIDDEN_VARIABLE_OPTIONS = [
    "query",
    "method",
    "url"
];
var GHES_V3_SUFFIX_REGEX = /\/api\/v3\/?$/;
function graphql(request2, query, options) {
    if (options) {
        if (typeof query === "string" && "query" in options) {
            return Promise.reject(new Error(`[@octokit/graphql] "query" cannot be used as variable name`));
        }
        for(const key in options){
            if (!FORBIDDEN_VARIABLE_OPTIONS.includes(key)) continue;
            return Promise.reject(new Error(`[@octokit/graphql] "${key}" cannot be used as variable name`));
        }
    }
    const parsedOptions = typeof query === "string" ? Object.assign({
        query
    }, options) : query;
    const requestOptions = Object.keys(parsedOptions).reduce((result, key)=>{
        if (NON_VARIABLE_OPTIONS.includes(key)) {
            result[key] = parsedOptions[key];
            return result;
        }
        if (!result.variables) {
            result.variables = {};
        }
        result.variables[key] = parsedOptions[key];
        return result;
    }, {});
    const baseUrl = parsedOptions.baseUrl || request2.endpoint.DEFAULTS.baseUrl;
    if (GHES_V3_SUFFIX_REGEX.test(baseUrl)) {
        requestOptions.url = baseUrl.replace(GHES_V3_SUFFIX_REGEX, "/api/graphql");
    }
    return request2(requestOptions).then((response)=>{
        if (response.data.errors) {
            const headers = {};
            for (const key of Object.keys(response.headers)){
                headers[key] = response.headers[key];
            }
            throw new GraphqlResponseError(requestOptions, headers, response.data);
        }
        return response.data.data;
    });
}
// pkg/dist-src/with-defaults.js
function withDefaults(request2, newDefaults) {
    const newRequest = request2.defaults(newDefaults);
    const newApi = (query, options)=>{
        return graphql(newRequest, query, options);
    };
    return Object.assign(newApi, {
        defaults: withDefaults.bind(null, newRequest),
        endpoint: newRequest.endpoint
    });
}
// pkg/dist-src/index.js
var graphql2 = withDefaults(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["request"], {
    headers: {
        "user-agent": `octokit-graphql.js/${VERSION} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserAgent"])()}`
    },
    method: "POST",
    url: "/graphql"
});
function withCustomRequest(customRequest) {
    return withDefaults(customRequest, {
        method: "POST",
        url: "/graphql"
    });
}
;
}),
"[project]/node_modules/@octokit/auth-token/dist-bundle/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// pkg/dist-src/is-jwt.js
__turbopack_context__.s({
    "createTokenAuth": ()=>createTokenAuth
});
var b64url = "(?:[a-zA-Z0-9_-]+)";
var sep = "\\.";
var jwtRE = new RegExp(`^${b64url}${sep}${b64url}${sep}${b64url}$`);
var isJWT = jwtRE.test.bind(jwtRE);
// pkg/dist-src/auth.js
async function auth(token) {
    const isApp = isJWT(token);
    const isInstallation = token.startsWith("v1.") || token.startsWith("ghs_");
    const isUserToServer = token.startsWith("ghu_");
    const tokenType = isApp ? "app" : isInstallation ? "installation" : isUserToServer ? "user-to-server" : "oauth";
    return {
        type: "token",
        token,
        tokenType
    };
}
// pkg/dist-src/with-authorization-prefix.js
function withAuthorizationPrefix(token) {
    if (token.split(/\./).length === 3) {
        return `bearer ${token}`;
    }
    return `token ${token}`;
}
// pkg/dist-src/hook.js
async function hook(token, request, route, parameters) {
    const endpoint = request.endpoint.merge(route, parameters);
    endpoint.headers.authorization = withAuthorizationPrefix(token);
    return request(endpoint);
}
// pkg/dist-src/index.js
var createTokenAuth = function createTokenAuth2(token) {
    if (!token) {
        throw new Error("[@octokit/auth-token] No token passed to createTokenAuth");
    }
    if (typeof token !== "string") {
        throw new Error("[@octokit/auth-token] Token passed to createTokenAuth is not a string");
    }
    token = token.replace(/^(token|bearer) +/i, "");
    return Object.assign(auth.bind(null, token), {
        hook: hook.bind(null, token)
    });
};
;
}),
"[project]/node_modules/@octokit/core/dist-src/version.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "VERSION": ()=>VERSION
});
const VERSION = "7.0.3";
;
}),
"[project]/node_modules/@octokit/core/dist-src/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Octokit": ()=>Octokit
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/universal-user-agent/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/before-after-hook/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/request/dist-bundle/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$graphql$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/graphql/dist-bundle/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$auth$2d$token$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/auth-token/dist-bundle/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$core$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/core/dist-src/version.js [app-route] (ecmascript)");
;
;
;
;
;
;
const noop = ()=>{};
const consoleWarn = console.warn.bind(console);
const consoleError = console.error.bind(console);
function createLogger(logger = {}) {
    if (typeof logger.debug !== "function") {
        logger.debug = noop;
    }
    if (typeof logger.info !== "function") {
        logger.info = noop;
    }
    if (typeof logger.warn !== "function") {
        logger.warn = consoleWarn;
    }
    if (typeof logger.error !== "function") {
        logger.error = consoleError;
    }
    return logger;
}
const userAgentTrail = `octokit-core.js/${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$core$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VERSION"]} ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$universal$2d$user$2d$agent$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getUserAgent"])()}`;
class Octokit {
    static VERSION = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$core$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VERSION"];
    static defaults(defaults) {
        const OctokitWithDefaults = class extends this {
            constructor(...args){
                const options = args[0] || {};
                if (typeof defaults === "function") {
                    super(defaults(options));
                    return;
                }
                super(Object.assign({}, defaults, options, options.userAgent && defaults.userAgent ? {
                    userAgent: `${options.userAgent} ${defaults.userAgent}`
                } : null));
            }
        };
        return OctokitWithDefaults;
    }
    static plugins = [];
    /**
   * Attach a plugin (or many) to your Octokit instance.
   *
   * @example
   * const API = Octokit.plugin(plugin1, plugin2, plugin3, ...)
   */ static plugin(...newPlugins) {
        const currentPlugins = this.plugins;
        const NewOctokit = class extends this {
            static plugins = currentPlugins.concat(newPlugins.filter((plugin)=>!currentPlugins.includes(plugin)));
        };
        return NewOctokit;
    }
    constructor(options = {}){
        const hook = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$before$2d$after$2d$hook$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].Collection();
        const requestDefaults = {
            baseUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["request"].endpoint.DEFAULTS.baseUrl,
            headers: {},
            request: Object.assign({}, options.request, {
                // @ts-ignore internal usage only, no need to type
                hook: hook.bind(null, "request")
            }),
            mediaType: {
                previews: [],
                format: ""
            }
        };
        requestDefaults.headers["user-agent"] = options.userAgent ? `${options.userAgent} ${userAgentTrail}` : userAgentTrail;
        if (options.baseUrl) {
            requestDefaults.baseUrl = options.baseUrl;
        }
        if (options.previews) {
            requestDefaults.mediaType.previews = options.previews;
        }
        if (options.timeZone) {
            requestDefaults.headers["time-zone"] = options.timeZone;
        }
        this.request = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$request$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["request"].defaults(requestDefaults);
        this.graphql = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$graphql$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withCustomRequest"])(this.request).defaults(requestDefaults);
        this.log = createLogger(options.log);
        this.hook = hook;
        if (!options.authStrategy) {
            if (!options.auth) {
                this.auth = async ()=>({
                        type: "unauthenticated"
                    });
            } else {
                const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$auth$2d$token$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createTokenAuth"])(options.auth);
                hook.wrap("request", auth.hook);
                this.auth = auth;
            }
        } else {
            const { authStrategy, ...otherOptions } = options;
            const auth = authStrategy(Object.assign({
                request: this.request,
                log: this.log,
                // we pass the current octokit instance as well as its constructor options
                // to allow for authentication strategies that return a new octokit instance
                // that shares the same internal state as the current one. The original
                // requirement for this was the "event-octokit" authentication strategy
                // of https://github.com/probot/octokit-auth-probot.
                octokit: this,
                octokitOptions: otherOptions
            }, options.auth));
            hook.wrap("request", auth.hook);
            this.auth = auth;
        }
        const classConstructor = this.constructor;
        for(let i = 0; i < classConstructor.plugins.length; ++i){
            Object.assign(this, classConstructor.plugins[i](this, options));
        }
    }
    // assigned during constructor
    request;
    graphql;
    log;
    hook;
    // TODO: type `octokit.auth` based on passed options.authStrategy
    auth;
}
;
}),
"[project]/node_modules/@octokit/plugin-request-log/dist-src/version.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "VERSION": ()=>VERSION
});
const VERSION = "6.0.0";
;
}),
"[project]/node_modules/@octokit/plugin-request-log/dist-src/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "requestLog": ()=>requestLog
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$request$2d$log$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/plugin-request-log/dist-src/version.js [app-route] (ecmascript)");
;
function requestLog(octokit) {
    octokit.hook.wrap("request", (request, options)=>{
        octokit.log.debug("request", options);
        const start = Date.now();
        const requestOptions = octokit.request.endpoint.parse(options);
        const path = requestOptions.url.replace(options.baseUrl, "");
        return request(options).then((response)=>{
            const requestId = response.headers["x-github-request-id"];
            octokit.log.info(`${requestOptions.method} ${path} - ${response.status} with id ${requestId} in ${Date.now() - start}ms`);
            return response;
        }).catch((error)=>{
            const requestId = error.response?.headers["x-github-request-id"] || "UNKNOWN";
            octokit.log.error(`${requestOptions.method} ${path} - ${error.status} with id ${requestId} in ${Date.now() - start}ms`);
            throw error;
        });
    });
}
requestLog.VERSION = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$request$2d$log$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VERSION"];
;
}),
"[project]/node_modules/@octokit/plugin-paginate-rest/dist-bundle/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// pkg/dist-src/version.js
__turbopack_context__.s({
    "composePaginateRest": ()=>composePaginateRest,
    "isPaginatingEndpoint": ()=>isPaginatingEndpoint,
    "paginateRest": ()=>paginateRest,
    "paginatingEndpoints": ()=>paginatingEndpoints
});
var VERSION = "0.0.0-development";
// pkg/dist-src/normalize-paginated-list-response.js
function normalizePaginatedListResponse(response) {
    if (!response.data) {
        return {
            ...response,
            data: []
        };
    }
    const responseNeedsNormalization = ("total_count" in response.data || "total_commits" in response.data) && !("url" in response.data);
    if (!responseNeedsNormalization) return response;
    const incompleteResults = response.data.incomplete_results;
    const repositorySelection = response.data.repository_selection;
    const totalCount = response.data.total_count;
    const totalCommits = response.data.total_commits;
    delete response.data.incomplete_results;
    delete response.data.repository_selection;
    delete response.data.total_count;
    delete response.data.total_commits;
    const namespaceKey = Object.keys(response.data)[0];
    const data = response.data[namespaceKey];
    response.data = data;
    if (typeof incompleteResults !== "undefined") {
        response.data.incomplete_results = incompleteResults;
    }
    if (typeof repositorySelection !== "undefined") {
        response.data.repository_selection = repositorySelection;
    }
    response.data.total_count = totalCount;
    response.data.total_commits = totalCommits;
    return response;
}
// pkg/dist-src/iterator.js
function iterator(octokit, route, parameters) {
    const options = typeof route === "function" ? route.endpoint(parameters) : octokit.request.endpoint(route, parameters);
    const requestMethod = typeof route === "function" ? route : octokit.request;
    const method = options.method;
    const headers = options.headers;
    let url = options.url;
    return {
        [Symbol.asyncIterator]: ()=>({
                async next () {
                    if (!url) return {
                        done: true
                    };
                    try {
                        const response = await requestMethod({
                            method,
                            url,
                            headers
                        });
                        const normalizedResponse = normalizePaginatedListResponse(response);
                        url = ((normalizedResponse.headers.link || "").match(/<([^<>]+)>;\s*rel="next"/) || [])[1];
                        if (!url && "total_commits" in normalizedResponse.data) {
                            const parsedUrl = new URL(normalizedResponse.url);
                            const params = parsedUrl.searchParams;
                            const page = parseInt(params.get("page") || "1", 10);
                            const per_page = parseInt(params.get("per_page") || "250", 10);
                            if (page * per_page < normalizedResponse.data.total_commits) {
                                params.set("page", String(page + 1));
                                url = parsedUrl.toString();
                            }
                        }
                        return {
                            value: normalizedResponse
                        };
                    } catch (error) {
                        if (error.status !== 409) throw error;
                        url = "";
                        return {
                            value: {
                                status: 200,
                                headers: {},
                                data: []
                            }
                        };
                    }
                }
            })
    };
}
// pkg/dist-src/paginate.js
function paginate(octokit, route, parameters, mapFn) {
    if (typeof parameters === "function") {
        mapFn = parameters;
        parameters = void 0;
    }
    return gather(octokit, [], iterator(octokit, route, parameters)[Symbol.asyncIterator](), mapFn);
}
function gather(octokit, results, iterator2, mapFn) {
    return iterator2.next().then((result)=>{
        if (result.done) {
            return results;
        }
        let earlyExit = false;
        function done() {
            earlyExit = true;
        }
        results = results.concat(mapFn ? mapFn(result.value, done) : result.value.data);
        if (earlyExit) {
            return results;
        }
        return gather(octokit, results, iterator2, mapFn);
    });
}
// pkg/dist-src/compose-paginate.js
var composePaginateRest = Object.assign(paginate, {
    iterator
});
// pkg/dist-src/generated/paginating-endpoints.js
var paginatingEndpoints = [
    "GET /advisories",
    "GET /app/hook/deliveries",
    "GET /app/installation-requests",
    "GET /app/installations",
    "GET /assignments/{assignment_id}/accepted_assignments",
    "GET /classrooms",
    "GET /classrooms/{classroom_id}/assignments",
    "GET /enterprises/{enterprise}/code-security/configurations",
    "GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories",
    "GET /enterprises/{enterprise}/dependabot/alerts",
    "GET /enterprises/{enterprise}/secret-scanning/alerts",
    "GET /events",
    "GET /gists",
    "GET /gists/public",
    "GET /gists/starred",
    "GET /gists/{gist_id}/comments",
    "GET /gists/{gist_id}/commits",
    "GET /gists/{gist_id}/forks",
    "GET /installation/repositories",
    "GET /issues",
    "GET /licenses",
    "GET /marketplace_listing/plans",
    "GET /marketplace_listing/plans/{plan_id}/accounts",
    "GET /marketplace_listing/stubbed/plans",
    "GET /marketplace_listing/stubbed/plans/{plan_id}/accounts",
    "GET /networks/{owner}/{repo}/events",
    "GET /notifications",
    "GET /organizations",
    "GET /orgs/{org}/actions/cache/usage-by-repository",
    "GET /orgs/{org}/actions/hosted-runners",
    "GET /orgs/{org}/actions/permissions/repositories",
    "GET /orgs/{org}/actions/runner-groups",
    "GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners",
    "GET /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories",
    "GET /orgs/{org}/actions/runner-groups/{runner_group_id}/runners",
    "GET /orgs/{org}/actions/runners",
    "GET /orgs/{org}/actions/secrets",
    "GET /orgs/{org}/actions/secrets/{secret_name}/repositories",
    "GET /orgs/{org}/actions/variables",
    "GET /orgs/{org}/actions/variables/{name}/repositories",
    "GET /orgs/{org}/attestations/{subject_digest}",
    "GET /orgs/{org}/blocks",
    "GET /orgs/{org}/campaigns",
    "GET /orgs/{org}/code-scanning/alerts",
    "GET /orgs/{org}/code-security/configurations",
    "GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories",
    "GET /orgs/{org}/codespaces",
    "GET /orgs/{org}/codespaces/secrets",
    "GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories",
    "GET /orgs/{org}/copilot/billing/seats",
    "GET /orgs/{org}/copilot/metrics",
    "GET /orgs/{org}/dependabot/alerts",
    "GET /orgs/{org}/dependabot/secrets",
    "GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories",
    "GET /orgs/{org}/events",
    "GET /orgs/{org}/failed_invitations",
    "GET /orgs/{org}/hooks",
    "GET /orgs/{org}/hooks/{hook_id}/deliveries",
    "GET /orgs/{org}/insights/api/route-stats/{actor_type}/{actor_id}",
    "GET /orgs/{org}/insights/api/subject-stats",
    "GET /orgs/{org}/insights/api/user-stats/{user_id}",
    "GET /orgs/{org}/installations",
    "GET /orgs/{org}/invitations",
    "GET /orgs/{org}/invitations/{invitation_id}/teams",
    "GET /orgs/{org}/issues",
    "GET /orgs/{org}/members",
    "GET /orgs/{org}/members/{username}/codespaces",
    "GET /orgs/{org}/migrations",
    "GET /orgs/{org}/migrations/{migration_id}/repositories",
    "GET /orgs/{org}/organization-roles/{role_id}/teams",
    "GET /orgs/{org}/organization-roles/{role_id}/users",
    "GET /orgs/{org}/outside_collaborators",
    "GET /orgs/{org}/packages",
    "GET /orgs/{org}/packages/{package_type}/{package_name}/versions",
    "GET /orgs/{org}/personal-access-token-requests",
    "GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories",
    "GET /orgs/{org}/personal-access-tokens",
    "GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories",
    "GET /orgs/{org}/private-registries",
    "GET /orgs/{org}/projects",
    "GET /orgs/{org}/properties/values",
    "GET /orgs/{org}/public_members",
    "GET /orgs/{org}/repos",
    "GET /orgs/{org}/rulesets",
    "GET /orgs/{org}/rulesets/rule-suites",
    "GET /orgs/{org}/rulesets/{ruleset_id}/history",
    "GET /orgs/{org}/secret-scanning/alerts",
    "GET /orgs/{org}/security-advisories",
    "GET /orgs/{org}/settings/network-configurations",
    "GET /orgs/{org}/team/{team_slug}/copilot/metrics",
    "GET /orgs/{org}/teams",
    "GET /orgs/{org}/teams/{team_slug}/discussions",
    "GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments",
    "GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions",
    "GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions",
    "GET /orgs/{org}/teams/{team_slug}/invitations",
    "GET /orgs/{org}/teams/{team_slug}/members",
    "GET /orgs/{org}/teams/{team_slug}/projects",
    "GET /orgs/{org}/teams/{team_slug}/repos",
    "GET /orgs/{org}/teams/{team_slug}/teams",
    "GET /projects/columns/{column_id}/cards",
    "GET /projects/{project_id}/collaborators",
    "GET /projects/{project_id}/columns",
    "GET /repos/{owner}/{repo}/actions/artifacts",
    "GET /repos/{owner}/{repo}/actions/caches",
    "GET /repos/{owner}/{repo}/actions/organization-secrets",
    "GET /repos/{owner}/{repo}/actions/organization-variables",
    "GET /repos/{owner}/{repo}/actions/runners",
    "GET /repos/{owner}/{repo}/actions/runs",
    "GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts",
    "GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs",
    "GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs",
    "GET /repos/{owner}/{repo}/actions/secrets",
    "GET /repos/{owner}/{repo}/actions/variables",
    "GET /repos/{owner}/{repo}/actions/workflows",
    "GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs",
    "GET /repos/{owner}/{repo}/activity",
    "GET /repos/{owner}/{repo}/assignees",
    "GET /repos/{owner}/{repo}/attestations/{subject_digest}",
    "GET /repos/{owner}/{repo}/branches",
    "GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations",
    "GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs",
    "GET /repos/{owner}/{repo}/code-scanning/alerts",
    "GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances",
    "GET /repos/{owner}/{repo}/code-scanning/analyses",
    "GET /repos/{owner}/{repo}/codespaces",
    "GET /repos/{owner}/{repo}/codespaces/devcontainers",
    "GET /repos/{owner}/{repo}/codespaces/secrets",
    "GET /repos/{owner}/{repo}/collaborators",
    "GET /repos/{owner}/{repo}/comments",
    "GET /repos/{owner}/{repo}/comments/{comment_id}/reactions",
    "GET /repos/{owner}/{repo}/commits",
    "GET /repos/{owner}/{repo}/commits/{commit_sha}/comments",
    "GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls",
    "GET /repos/{owner}/{repo}/commits/{ref}/check-runs",
    "GET /repos/{owner}/{repo}/commits/{ref}/check-suites",
    "GET /repos/{owner}/{repo}/commits/{ref}/status",
    "GET /repos/{owner}/{repo}/commits/{ref}/statuses",
    "GET /repos/{owner}/{repo}/compare/{basehead}",
    "GET /repos/{owner}/{repo}/compare/{base}...{head}",
    "GET /repos/{owner}/{repo}/contributors",
    "GET /repos/{owner}/{repo}/dependabot/alerts",
    "GET /repos/{owner}/{repo}/dependabot/secrets",
    "GET /repos/{owner}/{repo}/deployments",
    "GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses",
    "GET /repos/{owner}/{repo}/environments",
    "GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies",
    "GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps",
    "GET /repos/{owner}/{repo}/environments/{environment_name}/secrets",
    "GET /repos/{owner}/{repo}/environments/{environment_name}/variables",
    "GET /repos/{owner}/{repo}/events",
    "GET /repos/{owner}/{repo}/forks",
    "GET /repos/{owner}/{repo}/hooks",
    "GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries",
    "GET /repos/{owner}/{repo}/invitations",
    "GET /repos/{owner}/{repo}/issues",
    "GET /repos/{owner}/{repo}/issues/comments",
    "GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions",
    "GET /repos/{owner}/{repo}/issues/events",
    "GET /repos/{owner}/{repo}/issues/{issue_number}/comments",
    "GET /repos/{owner}/{repo}/issues/{issue_number}/events",
    "GET /repos/{owner}/{repo}/issues/{issue_number}/labels",
    "GET /repos/{owner}/{repo}/issues/{issue_number}/reactions",
    "GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues",
    "GET /repos/{owner}/{repo}/issues/{issue_number}/timeline",
    "GET /repos/{owner}/{repo}/keys",
    "GET /repos/{owner}/{repo}/labels",
    "GET /repos/{owner}/{repo}/milestones",
    "GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels",
    "GET /repos/{owner}/{repo}/notifications",
    "GET /repos/{owner}/{repo}/pages/builds",
    "GET /repos/{owner}/{repo}/projects",
    "GET /repos/{owner}/{repo}/pulls",
    "GET /repos/{owner}/{repo}/pulls/comments",
    "GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions",
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/comments",
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/commits",
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/files",
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews",
    "GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments",
    "GET /repos/{owner}/{repo}/releases",
    "GET /repos/{owner}/{repo}/releases/{release_id}/assets",
    "GET /repos/{owner}/{repo}/releases/{release_id}/reactions",
    "GET /repos/{owner}/{repo}/rules/branches/{branch}",
    "GET /repos/{owner}/{repo}/rulesets",
    "GET /repos/{owner}/{repo}/rulesets/rule-suites",
    "GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history",
    "GET /repos/{owner}/{repo}/secret-scanning/alerts",
    "GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations",
    "GET /repos/{owner}/{repo}/security-advisories",
    "GET /repos/{owner}/{repo}/stargazers",
    "GET /repos/{owner}/{repo}/subscribers",
    "GET /repos/{owner}/{repo}/tags",
    "GET /repos/{owner}/{repo}/teams",
    "GET /repos/{owner}/{repo}/topics",
    "GET /repositories",
    "GET /search/code",
    "GET /search/commits",
    "GET /search/issues",
    "GET /search/labels",
    "GET /search/repositories",
    "GET /search/topics",
    "GET /search/users",
    "GET /teams/{team_id}/discussions",
    "GET /teams/{team_id}/discussions/{discussion_number}/comments",
    "GET /teams/{team_id}/discussions/{discussion_number}/comments/{comment_number}/reactions",
    "GET /teams/{team_id}/discussions/{discussion_number}/reactions",
    "GET /teams/{team_id}/invitations",
    "GET /teams/{team_id}/members",
    "GET /teams/{team_id}/projects",
    "GET /teams/{team_id}/repos",
    "GET /teams/{team_id}/teams",
    "GET /user/blocks",
    "GET /user/codespaces",
    "GET /user/codespaces/secrets",
    "GET /user/emails",
    "GET /user/followers",
    "GET /user/following",
    "GET /user/gpg_keys",
    "GET /user/installations",
    "GET /user/installations/{installation_id}/repositories",
    "GET /user/issues",
    "GET /user/keys",
    "GET /user/marketplace_purchases",
    "GET /user/marketplace_purchases/stubbed",
    "GET /user/memberships/orgs",
    "GET /user/migrations",
    "GET /user/migrations/{migration_id}/repositories",
    "GET /user/orgs",
    "GET /user/packages",
    "GET /user/packages/{package_type}/{package_name}/versions",
    "GET /user/public_emails",
    "GET /user/repos",
    "GET /user/repository_invitations",
    "GET /user/social_accounts",
    "GET /user/ssh_signing_keys",
    "GET /user/starred",
    "GET /user/subscriptions",
    "GET /user/teams",
    "GET /users",
    "GET /users/{username}/attestations/{subject_digest}",
    "GET /users/{username}/events",
    "GET /users/{username}/events/orgs/{org}",
    "GET /users/{username}/events/public",
    "GET /users/{username}/followers",
    "GET /users/{username}/following",
    "GET /users/{username}/gists",
    "GET /users/{username}/gpg_keys",
    "GET /users/{username}/keys",
    "GET /users/{username}/orgs",
    "GET /users/{username}/packages",
    "GET /users/{username}/projects",
    "GET /users/{username}/received_events",
    "GET /users/{username}/received_events/public",
    "GET /users/{username}/repos",
    "GET /users/{username}/social_accounts",
    "GET /users/{username}/ssh_signing_keys",
    "GET /users/{username}/starred",
    "GET /users/{username}/subscriptions"
];
// pkg/dist-src/paginating-endpoints.js
function isPaginatingEndpoint(arg) {
    if (typeof arg === "string") {
        return paginatingEndpoints.includes(arg);
    } else {
        return false;
    }
}
// pkg/dist-src/index.js
function paginateRest(octokit) {
    return {
        paginate: Object.assign(paginate.bind(null, octokit), {
            iterator: iterator.bind(null, octokit)
        })
    };
}
paginateRest.VERSION = VERSION;
;
}),
"[project]/node_modules/@octokit/rest/dist-src/version.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "VERSION": ()=>VERSION
});
const VERSION = "22.0.0";
;
}),
"[project]/node_modules/@octokit/rest/dist-src/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Octokit": ()=>Octokit
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$core$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/core/dist-src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$request$2d$log$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/plugin-request-log/dist-src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$paginate$2d$rest$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/plugin-paginate-rest/dist-bundle/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$rest$2d$endpoint$2d$methods$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/plugin-rest-endpoint-methods/dist-src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$rest$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/rest/dist-src/version.js [app-route] (ecmascript)");
;
;
;
;
;
const Octokit = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$core$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Octokit"].plugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$request$2d$log$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requestLog"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$rest$2d$endpoint$2d$methods$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["legacyRestEndpointMethods"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$plugin$2d$paginate$2d$rest$2f$dist$2d$bundle$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["paginateRest"]).defaults({
    userAgent: `octokit-rest.js/${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$rest$2f$dist$2d$src$2f$version$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["VERSION"]}`
});
;
}),

};

//# sourceMappingURL=node_modules_a055b1d7._.js.map