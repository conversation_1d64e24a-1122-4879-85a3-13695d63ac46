{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/lib/github.ts"], "sourcesContent": ["import { Octokit } from '@octokit/rest';\nimport { GitHubRepo } from '@/types';\n\nexport class GitHubService {\n  private octokit: Octokit;\n\n  constructor(token?: string) {\n    this.octokit = new Octokit({\n      auth: token,\n    });\n  }\n\n  async getRepository(owner: string, repo: string): Promise<GitHubRepo> {\n    try {\n      const { data } = await this.octokit.rest.repos.get({\n        owner,\n        repo,\n      });\n      return data as GitHubRepo;\n    } catch (error) {\n      throw new Error(`Failed to fetch repository: ${error}`);\n    }\n  }\n\n  async getRepositoryContents(\n    owner: string,\n    repo: string,\n    path: string = ''\n  ): Promise<Array<{\n    name: string;\n    path: string;\n    type: string;\n    size?: number;\n    content?: string;\n  }>> {\n    try {\n      const { data } = await this.octokit.rest.repos.getContent({\n        owner,\n        repo,\n        path,\n      });\n      return Array.isArray(data) ? data : [data];\n    } catch (error) {\n      throw new Error(`Failed to fetch repository contents: ${error}`);\n    }\n  }\n\n  async getFileContent(\n    owner: string,\n    repo: string,\n    path: string\n  ): Promise<string> {\n    try {\n      const { data } = await this.octokit.rest.repos.getContent({\n        owner,\n        repo,\n        path,\n      });\n      \n      if ('content' in data && data.content) {\n        return Buffer.from(data.content, 'base64').toString('utf-8');\n      }\n      throw new Error('File content not found');\n    } catch (error) {\n      throw new Error(`Failed to fetch file content: ${error}`);\n    }\n  }\n\n  async getRepositoryLanguages(\n    owner: string,\n    repo: string\n  ): Promise<Record<string, number>> {\n    try {\n      const { data } = await this.octokit.rest.repos.listLanguages({\n        owner,\n        repo,\n      });\n      return data;\n    } catch (error) {\n      throw new Error(`Failed to fetch repository languages: ${error}`);\n    }\n  }\n\n  parseRepoUrl(url: string): { owner: string; repo: string } | null {\n    const match = url.match(/github\\.com\\/([^\\/]+)\\/([^\\/]+)/);\n    if (match) {\n      return {\n        owner: match[1],\n        repo: match[2].replace(/\\.git$/, ''),\n      };\n    }\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM;IACH,QAAiB;IAEzB,YAAY,KAAc,CAAE;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,2JAAA,CAAA,UAAO,CAAC;YACzB,MAAM;QACR;IACF;IAEA,MAAM,cAAc,KAAa,EAAE,IAAY,EAAuB;QACpE,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBACjD;gBACA;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,OAAO;QACxD;IACF;IAEA,MAAM,sBACJ,KAAa,EACb,IAAY,EACZ,OAAe,EAAE,EAOf;QACF,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxD;gBACA;gBACA;YACF;YACA,OAAO,MAAM,OAAO,CAAC,QAAQ,OAAO;gBAAC;aAAK;QAC5C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,OAAO;QACjE;IACF;IAEA,MAAM,eACJ,KAAa,EACb,IAAY,EACZ,IAAY,EACK;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;gBACxD;gBACA;gBACA;YACF;YAEA,IAAI,aAAa,QAAQ,KAAK,OAAO,EAAE;gBACrC,OAAO,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,UAAU,QAAQ,CAAC;YACtD;YACA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,OAAO;QAC1D;IACF;IAEA,MAAM,uBACJ,KAAa,EACb,IAAY,EACqB;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;gBAC3D;gBACA;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,OAAO;QAClE;IACF;IAEA,aAAa,GAAW,EAA0C;QAChE,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,IAAI,OAAO;YACT,OAAO;gBACL,OAAO,KAAK,CAAC,EAAE;gBACf,MAAM,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU;YACnC;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/lib/codeAnalyzer.ts"], "sourcesContent": ["import { FileNode, DependencyEdge } from '@/types';\n\nexport class CodeAnalyzer {\n  private static readonly SUPPORTED_EXTENSIONS = {\n    '.js': 'javascript',\n    '.jsx': 'javascript',\n    '.ts': 'typescript',\n    '.tsx': 'typescript',\n    '.py': 'python',\n    '.java': 'java',\n    '.cpp': 'cpp',\n    '.c': 'c',\n    '.cs': 'csharp',\n    '.php': 'php',\n    '.rb': 'ruby',\n    '.go': 'go',\n    '.rs': 'rust',\n    '.swift': 'swift',\n    '.kt': 'kotlin',\n    '.scala': 'scala',\n    '.html': 'html',\n    '.css': 'css',\n    '.scss': 'scss',\n    '.sass': 'sass',\n    '.less': 'less',\n    '.json': 'json',\n    '.xml': 'xml',\n    '.yaml': 'yaml',\n    '.yml': 'yaml',\n    '.md': 'markdown',\n    '.sql': 'sql',\n  };\n\n  static getLanguageFromExtension(filename: string): string | undefined {\n    const ext = filename.substring(filename.lastIndexOf('.'));\n    return this.SUPPORTED_EXTENSIONS[ext as keyof typeof this.SUPPORTED_EXTENSIONS];\n  }\n\n  static extractDependencies(content: string, language: string): string[] {\n    const dependencies: string[] = [];\n\n    switch (language) {\n      case 'javascript':\n      case 'typescript':\n        dependencies.push(...this.extractJavaScriptDependencies(content));\n        break;\n      case 'python':\n        dependencies.push(...this.extractPythonDependencies(content));\n        break;\n      case 'java':\n        dependencies.push(...this.extractJavaDependencies(content));\n        break;\n      case 'cpp':\n      case 'c':\n        dependencies.push(...this.extractCDependencies(content));\n        break;\n      case 'csharp':\n        dependencies.push(...this.extractCSharpDependencies(content));\n        break;\n      case 'go':\n        dependencies.push(...this.extractGoDependencies(content));\n        break;\n      case 'rust':\n        dependencies.push(...this.extractRustDependencies(content));\n        break;\n      default:\n        break;\n    }\n\n    return [...new Set(dependencies)]; // Remove duplicates\n  }\n\n  private static extractJavaScriptDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // ES6 imports\n    const importRegex = /import\\s+(?:.*\\s+from\\s+)?['\"`]([^'\"`]+)['\"`]/g;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    // CommonJS requires\n    const requireRegex = /require\\s*\\(\\s*['\"`]([^'\"`]+)['\"`]\\s*\\)/g;\n    while ((match = requireRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    // Dynamic imports\n    const dynamicImportRegex = /import\\s*\\(\\s*['\"`]([^'\"`]+)['\"`]\\s*\\)/g;\n    while ((match = dynamicImportRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    return dependencies;\n  }\n\n  private static extractPythonDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // import statements\n    const importRegex = /^import\\s+([^\\s#]+)/gm;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      dependencies.push(match[1].split('.')[0]);\n    }\n\n    // from ... import statements\n    const fromImportRegex = /^from\\s+([^\\s#]+)\\s+import/gm;\n    while ((match = fromImportRegex.exec(content)) !== null) {\n      dependencies.push(match[1].split('.')[0]);\n    }\n\n    return dependencies;\n  }\n\n  private static extractJavaDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // import statements\n    const importRegex = /^import\\s+(?:static\\s+)?([^;]+);/gm;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      const packageName = match[1].trim();\n      dependencies.push(packageName);\n    }\n\n    return dependencies;\n  }\n\n  private static extractCDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // #include statements\n    const includeRegex = /#include\\s*[<\"]([^>\"]+)[>\"]/g;\n    let match;\n    while ((match = includeRegex.exec(content)) !== null) {\n      dependencies.push(match[1]);\n    }\n\n    return dependencies;\n  }\n\n  private static extractCSharpDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // using statements\n    const usingRegex = /^using\\s+([^;]+);/gm;\n    let match;\n    while ((match = usingRegex.exec(content)) !== null) {\n      dependencies.push(match[1].trim());\n    }\n\n    return dependencies;\n  }\n\n  private static extractGoDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // import statements\n    const importRegex = /import\\s+(?:\\(\\s*([^)]+)\\s*\\)|\"([^\"]+)\")/g;\n    let match;\n    while ((match = importRegex.exec(content)) !== null) {\n      if (match[1]) {\n        // Multi-line import\n        const imports = match[1].split('\\n').map(line => {\n          const trimmed = line.trim();\n          const quoted = trimmed.match(/\"([^\"]+)\"/);\n          return quoted ? quoted[1] : null;\n        }).filter(Boolean);\n        dependencies.push(...imports as string[]);\n      } else if (match[2]) {\n        // Single import\n        dependencies.push(match[2]);\n      }\n    }\n\n    return dependencies;\n  }\n\n  private static extractRustDependencies(content: string): string[] {\n    const dependencies: string[] = [];\n    \n    // use statements\n    const useRegex = /^use\\s+([^;]+);/gm;\n    let match;\n    while ((match = useRegex.exec(content)) !== null) {\n      const usePath = match[1].trim();\n      const rootModule = usePath.split('::')[0];\n      dependencies.push(rootModule);\n    }\n\n    // extern crate statements\n    const externRegex = /^extern\\s+crate\\s+([^;]+);/gm;\n    while ((match = externRegex.exec(content)) !== null) {\n      dependencies.push(match[1].trim());\n    }\n\n    return dependencies;\n  }\n\n  static createDependencyEdges(nodes: FileNode[]): DependencyEdge[] {\n    const edges: DependencyEdge[] = [];\n    const nodeMap = new Map(nodes.map(node => [node.path, node]));\n\n    nodes.forEach(node => {\n      if (node.dependencies) {\n        node.dependencies.forEach(dep => {\n          // Try to find the dependency in the current project\n          const targetNode = this.findDependencyTarget(dep, nodeMap, node.path);\n          if (targetNode) {\n            edges.push({\n              id: `${node.id}-${targetNode.id}`,\n              source: node.id,\n              target: targetNode.id,\n              type: 'import',\n              label: dep,\n            });\n          }\n        });\n      }\n    });\n\n    return edges;\n  }\n\n  private static findDependencyTarget(\n    dependency: string,\n    nodeMap: Map<string, FileNode>,\n    sourcePath: string\n  ): FileNode | null {\n    // Handle relative imports\n    if (dependency.startsWith('./') || dependency.startsWith('../')) {\n      const resolvedPath = this.resolvePath(sourcePath, dependency);\n      return nodeMap.get(resolvedPath) || null;\n    }\n\n    // Handle absolute imports within the project\n    for (const [path, node] of nodeMap) {\n      if (path.includes(dependency) || node.name.includes(dependency)) {\n        return node;\n      }\n    }\n\n    return null;\n  }\n\n  private static resolvePath(basePath: string, relativePath: string): string {\n    const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));\n    const parts = baseDir.split('/').concat(relativePath.split('/'));\n    const resolved: string[] = [];\n\n    parts.forEach(part => {\n      if (part === '..') {\n        resolved.pop();\n      } else if (part !== '.' && part !== '') {\n        resolved.push(part);\n      }\n    });\n\n    return resolved.join('/');\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAwB,uBAAuB;QAC7C,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,EAAE;IAEF,OAAO,yBAAyB,QAAgB,EAAsB;QACpE,MAAM,MAAM,SAAS,SAAS,CAAC,SAAS,WAAW,CAAC;QACpD,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAA8C;IACjF;IAEA,OAAO,oBAAoB,OAAe,EAAE,QAAgB,EAAY;QACtE,MAAM,eAAyB,EAAE;QAEjC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,6BAA6B,CAAC;gBACxD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC;gBACpD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAC;gBAClD;YACF,KAAK;YACL,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC;gBAC/C;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAC;gBACpD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC;gBAChD;YACF,KAAK;gBACH,aAAa,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAC;gBAClD;YACF;gBACE;QACJ;QAEA,OAAO;eAAI,IAAI,IAAI;SAAc,EAAE,oBAAoB;IACzD;IAEA,OAAe,8BAA8B,OAAe,EAAY;QACtE,MAAM,eAAyB,EAAE;QAEjC,cAAc;QACd,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,oBAAoB;QACpB,MAAM,eAAe;QACrB,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,QAAQ,MAAM,KAAM;YACpD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,kBAAkB;QAClB,MAAM,qBAAqB;QAC3B,MAAO,CAAC,QAAQ,mBAAmB,IAAI,CAAC,QAAQ,MAAM,KAAM;YAC1D,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,OAAO;IACT;IAEA,OAAe,0BAA0B,OAAe,EAAY;QAClE,MAAM,eAAyB,EAAE;QAEjC,oBAAoB;QACpB,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB;QACxB,MAAO,CAAC,QAAQ,gBAAgB,IAAI,CAAC,QAAQ,MAAM,KAAM;YACvD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1C;QAEA,OAAO;IACT;IAEA,OAAe,wBAAwB,OAAe,EAAY;QAChE,MAAM,eAAyB,EAAE;QAEjC,oBAAoB;QACpB,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,MAAM,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI;YACjC,aAAa,IAAI,CAAC;QACpB;QAEA,OAAO;IACT;IAEA,OAAe,qBAAqB,OAAe,EAAY;QAC7D,MAAM,eAAyB,EAAE;QAEjC,sBAAsB;QACtB,MAAM,eAAe;QACrB,IAAI;QACJ,MAAO,CAAC,QAAQ,aAAa,IAAI,CAAC,QAAQ,MAAM,KAAM;YACpD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5B;QAEA,OAAO;IACT;IAEA,OAAe,0BAA0B,OAAe,EAAY;QAClE,MAAM,eAAyB,EAAE;QAEjC,mBAAmB;QACnB,MAAM,aAAa;QACnB,IAAI;QACJ,MAAO,CAAC,QAAQ,WAAW,IAAI,CAAC,QAAQ,MAAM,KAAM;YAClD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QACjC;QAEA,OAAO;IACT;IAEA,OAAe,sBAAsB,OAAe,EAAY;QAC9D,MAAM,eAAyB,EAAE;QAEjC,oBAAoB;QACpB,MAAM,cAAc;QACpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,IAAI,KAAK,CAAC,EAAE,EAAE;gBACZ,oBAAoB;gBACpB,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;oBACvC,MAAM,UAAU,KAAK,IAAI;oBACzB,MAAM,SAAS,QAAQ,KAAK,CAAC;oBAC7B,OAAO,SAAS,MAAM,CAAC,EAAE,GAAG;gBAC9B,GAAG,MAAM,CAAC;gBACV,aAAa,IAAI,IAAI;YACvB,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE;gBACnB,gBAAgB;gBAChB,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE;YAC5B;QACF;QAEA,OAAO;IACT;IAEA,OAAe,wBAAwB,OAAe,EAAY;QAChE,MAAM,eAAyB,EAAE;QAEjC,iBAAiB;QACjB,MAAM,WAAW;QACjB,IAAI;QACJ,MAAO,CAAC,QAAQ,SAAS,IAAI,CAAC,QAAQ,MAAM,KAAM;YAChD,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,IAAI;YAC7B,MAAM,aAAa,QAAQ,KAAK,CAAC,KAAK,CAAC,EAAE;YACzC,aAAa,IAAI,CAAC;QACpB;QAEA,0BAA0B;QAC1B,MAAM,cAAc;QACpB,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,QAAQ,MAAM,KAAM;YACnD,aAAa,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;QACjC;QAEA,OAAO;IACT;IAEA,OAAO,sBAAsB,KAAiB,EAAoB;QAChE,MAAM,QAA0B,EAAE;QAClC,MAAM,UAAU,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ;gBAAC,KAAK,IAAI;gBAAE;aAAK;QAE3D,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,YAAY,EAAE;gBACrB,KAAK,YAAY,CAAC,OAAO,CAAC,CAAA;oBACxB,oDAAoD;oBACpD,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC,KAAK,SAAS,KAAK,IAAI;oBACpE,IAAI,YAAY;wBACd,MAAM,IAAI,CAAC;4BACT,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE;4BACjC,QAAQ,KAAK,EAAE;4BACf,QAAQ,WAAW,EAAE;4BACrB,MAAM;4BACN,OAAO;wBACT;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,qBACb,UAAkB,EAClB,OAA8B,EAC9B,UAAkB,EACD;QACjB,0BAA0B;QAC1B,IAAI,WAAW,UAAU,CAAC,SAAS,WAAW,UAAU,CAAC,QAAQ;YAC/D,MAAM,eAAe,IAAI,CAAC,WAAW,CAAC,YAAY;YAClD,OAAO,QAAQ,GAAG,CAAC,iBAAiB;QACtC;QAEA,6CAA6C;QAC7C,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,QAAS;YAClC,IAAI,KAAK,QAAQ,CAAC,eAAe,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa;gBAC/D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,OAAe,YAAY,QAAgB,EAAE,YAAoB,EAAU;QACzE,MAAM,UAAU,SAAS,SAAS,CAAC,GAAG,SAAS,WAAW,CAAC;QAC3D,MAAM,QAAQ,QAAQ,KAAK,CAAC,KAAK,MAAM,CAAC,aAAa,KAAK,CAAC;QAC3D,MAAM,WAAqB,EAAE;QAE7B,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,SAAS,MAAM;gBACjB,SAAS,GAAG;YACd,OAAO,IAAI,SAAS,OAAO,SAAS,IAAI;gBACtC,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;AACF", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { GitHubService } from '@/lib/github';\nimport { CodeAnalyzer } from '@/lib/codeAnalyzer';\nimport { AnalysisResult, FileNode, CodeGraph } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url, token } = await request.json();\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'Repository URL is required' },\n        { status: 400 }\n      );\n    }\n\n    const githubService = new GitHubService(token);\n    const repoInfo = githubService.parseRepoUrl(url);\n\n    if (!repoInfo) {\n      return NextResponse.json(\n        { error: 'Invalid GitHub URL format' },\n        { status: 400 }\n      );\n    }\n\n    const { owner, repo } = repoInfo;\n\n    try {\n      // Repository bilgilerini al\n      const repository = await githubService.getRepository(owner, repo);\n      const languages = await githubService.getRepositoryLanguages(owner, repo);\n\n      // Repository içeriğini analiz et\n      const nodes = await analyzeRepositoryContents(githubService, owner, repo);\n      const edges = CodeAnalyzer.createDependencyEdges(nodes);\n\n      const graph: CodeGraph = { nodes, edges };\n\n      const result: AnalysisResult = {\n        repo: repository,\n        graph,\n        languages,\n        stats: {\n          totalFiles: nodes.filter(n => n.type === 'file').length,\n          totalDirectories: nodes.filter(n => n.type === 'directory').length,\n          totalDependencies: edges.length,\n        },\n      };\n\n      return NextResponse.json(result);\n    } catch (error) {\n      console.error('GitHub API Error:', error);\n      return NextResponse.json(\n        { error: 'Failed to fetch repository data. Please check if the repository exists and is public.' },\n        { status: 404 }\n      );\n    }\n  } catch (error) {\n    console.error('Analysis Error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nasync function analyzeRepositoryContents(\n  githubService: GitHubService,\n  owner: string,\n  repo: string,\n  path: string = '',\n  depth: number = 0\n): Promise<FileNode[]> {\n  const nodes: FileNode[] = [];\n  const maxDepth = 3; // Maksimum derinlik sınırı\n  const maxFiles = 100; // Maksimum dosya sayısı sınırı\n\n  if (depth >= maxDepth) {\n    return nodes;\n  }\n\n  try {\n    const contents = await githubService.getRepositoryContents(owner, repo, path);\n    \n    // İçerikleri sırala: önce klasörler, sonra dosyalar\n    const sortedContents = contents.sort((a, b) => {\n      if (a.type === 'dir' && b.type === 'file') return -1;\n      if (a.type === 'file' && b.type === 'dir') return 1;\n      return a.name.localeCompare(b.name);\n    });\n\n    let fileCount = 0;\n\n    for (const item of sortedContents) {\n      if (fileCount >= maxFiles) break;\n\n      const nodeId = `${path}/${item.name}`.replace(/^\\//, '') || item.name;\n      const node: FileNode = {\n        id: nodeId,\n        name: item.name,\n        path: item.path,\n        type: item.type === 'dir' ? 'directory' : 'file',\n        size: item.size,\n      };\n\n      if (item.type === 'file') {\n        fileCount++;\n        const language = CodeAnalyzer.getLanguageFromExtension(item.name);\n        if (language) {\n          node.language = language;\n          \n          // Sadece küçük dosyaları analiz et (50KB'den küçük)\n          if (item.size && item.size < 50000) {\n            try {\n              const content = await githubService.getFileContent(owner, repo, item.path);\n              node.content = content;\n              node.dependencies = CodeAnalyzer.extractDependencies(content, language);\n            } catch (contentError) {\n              console.warn(`Could not fetch content for ${item.path}:`, contentError);\n            }\n          }\n        }\n      } else if (item.type === 'dir') {\n        // Belirli klasörleri atla\n        const skipDirs = ['node_modules', '.git', 'dist', 'build', '.next', 'coverage', '__pycache__'];\n        if (!skipDirs.includes(item.name)) {\n          const children = await analyzeRepositoryContents(\n            githubService,\n            owner,\n            repo,\n            item.path,\n            depth + 1\n          );\n          node.children = children;\n          nodes.push(...children);\n        }\n      }\n\n      nodes.push(node);\n    }\n  } catch (error) {\n    console.warn(`Could not analyze path ${path}:`, error);\n  }\n\n  return nodes;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,IAAI,sHAAA,CAAA,gBAAa,CAAC;QACxC,MAAM,WAAW,cAAc,YAAY,CAAC;QAE5C,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;QAExB,IAAI;YACF,4BAA4B;YAC5B,MAAM,aAAa,MAAM,cAAc,aAAa,CAAC,OAAO;YAC5D,MAAM,YAAY,MAAM,cAAc,sBAAsB,CAAC,OAAO;YAEpE,iCAAiC;YACjC,MAAM,QAAQ,MAAM,0BAA0B,eAAe,OAAO;YACpE,MAAM,QAAQ,4HAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC;YAEjD,MAAM,QAAmB;gBAAE;gBAAO;YAAM;YAExC,MAAM,SAAyB;gBAC7B,MAAM;gBACN;gBACA;gBACA,OAAO;oBACL,YAAY,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;oBACvD,kBAAkB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,MAAM;oBAClE,mBAAmB,MAAM,MAAM;gBACjC;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwF,GACjG;gBAAE,QAAQ;YAAI;QAElB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,0BACb,aAA4B,EAC5B,KAAa,EACb,IAAY,EACZ,OAAe,EAAE,EACjB,QAAgB,CAAC;IAEjB,MAAM,QAAoB,EAAE;IAC5B,MAAM,WAAW,GAAG,2BAA2B;IAC/C,MAAM,WAAW,KAAK,+BAA+B;IAErD,IAAI,SAAS,UAAU;QACrB,OAAO;IACT;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,cAAc,qBAAqB,CAAC,OAAO,MAAM;QAExE,oDAAoD;QACpD,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAC,GAAG;YACvC,IAAI,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,KAAK,QAAQ,OAAO,CAAC;YACnD,IAAI,EAAE,IAAI,KAAK,UAAU,EAAE,IAAI,KAAK,OAAO,OAAO;YAClD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC;QAEA,IAAI,YAAY;QAEhB,KAAK,MAAM,QAAQ,eAAgB;YACjC,IAAI,aAAa,UAAU;YAE3B,MAAM,SAAS,GAAG,KAAK,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,OAAO,KAAK,IAAI;YACrE,MAAM,OAAiB;gBACrB,IAAI;gBACJ,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI,KAAK,QAAQ,cAAc;gBAC1C,MAAM,KAAK,IAAI;YACjB;YAEA,IAAI,KAAK,IAAI,KAAK,QAAQ;gBACxB;gBACA,MAAM,WAAW,4HAAA,CAAA,eAAY,CAAC,wBAAwB,CAAC,KAAK,IAAI;gBAChE,IAAI,UAAU;oBACZ,KAAK,QAAQ,GAAG;oBAEhB,oDAAoD;oBACpD,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,OAAO;wBAClC,IAAI;4BACF,MAAM,UAAU,MAAM,cAAc,cAAc,CAAC,OAAO,MAAM,KAAK,IAAI;4BACzE,KAAK,OAAO,GAAG;4BACf,KAAK,YAAY,GAAG,4HAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,SAAS;wBAChE,EAAE,OAAO,cAAc;4BACrB,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;wBAC5D;oBACF;gBACF;YACF,OAAO,IAAI,KAAK,IAAI,KAAK,OAAO;gBAC9B,0BAA0B;gBAC1B,MAAM,WAAW;oBAAC;oBAAgB;oBAAQ;oBAAQ;oBAAS;oBAAS;oBAAY;iBAAc;gBAC9F,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK,IAAI,GAAG;oBACjC,MAAM,WAAW,MAAM,0BACrB,eACA,OACA,MACA,KAAK,IAAI,EACT,QAAQ;oBAEV,KAAK,QAAQ,GAAG;oBAChB,MAAM,IAAI,IAAI;gBAChB;YACF;YAEA,MAAM,IAAI,CAAC;QACb;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,EAAE;IAClD;IAEA,OAAO;AACT", "debugId": null}}]}