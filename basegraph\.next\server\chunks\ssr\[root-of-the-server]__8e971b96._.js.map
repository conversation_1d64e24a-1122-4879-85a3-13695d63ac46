{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { clsx } from \"clsx\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", ...props }, ref) => {\n    return (\n      <button\n        className={clsx(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n          {\n            \"bg-primary text-primary-foreground hover:bg-primary/90\": variant === \"default\",\n            \"bg-destructive text-destructive-foreground hover:bg-destructive/90\": variant === \"destructive\",\n            \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\": variant === \"outline\",\n            \"bg-secondary text-secondary-foreground hover:bg-secondary/80\": variant === \"secondary\",\n            \"hover:bg-accent hover:text-accent-foreground\": variant === \"ghost\",\n            \"text-primary underline-offset-4 hover:underline\": variant === \"link\",\n          },\n          {\n            \"h-10 px-4 py-2\": size === \"default\",\n            \"h-9 rounded-md px-3\": size === \"sm\",\n            \"h-11 rounded-md px-8\": size === \"lg\",\n            \"h-10 w-10\": size === \"icon\",\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { clsx } from \"clsx\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  variant?: 'default' | 'outline';\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={clsx(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/GitHubRepoInput.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { AnalysisResult } from '@/types';\nimport { Github, AlertCircle } from 'lucide-react';\n\ninterface GitHubRepoInputProps {\n  onAnalysisStart: () => void;\n  onAnalysisComplete: (result: AnalysisResult) => void;\n}\n\nexport function GitHubRepoInput({ onAnalysisStart, onAnalysisComplete }: GitHubRepoInputProps) {\n  const [url, setUrl] = useState('');\n  const [error, setError] = useState('');\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n\n  const analyzeRepository = async () => {\n    if (!url.trim()) {\n      setError('Lütfen bir GitHub repository URL&apos;si girin');\n      return;\n    }\n\n    setError('');\n    setIsAnalyzing(true);\n    onAnalysisStart();\n\n    try {\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Analiz sırasında bir hata oluştu');\n      }\n\n      const result: AnalysisResult = await response.json();\n      onAnalysisComplete(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Analiz sırasında bir hata oluştu');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex space-x-2\">\n        <div className=\"flex-1\">\n          <Input\n            type=\"url\"\n            placeholder=\"https://github.com/username/repository\"\n            value={url}\n            onChange={(e) => setUrl(e.target.value)}\n            className=\"w-full\"\n            disabled={isAnalyzing}\n          />\n        </div>\n        <Button\n          onClick={analyzeRepository}\n          disabled={isAnalyzing || !url.trim()}\n          className=\"flex items-center space-x-2\"\n        >\n          <Github className=\"w-4 h-4\" />\n          <span>{isAnalyzing ? 'Analiz Ediliyor...' : 'Analiz Et'}</span>\n        </Button>\n      </div>\n\n      {error && (\n        <div className=\"flex items-center space-x-2 text-red-600 dark:text-red-400\">\n          <AlertCircle className=\"w-4 h-4\" />\n          <span className=\"text-sm\">{error}</span>\n        </div>\n      )}\n\n      <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n        <p>\n          Örnek: https://github.com/facebook/react veya https://github.com/microsoft/vscode\n        </p>\n        <p className=\"mt-1\">\n          Not: Büyük repository&apos;ler için analiz biraz zaman alabilir.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AANA;;;;;;AAaO,SAAS,gBAAgB,EAAE,eAAe,EAAE,kBAAkB,EAAwB;IAC3F,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB;QACxB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,SAAS;YACT;QACF;QAEA,SAAS;QACT,eAAe;QACf;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAI;YAC7B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,SAAyB,MAAM,SAAS,IAAI;YAClD,mBAAmB;QACrB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;4BACtC,WAAU;4BACV,UAAU;;;;;;;;;;;kCAGd,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,eAAe,CAAC,IAAI,IAAI;wBAClC,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,cAAc,uBAAuB;;;;;;;;;;;;;;;;;;YAI/C,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAK,WAAU;kCAAW;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAE;;;;;;kCAGH,8OAAC;wBAAE,WAAU;kCAAO;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/nodes/FileNodeComponent.tsx"], "sourcesContent": ["'use client';\n\nimport { memo } from 'react';\nimport { <PERSON><PERSON>, Position } from '@xyflow/react';\nimport { FileNode } from '@/types';\nimport {\n  Code,\n  FileText,\n  Image as ImageIcon,\n  Settings,\n  GitBranch\n} from 'lucide-react';\nimport { clsx } from 'clsx';\n\nexport const FileNodeComponent = memo(({ data, selected }: { data: { fileNode: FileNode; isSelected: boolean; onSelect: (nodeId: string) => void }; selected?: boolean }) => {\n  const { fileNode, isSelected, onSelect } = data;\n\n  const getFileIcon = () => {\n    const extension = fileNode.name.split('.').pop()?.toLowerCase();\n    \n    switch (extension) {\n      case 'js':\n      case 'jsx':\n      case 'ts':\n      case 'tsx':\n      case 'py':\n      case 'java':\n      case 'cpp':\n      case 'c':\n      case 'cs':\n      case 'php':\n      case 'rb':\n      case 'go':\n      case 'rs':\n      case 'swift':\n      case 'kt':\n        return Code;\n      case 'png':\n      case 'jpg':\n      case 'jpeg':\n      case 'gif':\n      case 'svg':\n      case 'webp':\n        return ImageIcon;\n      case 'json':\n      case 'xml':\n      case 'yaml':\n      case 'yml':\n      case 'toml':\n      case 'ini':\n        return Settings;\n      default:\n        return FileText;\n    }\n  };\n\n  const getLanguageColor = (language?: string): string => {\n    const colors: Record<string, string> = {\n      javascript: 'border-yellow-400 bg-yellow-50',\n      typescript: 'border-blue-400 bg-blue-50',\n      python: 'border-green-400 bg-green-50',\n      java: 'border-red-400 bg-red-50',\n      cpp: 'border-purple-400 bg-purple-50',\n      c: 'border-gray-400 bg-gray-50',\n      csharp: 'border-indigo-400 bg-indigo-50',\n      php: 'border-purple-500 bg-purple-50',\n      ruby: 'border-red-500 bg-red-50',\n      go: 'border-cyan-400 bg-cyan-50',\n      rust: 'border-orange-400 bg-orange-50',\n      swift: 'border-orange-500 bg-orange-50',\n      kotlin: 'border-purple-600 bg-purple-50',\n      html: 'border-orange-500 bg-orange-50',\n      css: 'border-blue-500 bg-blue-50',\n      scss: 'border-pink-500 bg-pink-50',\n      json: 'border-yellow-500 bg-yellow-50',\n      markdown: 'border-gray-600 bg-gray-50',\n    };\n    return colors[language || ''] || 'border-gray-300 bg-white';\n  };\n\n  const Icon = getFileIcon();\n  const colorClass = getLanguageColor(fileNode.language);\n  const hasDependencies = fileNode.dependencies && fileNode.dependencies.length > 0;\n\n  return (\n    <div\n      className={clsx(\n        'px-4 py-3 shadow-md rounded-lg border-2 cursor-pointer transition-all duration-200 min-w-[180px] max-w-[220px]',\n        colorClass,\n        isSelected || selected \n          ? 'ring-2 ring-blue-500 ring-offset-2 shadow-lg scale-105' \n          : 'hover:shadow-lg hover:scale-102'\n      )}\n      onClick={() => onSelect(fileNode.id)}\n    >\n      {/* Input Handle */}\n      <Handle\n        type=\"target\"\n        position={Position.Top}\n        className=\"w-3 h-3 !bg-blue-500 !border-2 !border-white\"\n      />\n\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"flex-shrink-0 mt-0.5\">\n          <Icon className=\"w-5 h-5 text-gray-600\" />\n        </div>\n        \n        <div className=\"flex-1 min-w-0\">\n          <div className=\"text-sm font-medium text-gray-900 truncate\">\n            {fileNode.name}\n          </div>\n          \n          {fileNode.language && (\n            <div className=\"text-xs text-gray-600 mt-1\">\n              {fileNode.language}\n            </div>\n          )}\n          \n          {fileNode.size && (\n            <div className=\"text-xs text-gray-500 mt-1\">\n              {(fileNode.size / 1024).toFixed(1)} KB\n            </div>\n          )}\n          \n          {hasDependencies && (\n            <div className=\"flex items-center space-x-1 mt-2\">\n              <GitBranch className=\"w-3 h-3 text-blue-600\" />\n              <span className=\"text-xs text-blue-600 font-medium\">\n                {fileNode.dependencies!.length} bağımlılık\n              </span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Output Handle */}\n      <Handle\n        type=\"source\"\n        position={Position.Bottom}\n        className=\"w-3 h-3 !bg-green-500 !border-2 !border-white\"\n      />\n    </div>\n  );\n});\n\nFileNodeComponent.displayName = 'FileNodeComponent';\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAOA;AAZA;;;;;;AAcO,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAiH;IACtK,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IAE3C,MAAM,cAAc;QAClB,MAAM,YAAY,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QAElD,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;Y<PERSON><PERSON>,KAAK;g<PERSON><PERSON>,OAAO,kMAAA,CAAA,OAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,oMAAA,CAAA,QAAS;YAClB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,0MAAA,CAAA,WAAQ;YACjB;gBACE,OAAO,8MAAA,CAAA,WAAQ;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,MAAM;YACN,KAAK;YACL,GAAG;YACH,QAAQ;YACR,KAAK;YACL,MAAM;YACN,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,KAAK;YACL,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,YAAY,GAAG,IAAI;IACnC;IAEA,MAAM,OAAO;IACb,MAAM,aAAa,iBAAiB,SAAS,QAAQ;IACrD,MAAM,kBAAkB,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG;IAEhF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,kHACA,YACA,cAAc,WACV,2DACA;QAEN,SAAS,IAAM,SAAS,SAAS,EAAE;;0BAGnC,8OAAC,yKAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,0JAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,WAAU;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,SAAS,IAAI;;;;;;4BAGf,SAAS,QAAQ,kBAChB,8OAAC;gCAAI,WAAU;0CACZ,SAAS,QAAQ;;;;;;4BAIrB,SAAS,IAAI,kBACZ,8OAAC;gCAAI,WAAU;;oCACZ,CAAC,SAAS,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;4BAItC,iCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAK,WAAU;;4CACb,SAAS,YAAY,CAAE,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC,yKAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,0JAAA,CAAA,WAAQ,CAAC,MAAM;gBACzB,WAAU;;;;;;;;;;;;AAIlB;AAEA,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/CodeEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { FileNode } from '@/types';\nimport { Button } from '@/components/ui/button';\nimport { X, Save, Download } from 'lucide-react';\nimport { clsx } from 'clsx';\n\ninterface CodeEditorProps {\n  fileNode: FileNode;\n  onClose: () => void;\n  onSave?: (content: string) => void;\n}\n\nexport function CodeEditor({ fileNode, onClose, onSave }: CodeEditorProps) {\n  const [content, setContent] = useState(fileNode.content || '');\n  const [isModified, setIsModified] = useState(false);\n\n  const handleContentChange = (newContent: string) => {\n    setContent(newContent);\n    setIsModified(newContent !== fileNode.content);\n  };\n\n  const handleSave = () => {\n    if (onSave) {\n      onSave(content);\n    }\n    setIsModified(false);\n  };\n\n  const handleDownload = () => {\n    const blob = new Blob([content], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = fileNode.name;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const getLanguageClass = (language?: string): string => {\n    const classes: Record<string, string> = {\n      javascript: 'language-javascript',\n      typescript: 'language-typescript',\n      python: 'language-python',\n      java: 'language-java',\n      cpp: 'language-cpp',\n      c: 'language-c',\n      csharp: 'language-csharp',\n      php: 'language-php',\n      ruby: 'language-ruby',\n      go: 'language-go',\n      rust: 'language-rust',\n      html: 'language-html',\n      css: 'language-css',\n      json: 'language-json',\n      markdown: 'language-markdown',\n    };\n    return classes[language || ''] || 'language-text';\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-3\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {fileNode.name}\n            </h2>\n            {isModified && (\n              <span className=\"text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 px-2 py-1 rounded\">\n                Değiştirildi\n              </span>\n            )}\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleDownload}\n              className=\"flex items-center space-x-1\"\n            >\n              <Download className=\"w-4 h-4\" />\n              <span>İndir</span>\n            </Button>\n            {onSave && (\n              <Button\n                variant=\"default\"\n                size=\"sm\"\n                onClick={handleSave}\n                disabled={!isModified}\n                className=\"flex items-center space-x-1\"\n              >\n                <Save className=\"w-4 h-4\" />\n                <span>Kaydet</span>\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onClose}\n              className=\"flex items-center space-x-1\"\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* File Info */}\n        <div className=\"px-4 py-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400\">\n            <span>Yol: {fileNode.path}</span>\n            {fileNode.language && <span>Dil: {fileNode.language}</span>}\n            {fileNode.size && <span>Boyut: {(fileNode.size / 1024).toFixed(1)} KB</span>}\n            <span>Satırlar: {content.split('\\n').length}</span>\n          </div>\n        </div>\n\n        {/* Editor */}\n        <div className=\"flex-1 overflow-hidden\">\n          <textarea\n            value={content}\n            onChange={(e) => handleContentChange(e.target.value)}\n            className={clsx(\n              'w-full h-full p-4 font-mono text-sm resize-none border-none outline-none',\n              'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',\n              'focus:ring-0 focus:border-transparent',\n              getLanguageClass(fileNode.language)\n            )}\n            placeholder={fileNode.content ? '' : 'Dosya içeriği yüklenemedi...'}\n            readOnly={!fileNode.content}\n            spellCheck={false}\n            style={{\n              lineHeight: '1.5',\n              tabSize: 2,\n            }}\n          />\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-4 py-2 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-4\">\n              {fileNode.dependencies && fileNode.dependencies.length > 0 && (\n                <span>{fileNode.dependencies.length} bağımlılık</span>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span>UTF-8</span>\n              <span>LF</span>\n              {fileNode.language && <span>{fileNode.language.toUpperCase()}</span>}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AACA;AANA;;;;;;AAcO,SAAS,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAmB;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,IAAI;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,cAAc,eAAe,SAAS,OAAO;IAC/C;IAEA,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;QACA,cAAc;IAChB;IAEA,MAAM,iBAAiB;QACrB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAa;QACtD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,SAAS,IAAI;QAC1B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAkC;YACtC,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,MAAM;YACN,KAAK;YACL,GAAG;YACH,QAAQ;YACR,KAAK;YACL,MAAM;YACN,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,MAAM;YACN,UAAU;QACZ;QACA,OAAO,OAAO,CAAC,YAAY,GAAG,IAAI;IACpC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,SAAS,IAAI;;;;;;gCAEf,4BACC,8OAAC;oCAAK,WAAU;8CAAkG;;;;;;;;;;;;sCAKtH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;gCAEP,wBACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,CAAC;oCACX,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAGV,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCAAM,SAAS,IAAI;;;;;;;4BACxB,SAAS,QAAQ,kBAAI,8OAAC;;oCAAK;oCAAM,SAAS,QAAQ;;;;;;;4BAClD,SAAS,IAAI,kBAAI,8OAAC;;oCAAK;oCAAQ,CAAC,SAAS,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAClE,8OAAC;;oCAAK;oCAAW,QAAQ,KAAK,CAAC,MAAM,MAAM;;;;;;;;;;;;;;;;;;8BAK/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wBACnD,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,4EACA,8DACA,yCACA,iBAAiB,SAAS,QAAQ;wBAEpC,aAAa,SAAS,OAAO,GAAG,KAAK;wBACrC,UAAU,CAAC,SAAS,OAAO;wBAC3B,YAAY;wBACZ,OAAO;4BACL,YAAY;4BACZ,SAAS;wBACX;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,MAAM,GAAG,mBACvD,8OAAC;;wCAAM,SAAS,YAAY,CAAC,MAAM;wCAAC;;;;;;;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;oCACL,SAAS,QAAQ,kBAAI,8OAAC;kDAAM,SAAS,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/NodeGraphView.tsx"], "sourcesContent": ["'use client';\n\nimport { use<PERSON><PERSON>back, useMemo, useState } from 'react';\nimport {\n  ReactFlow,\n  Node,\n  Edge,\n  addEdge,\n  Connection,\n  useNodesState,\n  useEdgesState,\n  Controls,\n  MiniMap,\n  Background,\n  BackgroundVariant,\n} from '@xyflow/react';\nimport '@xyflow/react/dist/style.css';\n\nimport { AnalysisResult, FileNode } from '@/types';\nimport { FileNodeComponent } from './nodes/FileNodeComponent';\nimport { CodeEditor } from './CodeEditor';\n\ninterface NodeGraphViewProps {\n  analysisResult: AnalysisResult;\n}\n\nconst nodeTypes = {\n  fileNode: FileNodeComponent,\n};\n\nexport function NodeGraphView({ analysisResult }: NodeGraphViewProps) {\n  const [selectedNode, setSelectedNode] = useState<string | null>(null);\n  const [editingNode, setEditingNode] = useState<FileNode | null>(null);\n\n  // React Flow node'larını oluştur\n  const initialNodes: Node[] = useMemo(() => {\n    const nodes: Node[] = [];\n    const { graph } = analysisResult;\n\n    // Sadece dosyaları node olar<PERSON> (klasörleri değil)\n    const fileNodes = graph.nodes.filter(node => node.type === 'file');\n\n    fileNodes.forEach((fileNode, index) => {\n      // Grid layout için pozisyon hesapla\n      const cols = Math.ceil(Math.sqrt(fileNodes.length));\n      const row = Math.floor(index / cols);\n      const col = index % cols;\n      \n      const x = col * 250 + 50;\n      const y = row * 150 + 50;\n\n      nodes.push({\n        id: fileNode.id,\n        type: 'fileNode',\n        position: { x, y },\n        data: {\n          fileNode,\n          isSelected: false,\n          onSelect: (nodeId: string) => setSelectedNode(nodeId),\n        },\n        draggable: true,\n      });\n    });\n\n    return nodes;\n  }, [analysisResult]);\n\n  // React Flow edge'lerini oluştur\n  const initialEdges: Edge[] = useMemo(() => {\n    return analysisResult.graph.edges.map(edge => ({\n      id: edge.id,\n      source: edge.source,\n      target: edge.target,\n      type: 'smoothstep',\n      animated: true,\n      label: edge.label,\n      style: {\n        stroke: '#6366f1',\n        strokeWidth: 2,\n      },\n      labelStyle: {\n        fontSize: 10,\n        fontWeight: 600,\n      },\n      labelBgStyle: {\n        fill: '#ffffff',\n        fillOpacity: 0.8,\n      },\n    }));\n  }, [analysisResult]);\n\n  const [nodes, , onNodesChange] = useNodesState(initialNodes);\n  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n\n  const onConnect = useCallback(\n    (params: Connection) => setEdges((eds) => addEdge(params, eds)),\n    [setEdges]\n  );\n\n  // Node seçimi değiştiğinde node'ları güncelle\n  const updatedNodes = useMemo(() => {\n    return nodes.map(node => ({\n      ...node,\n      data: {\n        ...node.data,\n        isSelected: node.id === selectedNode,\n      },\n    }));\n  }, [nodes, selectedNode]);\n\n  const getLanguageColor = (language?: string): string => {\n    const colors: Record<string, string> = {\n      javascript: '#f7df1e',\n      typescript: '#3178c6',\n      python: '#3776ab',\n      java: '#ed8b00',\n      cpp: '#00599c',\n      c: '#a8b9cc',\n      csharp: '#239120',\n      php: '#777bb4',\n      ruby: '#cc342d',\n      go: '#00add8',\n      rust: '#dea584',\n      swift: '#fa7343',\n      kotlin: '#7f52ff',\n      html: '#e34f26',\n      css: '#1572b6',\n      scss: '#cf649a',\n      json: '#000000',\n      markdown: '#083fa1',\n    };\n    return colors[language || ''] || '#6b7280';\n  };\n\n  return (\n    <div className=\"h-[600px] w-full\">\n      <ReactFlow\n        nodes={updatedNodes}\n        edges={edges}\n        onNodesChange={onNodesChange}\n        onEdgesChange={onEdgesChange}\n        onConnect={onConnect}\n        nodeTypes={nodeTypes}\n        fitView\n        attributionPosition=\"bottom-left\"\n      >\n        <Controls />\n        <MiniMap \n          nodeColor={(node) => {\n            const fileNode = node.data?.fileNode as FileNode;\n            return getLanguageColor(fileNode?.language);\n          }}\n          nodeStrokeWidth={3}\n          zoomable\n          pannable\n        />\n        <Background \n          variant={BackgroundVariant.Dots} \n          gap={20} \n          size={1}\n          color=\"#e5e7eb\"\n        />\n      </ReactFlow>\n\n      {/* Selected Node Info Panel */}\n      {selectedNode && (\n        <div className=\"absolute top-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 max-h-96 overflow-y-auto\">\n          {(() => {\n            const node = analysisResult.graph.nodes.find(n => n.id === selectedNode);\n            if (!node) return null;\n\n            return (\n              <div className=\"space-y-3\">\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                    {node.name}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {node.path}\n                  </p>\n                </div>\n\n                {node.language && (\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Dil: \n                    </span>\n                    <span \n                      className=\"ml-2 text-sm px-2 py-1 rounded text-white\"\n                      style={{ backgroundColor: getLanguageColor(node.language) }}\n                    >\n                      {node.language}\n                    </span>\n                  </div>\n                )}\n\n                {node.size && (\n                  <div>\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Boyut: \n                    </span>\n                    <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">\n                      {(node.size / 1024).toFixed(1)} KB\n                    </span>\n                  </div>\n                )}\n\n                {node.dependencies && node.dependencies.length > 0 && (\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      Bağımlılıklar ({node.dependencies.length})\n                    </h4>\n                    <div className=\"space-y-1 max-h-32 overflow-y-auto\">\n                      {node.dependencies.map((dep, index) => (\n                        <div\n                          key={index}\n                          className=\"text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded\"\n                        >\n                          {dep}\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700\">\n                  {node.content && (\n                    <button\n                      onClick={() => setEditingNode(node)}\n                      className=\"flex-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 py-2\"\n                    >\n                      Düzenle\n                    </button>\n                  )}\n                  <button\n                    onClick={() => setSelectedNode(null)}\n                    className=\"flex-1 text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 py-2\"\n                  >\n                    Kapat\n                  </button>\n                </div>\n              </div>\n            );\n          })()}\n        </div>\n      )}\n\n      {/* Code Editor Modal */}\n      {editingNode && (\n        <CodeEditor\n          fileNode={editingNode}\n          onClose={() => setEditingNode(null)}\n          onSave={(content) => {\n            console.log('Saving content for', editingNode.name, content);\n            setEditingNode(null);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAgBA;AACA;AApBA;;;;;;;AA0BA,MAAM,YAAY;IAChB,UAAU,gJAAA,CAAA,oBAAiB;AAC7B;AAEO,SAAS,cAAc,EAAE,cAAc,EAAsB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAEhE,iCAAiC;IACjC,MAAM,eAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,QAAgB,EAAE;QACxB,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,yDAAyD;QACzD,MAAM,YAAY,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAE3D,UAAU,OAAO,CAAC,CAAC,UAAU;YAC3B,oCAAoC;YACpC,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,MAAM;YACjD,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ;YAC/B,MAAM,MAAM,QAAQ;YAEpB,MAAM,IAAI,MAAM,MAAM;YACtB,MAAM,IAAI,MAAM,MAAM;YAEtB,MAAM,IAAI,CAAC;gBACT,IAAI,SAAS,EAAE;gBACf,MAAM;gBACN,UAAU;oBAAE;oBAAG;gBAAE;gBACjB,MAAM;oBACJ;oBACA,YAAY;oBACZ,UAAU,CAAC,SAAmB,gBAAgB;gBAChD;gBACA,WAAW;YACb;QACF;QAEA,OAAO;IACT,GAAG;QAAC;KAAe;IAEnB,iCAAiC;IACjC,MAAM,eAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,OAAO,eAAe,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7C,IAAI,KAAK,EAAE;gBACX,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,MAAM;gBACN,UAAU;gBACV,OAAO,KAAK,KAAK;gBACjB,OAAO;oBACL,QAAQ;oBACR,aAAa;gBACf;gBACA,YAAY;oBACV,UAAU;oBACV,YAAY;gBACd;gBACA,cAAc;oBACZ,MAAM;oBACN,aAAa;gBACf;YACF,CAAC;IACH,GAAG;QAAC;KAAe;IAEnB,MAAM,CAAC,SAAS,cAAc,GAAG,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE;IAEvD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1B,CAAC,SAAuB,SAAS,CAAC,MAAQ,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAC1D;QAAC;KAAS;IAGZ,8CAA8C;IAC9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,EAAE,KAAK;gBAC1B;YACF,CAAC;IACH,GAAG;QAAC;QAAO;KAAa;IAExB,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,MAAM;YACN,KAAK;YACL,GAAG;YACH,QAAQ;YACR,KAAK;YACL,MAAM;YACN,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,KAAK;YACL,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,YAAY,GAAG,IAAI;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yKAAA,CAAA,YAAS;gBACR,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,eAAe;gBACf,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,qBAAoB;;kCAEpB,8OAAC,yKAAA,CAAA,WAAQ;;;;;kCACT,8OAAC,yKAAA,CAAA,UAAO;wBACN,WAAW,CAAC;4BACV,MAAM,WAAW,KAAK,IAAI,EAAE;4BAC5B,OAAO,iBAAiB,UAAU;wBACpC;wBACA,iBAAiB;wBACjB,QAAQ;wBACR,QAAQ;;;;;;kCAEV,8OAAC,yKAAA,CAAA,aAAU;wBACT,SAAS,yKAAA,CAAA,oBAAiB,CAAC,IAAI;wBAC/B,KAAK;wBACL,MAAM;wBACN,OAAM;;;;;;;;;;;;YAKT,8BACC,8OAAC;gBAAI,WAAU;0BACZ,CAAC;oBACA,MAAM,OAAO,eAAe,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC3D,IAAI,CAAC,MAAM,OAAO;oBAElB,qBACE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,KAAK,IAAI;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDACV,KAAK,IAAI;;;;;;;;;;;;4BAIb,KAAK,QAAQ,kBACZ,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,iBAAiB,KAAK,QAAQ;wCAAE;kDAEzD,KAAK,QAAQ;;;;;;;;;;;;4BAKnB,KAAK,IAAI,kBACR,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,8OAAC;wCAAK,WAAU;;4CACb,CAAC,KAAK,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;4BAKpC,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,mBAC/C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAA4D;4CACxD,KAAK,YAAY,CAAC,MAAM;4CAAC;;;;;;;kDAE3C,8OAAC;wCAAI,WAAU;kDACZ,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC3B,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;;;;;;;0CAUf,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,OAAO,kBACX,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;kDAIH,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;;;;;;;;;;;;;gBAMT,CAAC;;;;;;YAKJ,6BACC,8OAAC,gIAAA,CAAA,aAAU;gBACT,UAAU;gBACV,SAAS,IAAM,eAAe;gBAC9B,QAAQ,CAAC;oBACP,QAAQ,GAAG,CAAC,sBAAsB,YAAY,IAAI,EAAE;oBACpD,eAAe;gBACjB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/TreeView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { AnalysisResult, FileNode } from '@/types';\nimport { CodeEditor } from './CodeEditor';\nimport {\n  ChevronRight,\n  ChevronDown,\n  Folder,\n  FolderOpen,\n  Code,\n  FileText,\n  Image as ImageIcon,\n  Settings,\n  Edit\n} from 'lucide-react';\nimport { clsx } from 'clsx';\n\ninterface TreeViewProps {\n  analysisResult: AnalysisResult;\n}\n\ninterface TreeNodeProps {\n  node: FileNode;\n  level: number;\n  onNodeSelect: (node: FileNode) => void;\n  selectedNode: FileNode | null;\n}\n\nexport function TreeView({ analysisResult }: TreeViewProps) {\n  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());\n  const [selectedNode, setSelectedNode] = useState<FileNode | null>(null);\n  const [editingNode, setEditingNode] = useState<FileNode | null>(null);\n\n  const toggleNode = (nodeId: string) => {\n    const newExpanded = new Set(expandedNodes);\n    if (newExpanded.has(nodeId)) {\n      newExpanded.delete(nodeId);\n    } else {\n      newExpanded.add(nodeId);\n    }\n    setExpandedNodes(newExpanded);\n  };\n\n  const getFileIcon = (node: FileNode) => {\n    if (node.type === 'directory') {\n      return expandedNodes.has(node.id) ? FolderOpen : Folder;\n    }\n\n    const extension = node.name.split('.').pop()?.toLowerCase();\n    \n    switch (extension) {\n      case 'js':\n      case 'jsx':\n      case 'ts':\n      case 'tsx':\n      case 'py':\n      case 'java':\n      case 'cpp':\n      case 'c':\n      case 'cs':\n      case 'php':\n      case 'rb':\n      case 'go':\n      case 'rs':\n      case 'swift':\n      case 'kt':\n        return Code;\n      case 'png':\n      case 'jpg':\n      case 'jpeg':\n      case 'gif':\n      case 'svg':\n      case 'webp':\n        return ImageIcon;\n      case 'json':\n      case 'xml':\n      case 'yaml':\n      case 'yml':\n      case 'toml':\n      case 'ini':\n        return Settings;\n      default:\n        return FileText;\n    }\n  };\n\n  const getLanguageColor = (language?: string) => {\n    const colors: Record<string, string> = {\n      javascript: 'text-yellow-600',\n      typescript: 'text-blue-600',\n      python: 'text-green-600',\n      java: 'text-red-600',\n      cpp: 'text-purple-600',\n      c: 'text-gray-600',\n      csharp: 'text-indigo-600',\n      php: 'text-purple-500',\n      ruby: 'text-red-500',\n      go: 'text-cyan-600',\n      rust: 'text-orange-600',\n      swift: 'text-orange-500',\n      kotlin: 'text-purple-700',\n      html: 'text-orange-500',\n      css: 'text-blue-500',\n      scss: 'text-pink-500',\n      json: 'text-yellow-500',\n      markdown: 'text-gray-700',\n    };\n    return colors[language || ''] || 'text-gray-500';\n  };\n\n  const TreeNode = ({ node, level, onNodeSelect, selectedNode }: TreeNodeProps) => {\n    const isExpanded = expandedNodes.has(node.id);\n    const isSelected = selectedNode?.id === node.id;\n    const hasChildren = node.children && node.children.length > 0;\n    const Icon = getFileIcon(node);\n\n    return (\n      <div>\n        <div\n          className={clsx(\n            'flex items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded',\n            isSelected && 'bg-blue-100 dark:bg-blue-900',\n            'transition-colors duration-150'\n          )}\n          style={{ paddingLeft: `${level * 20 + 8}px` }}\n          onClick={() => {\n            if (node.type === 'directory' && hasChildren) {\n              toggleNode(node.id);\n            }\n            onNodeSelect(node);\n          }}\n        >\n          {node.type === 'directory' && hasChildren && (\n            <div className=\"mr-1\">\n              {isExpanded ? (\n                <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n              ) : (\n                <ChevronRight className=\"w-4 h-4 text-gray-400\" />\n              )}\n            </div>\n          )}\n          {(!hasChildren || node.type === 'file') && (\n            <div className=\"w-5 mr-1\" />\n          )}\n          \n          <Icon \n            className={clsx(\n              'w-4 h-4 mr-2',\n              node.language ? getLanguageColor(node.language) : 'text-gray-500'\n            )} \n          />\n          \n          <span className={clsx(\n            'text-sm truncate',\n            isSelected \n              ? 'text-blue-900 dark:text-blue-100 font-medium' \n              : 'text-gray-700 dark:text-gray-300'\n          )}>\n            {node.name}\n          </span>\n          \n          {node.dependencies && node.dependencies.length > 0 && (\n            <span className=\"ml-auto text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 px-1.5 py-0.5 rounded\">\n              {node.dependencies.length}\n            </span>\n          )}\n        </div>\n        \n        {node.type === 'directory' && hasChildren && isExpanded && (\n          <div>\n            {node.children!.map((child) => (\n              <TreeNode\n                key={child.id}\n                node={child}\n                level={level + 1}\n                onNodeSelect={onNodeSelect}\n                selectedNode={selectedNode}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  // Root seviyesindeki node'ları organize et\n  const rootNodes = analysisResult.graph.nodes.filter(node => \n    !node.path.includes('/') || node.path.split('/').length === 1\n  );\n\n  return (\n    <div className=\"flex h-[600px]\">\n      {/* Tree Panel */}\n      <div className=\"w-1/2 border-r border-gray-200 dark:border-gray-700 overflow-y-auto\">\n        <div className=\"p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Dosya Yapısı\n          </h3>\n          <div className=\"space-y-1\">\n            {rootNodes.map((node) => (\n              <TreeNode\n                key={node.id}\n                node={node}\n                level={0}\n                onNodeSelect={setSelectedNode}\n                selectedNode={selectedNode}\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Details Panel */}\n      <div className=\"w-1/2 p-4 overflow-y-auto\">\n        {selectedNode ? (\n          <div className=\"space-y-4\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {selectedNode.name}\n              </h3>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n                <p><strong>Yol:</strong> {selectedNode.path}</p>\n                <p><strong>Tip:</strong> {selectedNode.type === 'file' ? 'Dosya' : 'Klasör'}</p>\n                {selectedNode.language && (\n                  <p><strong>Dil:</strong> {selectedNode.language}</p>\n                )}\n                {selectedNode.size && (\n                  <p><strong>Boyut:</strong> {(selectedNode.size / 1024).toFixed(1)} KB</p>\n                )}\n              </div>\n            </div>\n\n            {selectedNode.dependencies && selectedNode.dependencies.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-2\">\n                  Bağımlılıklar ({selectedNode.dependencies.length})\n                </h4>\n                <div className=\"space-y-1\">\n                  {selectedNode.dependencies.map((dep, index) => (\n                    <div\n                      key={index}\n                      className=\"text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded\"\n                    >\n                      {dep}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {selectedNode.content && (\n              <div>\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                    İçerik Önizleme\n                  </h4>\n                  <button\n                    onClick={() => setEditingNode(selectedNode)}\n                    className=\"flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n                  >\n                    <Edit className=\"w-4 h-4\" />\n                    <span>Düzenle</span>\n                  </button>\n                </div>\n                <pre className=\"text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto max-h-64 overflow-y-auto\">\n                  <code>{selectedNode.content.slice(0, 1000)}</code>\n                  {selectedNode.content.length > 1000 && (\n                    <div className=\"text-gray-500 mt-2\">... (devamı var)</div>\n                  )}\n                </pre>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"text-center text-gray-500 dark:text-gray-400 mt-8\">\n            <FileText className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\n            <p>Detayları görmek için bir dosya veya klasör seçin</p>\n          </div>\n        )}\n      </div>\n\n      {/* Code Editor Modal */}\n      {editingNode && (\n        <CodeEditor\n          fileNode={editingNode}\n          onClose={() => setEditingNode(null)}\n          onSave={(content) => {\n            // Bu örnekte sadece local state'i güncelliyoruz\n            // Gerçek uygulamada bu veriyi backend'e gönderebilirsiniz\n            console.log('Saving content for', editingNode.name, content);\n            setEditingNode(null);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAhBA;;;;;;AA6BO,SAAS,SAAS,EAAE,cAAc,EAAiB;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAEhE,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,iBAAiB;IACnB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,kNAAA,CAAA,aAAU,GAAG,sMAAA,CAAA,SAAM;QACzD;QAEA,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QAE9C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,kMAAA,CAAA,OAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,oMAAA,CAAA,QAAS;YAClB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,0MAAA,CAAA,WAAQ;YACjB;gBACE,OAAO,8MAAA,CAAA,WAAQ;QACnB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,MAAM;YACN,KAAK;YACL,GAAG;YACH,QAAQ;YACR,KAAK;YACL,MAAM;YACN,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,KAAK;YACL,MAAM;YACN,MAAM;YACN,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,YAAY,GAAG,IAAI;IACnC;IAEA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAiB;QAC1E,MAAM,aAAa,cAAc,GAAG,CAAC,KAAK,EAAE;QAC5C,MAAM,aAAa,cAAc,OAAO,KAAK,EAAE;QAC/C,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,OAAO,YAAY;QAEzB,qBACE,8OAAC;;8BACC,8OAAC;oBACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,+FACA,cAAc,gCACd;oBAEF,OAAO;wBAAE,aAAa,GAAG,QAAQ,KAAK,EAAE,EAAE,CAAC;oBAAC;oBAC5C,SAAS;wBACP,IAAI,KAAK,IAAI,KAAK,eAAe,aAAa;4BAC5C,WAAW,KAAK,EAAE;wBACpB;wBACA,aAAa;oBACf;;wBAEC,KAAK,IAAI,KAAK,eAAe,6BAC5B,8OAAC;4BAAI,WAAU;sCACZ,2BACC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;wBAI7B,CAAC,CAAC,eAAe,KAAK,IAAI,KAAK,MAAM,mBACpC,8OAAC;4BAAI,WAAU;;;;;;sCAGjB,8OAAC;4BACC,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EACZ,gBACA,KAAK,QAAQ,GAAG,iBAAiB,KAAK,QAAQ,IAAI;;;;;;sCAItD,8OAAC;4BAAK,WAAW,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAClB,oBACA,aACI,iDACA;sCAEH,KAAK,IAAI;;;;;;wBAGX,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,mBAC/C,8OAAC;4BAAK,WAAU;sCACb,KAAK,YAAY,CAAC,MAAM;;;;;;;;;;;;gBAK9B,KAAK,IAAI,KAAK,eAAe,eAAe,4BAC3C,8OAAC;8BACE,KAAK,QAAQ,CAAE,GAAG,CAAC,CAAC,sBACnB,8OAAC;4BAEC,MAAM;4BACN,OAAO,QAAQ;4BACf,cAAc;4BACd,cAAc;2BAJT,MAAM,EAAE;;;;;;;;;;;;;;;;IAW3B;IAEA,2CAA2C;IAC3C,MAAM,YAAY,eAAe,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAClD,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM,KAAK;IAG9D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;oCAEC,MAAM;oCACN,OAAO;oCACP,cAAc;oCACd,cAAc;mCAJT,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAYtB,8OAAC;gBAAI,WAAU;0BACZ,6BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,aAAa,IAAI;;;;;;8CAEpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAa;gDAAE,aAAa,IAAI;;;;;;;sDAC3C,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAa;gDAAE,aAAa,IAAI,KAAK,SAAS,UAAU;;;;;;;wCAClE,aAAa,QAAQ,kBACpB,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAa;gDAAE,aAAa,QAAQ;;;;;;;wCAEhD,aAAa,IAAI,kBAChB,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAe;gDAAE,CAAC,aAAa,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;wBAKvE,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,MAAM,GAAG,mBAC/D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAyD;wCACrD,aAAa,YAAY,CAAC,MAAM;wCAAC;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;8CACZ,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnC,8OAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;;;;;;;;;;;;wBAUd,aAAa,OAAO,kBACnB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;;8DAEV,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAM,aAAa,OAAO,CAAC,KAAK,CAAC,GAAG;;;;;;wCACpC,aAAa,OAAO,CAAC,MAAM,GAAG,sBAC7B,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;yCAO9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;sCAAE;;;;;;;;;;;;;;;;;YAMR,6BACC,8OAAC,gIAAA,CAAA,aAAU;gBACT,UAAU;gBACV,SAAS,IAAM,eAAe;gBAC9B,QAAQ,CAAC;oBACP,gDAAgD;oBAChD,0DAA0D;oBAC1D,QAAQ,GAAG,CAAC,sBAAsB,YAAY,IAAI,EAAE;oBACpD,eAAe;gBACjB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/RepositoryInfo.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { AnalysisResult } from '@/types';\nimport { ExternalLink, Star, GitFork, Eye, FileText, Folder, GitBranch } from 'lucide-react';\n\ninterface RepositoryInfoProps {\n  analysisResult: AnalysisResult;\n}\n\nexport function RepositoryInfo({ analysisResult }: RepositoryInfoProps) {\n  const { repo, languages, stats } = analysisResult;\n\n  const languageEntries = Object.entries(languages).sort(([, a], [, b]) => b - a);\n  const totalBytes = Object.values(languages).reduce((sum, bytes) => sum + bytes, 0);\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6\">\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n              {repo.full_name}\n            </h2>\n            <a\n              href={repo.html_url}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n            >\n              <ExternalLink className=\"w-4 h-4\" />\n            </a>\n          </div>\n          {repo.description && (\n            <p className=\"text-gray-600 dark:text-gray-400 mb-3\">\n              {repo.description}\n            </p>\n          )}\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n            <div className=\"flex items-center space-x-1\">\n              <Star className=\"w-4 h-4\" />\n              <span>Stars</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <GitFork className=\"w-4 h-4\" />\n              <span>Forks</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"w-4 h-4\" />\n              <span>Watchers</span>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex-shrink-0 ml-4\">\n          <Image\n            src={repo.owner.avatar_url}\n            alt={repo.owner.login}\n            width={48}\n            height={48}\n            className=\"w-12 h-12 rounded-full\"\n          />\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        {/* Statistics */}\n        <div className=\"space-y-3\">\n          <h3 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n            İstatistikler\n          </h3>\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <FileText className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Dosyalar</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {stats.totalFiles}\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Folder className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Klasörler</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {stats.totalDirectories}\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <GitBranch className=\"w-4 h-4 text-gray-400\" />\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Bağımlılıklar</span>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {stats.totalDependencies}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Languages */}\n        <div className=\"space-y-3\">\n          <h3 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n            Programlama Dilleri\n          </h3>\n          <div className=\"space-y-2\">\n            {languageEntries.slice(0, 5).map(([language, bytes]) => {\n              const percentage = ((bytes / totalBytes) * 100).toFixed(1);\n              return (\n                <div key={language} className=\"space-y-1\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {language}\n                    </span>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-500\">\n                      {percentage}%\n                    </span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5\">\n                    <div\n                      className=\"bg-blue-600 h-1.5 rounded-full\"\n                      style={{ width: `${percentage}%` }}\n                    />\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Repository Details */}\n        <div className=\"space-y-3\">\n          <h3 className=\"text-sm font-medium text-gray-900 dark:text-white\">\n            Repository Detayları\n          </h3>\n          <div className=\"space-y-2 text-sm\">\n            <div>\n              <span className=\"text-gray-600 dark:text-gray-400\">Sahip: </span>\n              <span className=\"text-gray-900 dark:text-white\">{repo.owner.login}</span>\n            </div>\n            <div>\n              <span className=\"text-gray-600 dark:text-gray-400\">Ana Dal: </span>\n              <span className=\"text-gray-900 dark:text-white\">{repo.default_branch}</span>\n            </div>\n            {repo.language && (\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">Ana Dil: </span>\n                <span className=\"text-gray-900 dark:text-white\">{repo.language}</span>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAUO,SAAS,eAAe,EAAE,cAAc,EAAuB;IACpE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEnC,MAAM,kBAAkB,OAAO,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI;IAC7E,MAAM,aAAa,OAAO,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO;IAEhF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,SAAS;;;;;;kDAEjB,8OAAC;wCACC,MAAM,KAAK,QAAQ;wCACnB,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAG3B,KAAK,WAAW,kBACf,8OAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;0CAGrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAIZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,KAAK,CAAC,UAAU;4BAC1B,KAAK,KAAK,KAAK,CAAC,KAAK;4BACrB,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAK,WAAU;0DACb,MAAM,UAAU;;;;;;;;;;;;kDAGrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAK,WAAU;0DACb,MAAM,gBAAgB;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,8OAAC;gDAAK,WAAU;0DACb,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAOhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM;oCACjD,MAAM,aAAa,CAAC,AAAC,QAAQ,aAAc,GAAG,EAAE,OAAO,CAAC;oCACxD,qBACE,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb;;;;;;kEAEH,8OAAC;wDAAK,WAAU;;4DACb;4DAAW;;;;;;;;;;;;;0DAGhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oDAAC;;;;;;;;;;;;uCAZ7B;;;;;gCAiBd;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAiC,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;;kDAEnE,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAiC,KAAK,cAAc;;;;;;;;;;;;oCAErE,KAAK,QAAQ,kBACZ,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DAAiC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9E", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/components/CodeGraphViewer.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { AnalysisResult } from '@/types';\nimport { Button } from '@/components/ui/button';\nimport { NodeGraphView } from './NodeGraphView';\nimport { TreeView } from './TreeView';\nimport { RepositoryInfo } from './RepositoryInfo';\nimport { ArrowLeft, Network, FolderTree } from 'lucide-react';\n\ninterface CodeGraphViewerProps {\n  analysisResult: AnalysisResult;\n  onReset: () => void;\n}\n\nexport function CodeGraphViewer({ analysisResult, onReset }: CodeGraphViewerProps) {\n  const [viewMode, setViewMode] = useState<'tree' | 'graph'>('tree');\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <Button\n          variant=\"outline\"\n          onClick={onReset}\n          className=\"flex items-center space-x-2\"\n        >\n          <ArrowLeft className=\"w-4 h-4\" />\n          <span>Yeni <PERSON></span>\n        </Button>\n\n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant={viewMode === 'tree' ? 'default' : 'outline'}\n            onClick={() => setViewMode('tree')}\n            className=\"flex items-center space-x-2\"\n          >\n            <FolderTree className=\"w-4 h-4\" />\n            <span>Tree View</span>\n          </Button>\n          <Button\n            variant={viewMode === 'graph' ? 'default' : 'outline'}\n            onClick={() => setViewMode('graph')}\n            className=\"flex items-center space-x-2\"\n          >\n            <Network className=\"w-4 h-4\" />\n            <span>Node Graph</span>\n          </Button>\n        </div>\n      </div>\n\n      {/* Repository Info */}\n      <RepositoryInfo analysisResult={analysisResult} />\n\n      {/* Main Content */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border\">\n        {viewMode === 'tree' ? (\n          <TreeView analysisResult={analysisResult} />\n        ) : (\n          <NodeGraphView analysisResult={analysisResult} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AARA;;;;;;;;AAeO,SAAS,gBAAgB,EAAE,cAAc,EAAE,OAAO,EAAwB;IAC/E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAE3D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CAAK;;;;;;;;;;;;kCAGR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,SAAS,YAAY;gCAC3C,SAAS,IAAM,YAAY;gCAC3B,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,aAAa,UAAU,YAAY;gCAC5C,SAAS,IAAM,YAAY;gCAC3B,WAAU;;kDAEV,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC,oIAAA,CAAA,iBAAc;gBAAC,gBAAgB;;;;;;0BAGhC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,uBACZ,8OAAC,8HAAA,CAAA,WAAQ;oBAAC,gBAAgB;;;;;yCAE1B,8OAAC,mIAAA,CAAA,gBAAa;oBAAC,gBAAgB;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 2327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { GitHubRepoInput } from '@/components/GitHubRepoInput';\nimport { CodeGraphViewer } from '@/components/CodeGraphViewer';\nimport { AnalysisResult } from '@/types';\n\nexport default function Home() {\n  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleAnalysisComplete = (result: AnalysisResult) => {\n    setAnalysisResult(result);\n    setIsLoading(false);\n  };\n\n  const handleAnalysisStart = () => {\n    setIsLoading(true);\n    setAnalysisResult(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                BaseGraph\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                GitHub kod tabanı görselleştirme ve analiz aracı\n              </p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!analysisResult && !isLoading && (\n          <div className=\"text-center\">\n            <div className=\"max-w-md mx-auto\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                GitHub Repository Analizi\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-8\">\n                Bir GitHub repository URL&apos;si girin ve kod tabanınızın görsel analizini görün\n              </p>\n              <GitHubRepoInput\n                onAnalysisStart={handleAnalysisStart}\n                onAnalysisComplete={handleAnalysisComplete}\n              />\n            </div>\n          </div>\n        )}\n\n        {isLoading && (\n          <div className=\"text-center py-12\">\n            <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            <p className=\"mt-4 text-gray-600 dark:text-gray-400\">\n              Repository analiz ediliyor...\n            </p>\n          </div>\n        )}\n\n        {analysisResult && (\n          <CodeGraphViewer\n            analysisResult={analysisResult}\n            onReset={() => setAnalysisResult(null)}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,yBAAyB,CAAC;QAC9B,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CAGjE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhE,8OAAC;gBAAK,WAAU;;oBACb,CAAC,kBAAkB,CAAC,2BACnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,8OAAC,qIAAA,CAAA,kBAAe;oCACd,iBAAiB;oCACjB,oBAAoB;;;;;;;;;;;;;;;;;oBAM3B,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;oBAMxD,gCACC,8OAAC,qIAAA,CAAA,kBAAe;wBACd,gBAAgB;wBAChB,SAAS,IAAM,kBAAkB;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}]}