import { FileNode, DependencyEdge, FunctionInfo, FunctionCall } from '@/types';

export class CodeAnalyzer {
  private static readonly SUPPORTED_EXTENSIONS = {
    '.js': 'javascript',
    '.jsx': 'javascript',
    '.ts': 'typescript',
    '.tsx': 'typescript',
    '.py': 'python',
    '.java': 'java',
    '.cpp': 'cpp',
    '.c': 'c',
    '.cs': 'csharp',
    '.php': 'php',
    '.rb': 'ruby',
    '.go': 'go',
    '.rs': 'rust',
    '.swift': 'swift',
    '.kt': 'kotlin',
    '.scala': 'scala',
    '.html': 'html',
    '.css': 'css',
    '.scss': 'scss',
    '.sass': 'sass',
    '.less': 'less',
    '.json': 'json',
    '.xml': 'xml',
    '.yaml': 'yaml',
    '.yml': 'yaml',
    '.md': 'markdown',
    '.sql': 'sql',
  };

  static getLanguageFromExtension(filename: string): string | undefined {
    const ext = filename.substring(filename.lastIndexOf('.'));
    return this.SUPPORTED_EXTENSIONS[ext as keyof typeof this.SUPPORTED_EXTENSIONS];
  }

  static extractDependencies(content: string, language: string): string[] {
    const dependencies: string[] = [];

    switch (language) {
      case 'javascript':
      case 'typescript':
        dependencies.push(...this.extractJavaScriptDependencies(content));
        break;
      case 'python':
        dependencies.push(...this.extractPythonDependencies(content));
        break;
      case 'java':
        dependencies.push(...this.extractJavaDependencies(content));
        break;
      case 'cpp':
      case 'c':
        dependencies.push(...this.extractCDependencies(content));
        break;
      case 'csharp':
        dependencies.push(...this.extractCSharpDependencies(content));
        break;
      case 'go':
        dependencies.push(...this.extractGoDependencies(content));
        break;
      case 'rust':
        dependencies.push(...this.extractRustDependencies(content));
        break;
      default:
        break;
    }

    return [...new Set(dependencies)]; // Remove duplicates
  }

  private static extractJavaScriptDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // ES6 imports
    const importRegex = /import\s+(?:.*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    // CommonJS requires
    const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    // Dynamic imports
    const dynamicImportRegex = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    return dependencies;
  }

  private static extractPythonDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // import statements
    const importRegex = /^import\s+([^\s#]+)/gm;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1].split('.')[0]);
    }

    // from ... import statements
    const fromImportRegex = /^from\s+([^\s#]+)\s+import/gm;
    while ((match = fromImportRegex.exec(content)) !== null) {
      dependencies.push(match[1].split('.')[0]);
    }

    return dependencies;
  }

  private static extractJavaDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // import statements
    const importRegex = /^import\s+(?:static\s+)?([^;]+);/gm;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const packageName = match[1].trim();
      dependencies.push(packageName);
    }

    return dependencies;
  }

  private static extractCDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // #include statements
    const includeRegex = /#include\s*[<"]([^>"]+)[>"]/g;
    let match;
    while ((match = includeRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    return dependencies;
  }

  private static extractCSharpDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // using statements
    const usingRegex = /^using\s+([^;]+);/gm;
    let match;
    while ((match = usingRegex.exec(content)) !== null) {
      dependencies.push(match[1].trim());
    }

    return dependencies;
  }

  private static extractGoDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // import statements
    const importRegex = /import\s+(?:\(\s*([^)]+)\s*\)|"([^"]+)")/g;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        // Multi-line import
        const imports = match[1].split('\n').map(line => {
          const trimmed = line.trim();
          const quoted = trimmed.match(/"([^"]+)"/);
          return quoted ? quoted[1] : null;
        }).filter(Boolean);
        dependencies.push(...imports as string[]);
      } else if (match[2]) {
        // Single import
        dependencies.push(match[2]);
      }
    }

    return dependencies;
  }

  private static extractRustDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // use statements
    const useRegex = /^use\s+([^;]+);/gm;
    let match;
    while ((match = useRegex.exec(content)) !== null) {
      const usePath = match[1].trim();
      const rootModule = usePath.split('::')[0];
      dependencies.push(rootModule);
    }

    // extern crate statements
    const externRegex = /^extern\s+crate\s+([^;]+);/gm;
    while ((match = externRegex.exec(content)) !== null) {
      dependencies.push(match[1].trim());
    }

    return dependencies;
  }

  static createDependencyEdges(nodes: FileNode[]): DependencyEdge[] {
    const edges: DependencyEdge[] = [];
    const nodeMap = new Map(nodes.map(node => [node.path, node]));

    nodes.forEach(node => {
      if (node.dependencies) {
        node.dependencies.forEach(dep => {
          // Try to find the dependency in the current project
          const targetNode = this.findDependencyTarget(dep, nodeMap, node.path);
          if (targetNode) {
            edges.push({
              id: `${node.id}-${targetNode.id}`,
              source: node.id,
              target: targetNode.id,
              type: 'import',
              label: dep,
            });
          }
        });
      }
    });

    return edges;
  }

  private static findDependencyTarget(
    dependency: string,
    nodeMap: Map<string, FileNode>,
    sourcePath: string
  ): FileNode | null {
    // Handle relative imports
    if (dependency.startsWith('./') || dependency.startsWith('../')) {
      const resolvedPath = this.resolvePath(sourcePath, dependency);
      return nodeMap.get(resolvedPath) || null;
    }

    // Handle absolute imports within the project
    for (const [path, node] of nodeMap) {
      if (path.includes(dependency) || node.name.includes(dependency)) {
        return node;
      }
    }

    return null;
  }

  private static resolvePath(basePath: string, relativePath: string): string {
    const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
    const parts = baseDir.split('/').concat(relativePath.split('/'));
    const resolved: string[] = [];

    parts.forEach(part => {
      if (part === '..') {
        resolved.pop();
      } else if (part !== '.' && part !== '') {
        resolved.push(part);
      }
    });

    return resolved.join('/');
  }
}
