import { FileNode, DependencyEdge, FunctionInfo, FunctionCall } from '@/types';
import { AICodeAnalyzer, AIConfig } from './aiCodeAnalyzer';

export class CodeAnalyzer {
  private static readonly SUPPORTED_EXTENSIONS = {
    '.js': 'javascript',
    '.jsx': 'javascript',
    '.ts': 'typescript',
    '.tsx': 'typescript',
    '.py': 'python',
    '.java': 'java',
    '.cpp': 'cpp',
    '.c': 'c',
    '.cs': 'csharp',
    '.php': 'php',
    '.rb': 'ruby',
    '.go': 'go',
    '.rs': 'rust',
    '.swift': 'swift',
    '.kt': 'kotlin',
    '.scala': 'scala',
    '.html': 'html',
    '.css': 'css',
    '.scss': 'scss',
    '.sass': 'sass',
    '.less': 'less',
    '.json': 'json',
    '.xml': 'xml',
    '.yaml': 'yaml',
    '.yml': 'yaml',
    '.md': 'markdown',
    '.sql': 'sql',
  };

  static getLanguageFromExtension(filename: string): string | undefined {
    const ext = filename.substring(filename.lastIndexOf('.'));
    return this.SUPPORTED_EXTENSIONS[ext as keyof typeof this.SUPPORTED_EXTENSIONS];
  }

  static extractDependencies(content: string, language: string): string[] {
    const dependencies: string[] = [];

    switch (language) {
      case 'javascript':
      case 'typescript':
        dependencies.push(...this.extractJavaScriptDependencies(content));
        break;
      case 'python':
        dependencies.push(...this.extractPythonDependencies(content));
        break;
      case 'java':
        dependencies.push(...this.extractJavaDependencies(content));
        break;
      case 'cpp':
      case 'c':
        dependencies.push(...this.extractCDependencies(content));
        break;
      case 'csharp':
        dependencies.push(...this.extractCSharpDependencies(content));
        break;
      case 'go':
        dependencies.push(...this.extractGoDependencies(content));
        break;
      case 'rust':
        dependencies.push(...this.extractRustDependencies(content));
        break;
      default:
        break;
    }

    return [...new Set(dependencies)]; // Remove duplicates
  }

  static async extractFunctions(content: string, language: string, filename: string = 'unknown'): Promise<FunctionInfo[]> {
    // Check if AI analysis is enabled
    const enableAI = process.env.ENABLE_AI_ANALYSIS === 'true' && process.env.OPENAI_API_KEY;

    if (enableAI) {
      try {
        // Try to get config from localStorage first, then fall back to environment variables
        let aiConfig: AIConfig;

        if (typeof window !== 'undefined') {
          const savedConfig = localStorage.getItem('aiProviderConfig');
          if (savedConfig) {
            const parsed = JSON.parse(savedConfig);
            aiConfig = {
              apiKey: parsed.apiKey,
              baseURL: parsed.baseURL,
              model: parsed.model,
              maxTokens: parsed.maxTokens,
              temperature: parsed.temperature,
            };
          } else {
            // Fallback to environment variables
            aiConfig = {
              apiKey: process.env.OPENAI_API_KEY || '',
              baseURL: process.env.OPENAI_BASE_URL,
              model: process.env.OPENAI_MODEL,
              maxTokens: process.env.OPENAI_MAX_TOKENS ? parseInt(process.env.OPENAI_MAX_TOKENS) : undefined,
              temperature: process.env.OPENAI_TEMPERATURE ? parseFloat(process.env.OPENAI_TEMPERATURE) : undefined,
            };
          }
        } else {
          // Server-side: use environment variables
          aiConfig = {
            apiKey: process.env.OPENAI_API_KEY || '',
            baseURL: process.env.OPENAI_BASE_URL,
            model: process.env.OPENAI_MODEL,
            maxTokens: process.env.OPENAI_MAX_TOKENS ? parseInt(process.env.OPENAI_MAX_TOKENS) : undefined,
            temperature: process.env.OPENAI_TEMPERATURE ? parseFloat(process.env.OPENAI_TEMPERATURE) : undefined,
          };
        }

        const aiAnalyzer = AICodeAnalyzer.getInstance(aiConfig);
        const result = await aiAnalyzer.analyzeCodeWithCache(content, language, filename);
        return result.functions;
      } catch (error) {
        console.warn('AI analysis failed, falling back to regex analysis:', error);
      }
    }

    // Fallback to regex-based analysis
    return this.extractFunctionsRegex(content, language);
  }

  private static extractFunctionsRegex(content: string, language: string): FunctionInfo[] {
    // Simple regex-based fallback for basic function detection
    const functions: FunctionInfo[] = [];
    const lines = content.split('\n');

    switch (language) {
      case 'javascript':
      case 'typescript':
        functions.push(...this.extractJavaScriptFunctionsRegex(lines));
        break;
      case 'python':
        functions.push(...this.extractPythonFunctionsRegex(lines));
        break;
      default:
        // For other languages, provide basic function detection
        functions.push(...this.extractGenericFunctionsRegex(lines, language));
        break;
    }

    return functions;
  }

  private static extractJavaScriptFunctionsRegex(lines: string[]): FunctionInfo[] {
    const functions: FunctionInfo[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Function declarations: function name() {}
      const funcMatch = line.match(/^(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(/);
      if (funcMatch) {
        functions.push({
          name: funcMatch[1],
          startLine: i + 1,
          endLine: i + 1, // Simple approximation
          parameters: [],
          isExported: line.includes('export'),
          isAsync: line.includes('async'),
          calls: []
        });
      }

      // Arrow functions: const name = () => {}
      const arrowMatch = line.match(/^(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s+)?\(/);
      if (arrowMatch) {
        functions.push({
          name: arrowMatch[1],
          startLine: i + 1,
          endLine: i + 1,
          parameters: [],
          isExported: line.includes('export'),
          isAsync: line.includes('async'),
          calls: []
        });
      }
    }

    return functions;
  }

  private static extractPythonFunctionsRegex(lines: string[]): FunctionInfo[] {
    const functions: FunctionInfo[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Python function definitions: def name():
      const funcMatch = line.match(/^(?:async\s+)?def\s+(\w+)\s*\(/);
      if (funcMatch) {
        functions.push({
          name: funcMatch[1],
          startLine: i + 1,
          endLine: i + 1,
          parameters: [],
          isAsync: line.includes('async'),
          calls: []
        });
      }
    }

    return functions;
  }

  private static extractGenericFunctionsRegex(lines: string[], language: string): FunctionInfo[] {
    // Very basic function detection for other languages
    const functions: FunctionInfo[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Generic pattern for function-like constructs
      const patterns = [
        /function\s+(\w+)/,  // function keyword
        /def\s+(\w+)/,       // Python def
        /fn\s+(\w+)/,        // Rust fn
        /func\s+(\w+)/,      // Go func
        /(\w+)\s*\(/         // Generic name(
      ];

      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match) {
          functions.push({
            name: match[1],
            startLine: i + 1,
            endLine: i + 1,
            parameters: [],
            calls: []
          });
          break;
        }
      }
    }

    return functions;
  }

  private static extractJavaScriptDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // ES6 imports
    const importRegex = /import\s+(?:.*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    // CommonJS requires
    const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    // Dynamic imports
    const dynamicImportRegex = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    return dependencies;
  }

  private static extractPythonDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // import statements
    const importRegex = /^import\s+([^\s#]+)/gm;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      dependencies.push(match[1].split('.')[0]);
    }

    // from ... import statements
    const fromImportRegex = /^from\s+([^\s#]+)\s+import/gm;
    while ((match = fromImportRegex.exec(content)) !== null) {
      dependencies.push(match[1].split('.')[0]);
    }

    return dependencies;
  }

  private static extractJavaDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // import statements
    const importRegex = /^import\s+(?:static\s+)?([^;]+);/gm;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const packageName = match[1].trim();
      dependencies.push(packageName);
    }

    return dependencies;
  }

  private static extractCDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // #include statements
    const includeRegex = /#include\s*[<"]([^>"]+)[>"]/g;
    let match;
    while ((match = includeRegex.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    return dependencies;
  }

  private static extractCSharpDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // using statements
    const usingRegex = /^using\s+([^;]+);/gm;
    let match;
    while ((match = usingRegex.exec(content)) !== null) {
      dependencies.push(match[1].trim());
    }

    return dependencies;
  }

  private static extractGoDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // import statements
    const importRegex = /import\s+(?:\(\s*([^)]+)\s*\)|"([^"]+)")/g;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      if (match[1]) {
        // Multi-line import
        const imports = match[1].split('\n').map(line => {
          const trimmed = line.trim();
          const quoted = trimmed.match(/"([^"]+)"/);
          return quoted ? quoted[1] : null;
        }).filter(Boolean);
        dependencies.push(...imports as string[]);
      } else if (match[2]) {
        // Single import
        dependencies.push(match[2]);
      }
    }

    return dependencies;
  }

  private static extractRustDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // use statements
    const useRegex = /^use\s+([^;]+);/gm;
    let match;
    while ((match = useRegex.exec(content)) !== null) {
      const usePath = match[1].trim();
      const rootModule = usePath.split('::')[0];
      dependencies.push(rootModule);
    }

    // extern crate statements
    const externRegex = /^extern\s+crate\s+([^;]+);/gm;
    while ((match = externRegex.exec(content)) !== null) {
      dependencies.push(match[1].trim());
    }

    return dependencies;
  }

  static createDependencyEdges(nodes: FileNode[]): DependencyEdge[] {
    const edges: DependencyEdge[] = [];
    const nodeMap = new Map(nodes.map(node => [node.path, node]));
    const functionMap = new Map<string, { node: FileNode, function: FunctionInfo }>();

    // Build function map for quick lookup
    nodes.forEach(node => {
      if (node.functions) {
        node.functions.forEach(func => {
          functionMap.set(`${node.path}:${func.name}`, { node, function: func });
        });
      }
    });

    nodes.forEach(node => {
      // Add import/require edges
      if (node.dependencies) {
        node.dependencies.forEach(dep => {
          const targetNode = this.findDependencyTarget(dep, nodeMap, node.path);
          if (targetNode) {
            edges.push({
              id: `import-${node.id}-${targetNode.id}`,
              source: node.id,
              target: targetNode.id,
              type: 'import',
              label: dep,
            });
          }
        });
      }

      // Add function call edges
      if (node.functions) {
        node.functions.forEach(func => {
          if (func.calls) {
            func.calls.forEach(call => {
              if (!call.isExternal) {
                // Try to find the target function in the same file first
                const sameFileTarget = functionMap.get(`${node.path}:${call.functionName}`);
                if (sameFileTarget) {
                  edges.push({
                    id: `call-${node.id}-${func.name}-${call.functionName}-${call.line}`,
                    source: node.id,
                    target: node.id, // Same file
                    type: 'function_call',
                    label: `${func.name} → ${call.functionName}`,
                    sourceFunction: func.name,
                    targetFunction: call.functionName,
                    line: call.line,
                  });
                } else if (call.targetFile) {
                  // Try to find in other files
                  const targetNode = nodeMap.get(call.targetFile);
                  if (targetNode) {
                    edges.push({
                      id: `call-${node.id}-${targetNode.id}-${func.name}-${call.functionName}-${call.line}`,
                      source: node.id,
                      target: targetNode.id,
                      type: 'function_call',
                      label: `${func.name} → ${call.functionName}`,
                      sourceFunction: func.name,
                      targetFunction: call.functionName,
                      line: call.line,
                    });
                  }
                }
              }
            });
          }
        });
      }
    });

    return edges;
  }

  private static findDependencyTarget(
    dependency: string,
    nodeMap: Map<string, FileNode>,
    sourcePath: string
  ): FileNode | null {
    // Handle relative imports
    if (dependency.startsWith('./') || dependency.startsWith('../')) {
      const resolvedPath = this.resolvePath(sourcePath, dependency);
      return nodeMap.get(resolvedPath) || null;
    }

    // Handle absolute imports within the project
    for (const [path, node] of nodeMap) {
      if (path.includes(dependency) || node.name.includes(dependency)) {
        return node;
      }
    }

    return null;
  }

  private static resolvePath(basePath: string, relativePath: string): string {
    const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
    const parts = baseDir.split('/').concat(relativePath.split('/'));
    const resolved: string[] = [];

    parts.forEach(part => {
      if (part === '..') {
        resolved.pop();
      } else if (part !== '.' && part !== '') {
        resolved.push(part);
      }
    });

    return resolved.join('/');
  }
}
