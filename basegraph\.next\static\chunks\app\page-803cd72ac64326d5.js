(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3169:(e,s,a)=>{Promise.resolve().then(a.bind(a,9532))},9532:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>T});var t=a(5155),r=a(2115),l=a(2596);let n=r.forwardRef((e,s)=>{let{className:a,variant:r="default",size:n="default",...i}=e;return(0,t.jsx)("button",{className:(0,l.$)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{"bg-primary text-primary-foreground hover:bg-primary/90":"default"===r,"bg-destructive text-destructive-foreground hover:bg-destructive/90":"destructive"===r,"border border-input bg-background hover:bg-accent hover:text-accent-foreground":"outline"===r,"bg-secondary text-secondary-foreground hover:bg-secondary/80":"secondary"===r,"hover:bg-accent hover:text-accent-foreground":"ghost"===r,"text-primary underline-offset-4 hover:underline":"link"===r},{"h-10 px-4 py-2":"default"===n,"h-9 rounded-md px-3":"sm"===n,"h-11 rounded-md px-8":"lg"===n,"h-10 w-10":"icon"===n},a),ref:s,...i})});n.displayName="Button";let i=r.forwardRef((e,s)=>{let{className:a,type:r,...n}=e;return(0,t.jsx)("input",{type:r,className:(0,l.$)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...n})});i.displayName="Input";var d=a(9099),c=a(5339);function x(e){let{onAnalysisStart:s,onAnalysisComplete:a}=e,[l,x]=(0,r.useState)(""),[o,m]=(0,r.useState)(""),[h,g]=(0,r.useState)(!1),p=async()=>{if(!l.trim())return void m("L\xfctfen bir GitHub repository URL&apos;si girin");m(""),g(!0),s();try{let e=await fetch("/api/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:l})});if(!e.ok){let s=await e.json();throw Error(s.error||"Analiz sırasında bir hata oluştu")}let s=await e.json();a(s)}catch(e){m(e instanceof Error?e.message:"Analiz sırasında bir hata oluştu")}finally{g(!1)}};return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(i,{type:"url",placeholder:"https://github.com/username/repository",value:l,onChange:e=>x(e.target.value),className:"w-full",disabled:h})}),(0,t.jsxs)(n,{onClick:p,disabled:h||!l.trim(),className:"flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:h?"Analiz Ediliyor...":"Analiz Et"})]})]}),o&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-red-600 dark:text-red-400",children:[(0,t.jsx)(c.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:o})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("p",{children:"\xd6rnek: https://github.com/facebook/react veya https://github.com/microsoft/vscode"}),(0,t.jsx)("p",{className:"mt-1",children:"Not: B\xfcy\xfck repository'ler i\xe7in analiz biraz zaman alabilir."})]})]})}var o=a(1772),m=a(9219);a(6419);var h=a(9621),g=a(7213),p=a(381),u=a(7434),y=a(2775);let b=(0,r.memo)(e=>{let{data:s,selected:a}=e,{fileNode:r,isSelected:n,onSelect:i}=s,d=(()=>{var e;switch(null==(e=r.name.split(".").pop())?void 0:e.toLowerCase()){case"js":case"jsx":case"ts":case"tsx":case"py":case"java":case"cpp":case"c":case"cs":case"php":case"rb":case"go":case"rs":case"swift":case"kt":return h.A;case"png":case"jpg":case"jpeg":case"gif":case"svg":case"webp":return g.A;case"json":case"xml":case"yaml":case"yml":case"toml":case"ini":return p.A;default:return u.A}})(),c={javascript:"border-yellow-400 bg-yellow-50",typescript:"border-blue-400 bg-blue-50",python:"border-green-400 bg-green-50",java:"border-red-400 bg-red-50",cpp:"border-purple-400 bg-purple-50",c:"border-gray-400 bg-gray-50",csharp:"border-indigo-400 bg-indigo-50",php:"border-purple-500 bg-purple-50",ruby:"border-red-500 bg-red-50",go:"border-cyan-400 bg-cyan-50",rust:"border-orange-400 bg-orange-50",swift:"border-orange-500 bg-orange-50",kotlin:"border-purple-600 bg-purple-50",html:"border-orange-500 bg-orange-50",css:"border-blue-500 bg-blue-50",scss:"border-pink-500 bg-pink-50",json:"border-yellow-500 bg-yellow-50",markdown:"border-gray-600 bg-gray-50"}[r.language||""]||"border-gray-300 bg-white",x=r.dependencies&&r.dependencies.length>0;return(0,t.jsxs)("div",{className:(0,l.$)("px-4 py-3 shadow-md rounded-lg border-2 cursor-pointer transition-all duration-200 min-w-[180px] max-w-[220px]",c,n||a?"ring-2 ring-blue-500 ring-offset-2 shadow-lg scale-105":"hover:shadow-lg hover:scale-102"),onClick:()=>i(r.id),children:[(0,t.jsx)(o.h7,{type:"target",position:m.yX.Top,className:"w-3 h-3 !bg-blue-500 !border-2 !border-white"}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,t.jsx)(d,{className:"w-5 h-5 text-gray-600"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate",children:r.name}),r.language&&(0,t.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:r.language}),r.size&&(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[(r.size/1024).toFixed(1)," KB"]}),x&&(0,t.jsxs)("div",{className:"flex items-center space-x-1 mt-2",children:[(0,t.jsx)(y.A,{className:"w-3 h-3 text-blue-600"}),(0,t.jsxs)("span",{className:"text-xs text-blue-600 font-medium",children:[r.dependencies.length," bağımlılık"]})]})]})]}),(0,t.jsx)(o.h7,{type:"source",position:m.yX.Bottom,className:"w-3 h-3 !bg-green-500 !border-2 !border-white"})]})});b.displayName="FileNodeComponent";var j=a(1788),f=a(4229),v=a(4416);function N(e){let{fileNode:s,onClose:a,onSave:i}=e,[d,c]=(0,r.useState)(s.content||""),[x,o]=(0,r.useState)(!1);return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s.name}),x&&(0,t.jsx)("span",{className:"text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 px-2 py-1 rounded",children:"Değiştirildi"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n,{variant:"outline",size:"sm",onClick:()=>{let e=new Blob([d],{type:"text/plain"}),a=URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download=s.name,document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(a)},className:"flex items-center space-x-1",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"İndir"})]}),i&&(0,t.jsxs)(n,{variant:"default",size:"sm",onClick:()=>{i&&i(d),o(!1)},disabled:!x,className:"flex items-center space-x-1",children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Kaydet"})]}),(0,t.jsx)(n,{variant:"ghost",size:"sm",onClick:a,className:"flex items-center space-x-1",children:(0,t.jsx)(v.A,{className:"w-4 h-4"})})]})]}),(0,t.jsx)("div",{className:"px-4 py-2 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsxs)("span",{children:["Yol: ",s.path]}),s.language&&(0,t.jsxs)("span",{children:["Dil: ",s.language]}),s.size&&(0,t.jsxs)("span",{children:["Boyut: ",(s.size/1024).toFixed(1)," KB"]}),(0,t.jsxs)("span",{children:["Satırlar: ",d.split("\n").length]})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,t.jsx)("textarea",{value:d,onChange:e=>{var a;c(a=e.target.value),o(a!==s.content)},className:(0,l.$)("w-full h-full p-4 font-mono text-sm resize-none border-none outline-none","bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100","focus:ring-0 focus:border-transparent",{javascript:"language-javascript",typescript:"language-typescript",python:"language-python",java:"language-java",cpp:"language-cpp",c:"language-c",csharp:"language-csharp",php:"language-php",ruby:"language-ruby",go:"language-go",rust:"language-rust",html:"language-html",css:"language-css",json:"language-json",markdown:"language-markdown"}[s.language||""]||"language-text"),placeholder:s.content?"":"Dosya i\xe7eriği y\xfcklenemedi...",readOnly:!s.content,spellCheck:!1,style:{lineHeight:"1.5",tabSize:2}})}),(0,t.jsx)("div",{className:"px-4 py-2 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:s.dependencies&&s.dependencies.length>0&&(0,t.jsxs)("span",{children:[s.dependencies.length," bağımlılık"]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("span",{children:"UTF-8"}),(0,t.jsx)("span",{children:"LF"}),s.language&&(0,t.jsx)("span",{children:s.language.toUpperCase()})]})]})})]})})}let k={fileNode:b};function w(e){let{analysisResult:s}=e,[a,l]=(0,r.useState)(null),[n,i]=(0,r.useState)(null),d=(0,r.useMemo)(()=>{let e=[],{graph:a}=s,t=a.nodes.filter(e=>"file"===e.type);return t.forEach((s,a)=>{let r=Math.ceil(Math.sqrt(t.length)),n=Math.floor(a/r);e.push({id:s.id,type:"fileNode",position:{x:a%r*250+50,y:150*n+50},data:{fileNode:s,isSelected:!1,onSelect:e=>l(e)},draggable:!0})}),e},[s]),c=(0,r.useMemo)(()=>s.graph.edges.map(e=>({id:e.id,source:e.source,target:e.target,type:"smoothstep",animated:!0,label:e.label,style:{stroke:"#6366f1",strokeWidth:2},labelStyle:{fontSize:10,fontWeight:600},labelBgStyle:{fill:"#ffffff",fillOpacity:.8}})),[s]),[x,,h]=(0,o.ck)(d),[g,p,u]=(0,o.fM)(c),y=(0,r.useCallback)(e=>p(s=>(0,m.rN)(e,s)),[p]),b=(0,r.useMemo)(()=>x.map(e=>({...e,data:{...e.data,isSelected:e.id===a}})),[x,a]),j=e=>({javascript:"#f7df1e",typescript:"#3178c6",python:"#3776ab",java:"#ed8b00",cpp:"#00599c",c:"#a8b9cc",csharp:"#239120",php:"#777bb4",ruby:"#cc342d",go:"#00add8",rust:"#dea584",swift:"#fa7343",kotlin:"#7f52ff",html:"#e34f26",css:"#1572b6",scss:"#cf649a",json:"#000000",markdown:"#083fa1"})[e||""]||"#6b7280";return(0,t.jsxs)("div",{className:"h-[600px] w-full",children:[(0,t.jsxs)(o.Gc,{nodes:b,edges:g,onNodesChange:h,onEdgesChange:u,onConnect:y,nodeTypes:k,fitView:!0,attributionPosition:"bottom-left",children:[(0,t.jsx)(o.H2,{}),(0,t.jsx)(o.of,{nodeColor:e=>{var s;let a=null==(s=e.data)?void 0:s.fileNode;return j(null==a?void 0:a.language)},nodeStrokeWidth:3,zoomable:!0,pannable:!0}),(0,t.jsx)(o.VS,{variant:o._5.Dots,gap:20,size:1,color:"#e5e7eb"})]}),a&&(0,t.jsx)("div",{className:"absolute top-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 max-h-96 overflow-y-auto",children:(()=>{let e=s.graph.nodes.find(e=>e.id===a);return e?(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.path})]}),e.language&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Dil:"}),(0,t.jsx)("span",{className:"ml-2 text-sm px-2 py-1 rounded text-white",style:{backgroundColor:j(e.language)},children:e.language})]}),e.size&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Boyut:"}),(0,t.jsxs)("span",{className:"ml-2 text-sm text-gray-600 dark:text-gray-400",children:[(e.size/1024).toFixed(1)," KB"]})]}),e.dependencies&&e.dependencies.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Bağımlılıklar (",e.dependencies.length,")"]}),(0,t.jsx)("div",{className:"space-y-1 max-h-32 overflow-y-auto",children:e.dependencies.map((e,s)=>(0,t.jsx)("div",{className:"text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded",children:e},s))})]}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700",children:[e.content&&(0,t.jsx)("button",{onClick:()=>i(e),className:"flex-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 py-2",children:"D\xfczenle"}),(0,t.jsx)("button",{onClick:()=>l(null),className:"flex-1 text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 py-2",children:"Kapat"})]})]}):null})()}),n&&(0,t.jsx)(N,{fileNode:n,onClose:()=>i(null),onSave:e=>{console.log("Saving content for",n.name,e),i(null)}})]})}var A=a(4395),S=a(1380),z=a(6474),C=a(3052),D=a(3717);function B(e){let{analysisResult:s}=e,[a,n]=(0,r.useState)(new Set),[i,d]=(0,r.useState)(null),[c,x]=(0,r.useState)(null),o=e=>{let{node:s,level:r,onNodeSelect:i,selectedNode:d}=e,c=a.has(s.id),x=(null==d?void 0:d.id)===s.id,m=s.children&&s.children.length>0,y=(e=>{var s;if("directory"===e.type)return a.has(e.id)?A.A:S.A;switch(null==(s=e.name.split(".").pop())?void 0:s.toLowerCase()){case"js":case"jsx":case"ts":case"tsx":case"py":case"java":case"cpp":case"c":case"cs":case"php":case"rb":case"go":case"rs":case"swift":case"kt":return h.A;case"png":case"jpg":case"jpeg":case"gif":case"svg":case"webp":return g.A;case"json":case"xml":case"yaml":case"yml":case"toml":case"ini":return p.A;default:return u.A}})(s);return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:(0,l.$)("flex items-center py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded",x&&"bg-blue-100 dark:bg-blue-900","transition-colors duration-150"),style:{paddingLeft:"".concat(20*r+8,"px")},onClick:()=>{"directory"===s.type&&m&&(e=>{let s=new Set(a);s.has(e)?s.delete(e):s.add(e),n(s)})(s.id),i(s)},children:["directory"===s.type&&m&&(0,t.jsx)("div",{className:"mr-1",children:c?(0,t.jsx)(z.A,{className:"w-4 h-4 text-gray-400"}):(0,t.jsx)(C.A,{className:"w-4 h-4 text-gray-400"})}),(!m||"file"===s.type)&&(0,t.jsx)("div",{className:"w-5 mr-1"}),(0,t.jsx)(y,{className:(0,l.$)("w-4 h-4 mr-2",s.language&&({javascript:"text-yellow-600",typescript:"text-blue-600",python:"text-green-600",java:"text-red-600",cpp:"text-purple-600",c:"text-gray-600",csharp:"text-indigo-600",php:"text-purple-500",ruby:"text-red-500",go:"text-cyan-600",rust:"text-orange-600",swift:"text-orange-500",kotlin:"text-purple-700",html:"text-orange-500",css:"text-blue-500",scss:"text-pink-500",json:"text-yellow-500",markdown:"text-gray-700"})[s.language||""]||"text-gray-500")}),(0,t.jsx)("span",{className:(0,l.$)("text-sm truncate",x?"text-blue-900 dark:text-blue-100 font-medium":"text-gray-700 dark:text-gray-300"),children:s.name}),s.dependencies&&s.dependencies.length>0&&(0,t.jsx)("span",{className:"ml-auto text-xs text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 px-1.5 py-0.5 rounded",children:s.dependencies.length})]}),"directory"===s.type&&m&&c&&(0,t.jsx)("div",{children:s.children.map(e=>(0,t.jsx)(o,{node:e,level:r+1,onNodeSelect:i,selectedNode:d},e.id))})]})},m=s.graph.nodes.filter(e=>!e.path.includes("/")||1===e.path.split("/").length);return(0,t.jsxs)("div",{className:"flex h-[600px]",children:[(0,t.jsx)("div",{className:"w-1/2 border-r border-gray-200 dark:border-gray-700 overflow-y-auto",children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Dosya Yapısı"}),(0,t.jsx)("div",{className:"space-y-1",children:m.map(e=>(0,t.jsx)(o,{node:e,level:0,onNodeSelect:d,selectedNode:i},e.id))})]})}),(0,t.jsx)("div",{className:"w-1/2 p-4 overflow-y-auto",children:i?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:i.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Yol:"})," ",i.path]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tip:"})," ","file"===i.type?"Dosya":"Klas\xf6r"]}),i.language&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Dil:"})," ",i.language]}),i.size&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Boyut:"})," ",(i.size/1024).toFixed(1)," KB"]})]})]}),i.dependencies&&i.dependencies.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-2",children:["Bağımlılıklar (",i.dependencies.length,")"]}),(0,t.jsx)("div",{className:"space-y-1",children:i.dependencies.map((e,s)=>(0,t.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded",children:e},s))})]}),i.content&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white",children:"İ\xe7erik \xd6nizleme"}),(0,t.jsxs)("button",{onClick:()=>x(i),className:"flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",children:[(0,t.jsx)(D.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"D\xfczenle"})]})]}),(0,t.jsxs)("pre",{className:"text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto max-h-64 overflow-y-auto",children:[(0,t.jsx)("code",{children:i.content.slice(0,1e3)}),i.content.length>1e3&&(0,t.jsx)("div",{className:"text-gray-500 mt-2",children:"... (devamı var)"})]})]})]}):(0,t.jsxs)("div",{className:"text-center text-gray-500 dark:text-gray-400 mt-8",children:[(0,t.jsx)(u.A,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),(0,t.jsx)("p",{children:"Detayları g\xf6rmek i\xe7in bir dosya veya klas\xf6r se\xe7in"})]})}),c&&(0,t.jsx)(N,{fileNode:c,onClose:()=>x(null),onSave:e=>{console.log("Saving content for",c.name,e),x(null)}})]})}var R=a(6766),_=a(3786),L=a(8564),E=a(171),F=a(2657);function O(e){let{analysisResult:s}=e,{repo:a,languages:r,stats:l}=s,n=Object.entries(r).sort((e,s)=>{let[,a]=e,[,t]=s;return t-a}),i=Object.values(r).reduce((e,s)=>e+s,0);return(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:a.full_name}),(0,t.jsx)("a",{href:a.html_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",children:(0,t.jsx)(_.A,{className:"w-4 h-4"})})]}),a.description&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-3",children:a.description}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(L.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Stars"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(E.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Forks"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(F.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Watchers"})]})]})]}),(0,t.jsx)("div",{className:"flex-shrink-0 ml-4",children:(0,t.jsx)(R.default,{src:a.owner.avatar_url,alt:a.owner.login,width:48,height:48,className:"w-12 h-12 rounded-full"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"İstatistikler"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Dosyalar"})]}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:l.totalFiles})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(S.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Klas\xf6rler"})]}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:l.totalDirectories})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Bağımlılıklar"})]}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:l.totalDependencies})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Programlama Dilleri"}),(0,t.jsx)("div",{className:"space-y-2",children:n.slice(0,5).map(e=>{let[s,a]=e,r=(a/i*100).toFixed(1);return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:s}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-500",children:[r,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5",children:(0,t.jsx)("div",{className:"bg-blue-600 h-1.5 rounded-full",style:{width:"".concat(r,"%")}})})]},s)})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Repository Detayları"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Sahip: "}),(0,t.jsx)("span",{className:"text-gray-900 dark:text-white",children:a.owner.login})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Ana Dal: "}),(0,t.jsx)("span",{className:"text-gray-900 dark:text-white",children:a.default_branch})]}),a.language&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Ana Dil: "}),(0,t.jsx)("span",{className:"text-gray-900 dark:text-white",children:a.language})]})]})]})]})]})}var K=a(5169),U=a(8239),G=a(9302);function M(e){let{analysisResult:s,onReset:a}=e,[l,i]=(0,r.useState)("tree");return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n,{variant:"outline",onClick:a,className:"flex items-center space-x-2",children:[(0,t.jsx)(K.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Yeni Analiz"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n,{variant:"tree"===l?"default":"outline",onClick:()=>i("tree"),className:"flex items-center space-x-2",children:[(0,t.jsx)(U.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Tree View"})]}),(0,t.jsxs)(n,{variant:"graph"===l?"default":"outline",onClick:()=>i("graph"),className:"flex items-center space-x-2",children:[(0,t.jsx)(G.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Node Graph"})]})]})]}),(0,t.jsx)(O,{analysisResult:s}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border",children:"tree"===l?(0,t.jsx)(B,{analysisResult:s}):(0,t.jsx)(w,{analysisResult:s})})]})}function T(){let[e,s]=(0,r.useState)(null),[a,l]=(0,r.useState)(!1);return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"BaseGraph"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"GitHub kod tabanı g\xf6rselleştirme ve analiz aracı"})]})})})}),(0,t.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[!e&&!a&&(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"GitHub Repository Analizi"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:"Bir GitHub repository URL'si girin ve kod tabanınızın g\xf6rsel analizini g\xf6r\xfcn"}),(0,t.jsx)(x,{onAnalysisStart:()=>{l(!0),s(null)},onAnalysisComplete:e=>{s(e),l(!1)}})]})}),a&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Repository analiz ediliyor..."})]}),e&&(0,t.jsx)(M,{analysisResult:e,onReset:()=>s(null)})]})]})}}},e=>{e.O(0,[946,862,811,441,964,358],()=>e(e.s=3169)),_N_E=e.O()}]);