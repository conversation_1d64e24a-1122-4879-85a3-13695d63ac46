'use client';

import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { AnalysisResult, FileNode } from '@/types';
import { FileNodeComponent } from './nodes/FileNodeComponent';
import { CodeEditor } from './CodeEditor';

interface NodeGraphViewProps {
  analysisResult: AnalysisResult;
}

const nodeTypes = {
  fileNode: FileNodeComponent,
};

export function NodeGraphView({ analysisResult }: NodeGraphViewProps) {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [editingNode, setEditingNode] = useState<FileNode | null>(null);

  // React Flow node'larını oluştur
  const initialNodes: Node[] = useMemo(() => {
    const nodes: Node[] = [];
    const { graph } = analysisResult;

    // Sadece dosyaları node olar<PERSON> (klasörleri değil)
    const fileNodes = graph.nodes.filter(node => node.type === 'file');

    fileNodes.forEach((fileNode, index) => {
      // Grid layout için pozisyon hesapla
      const cols = Math.ceil(Math.sqrt(fileNodes.length));
      const row = Math.floor(index / cols);
      const col = index % cols;
      
      const x = col * 250 + 50;
      const y = row * 150 + 50;

      nodes.push({
        id: fileNode.id,
        type: 'fileNode',
        position: { x, y },
        data: {
          fileNode,
          isSelected: false,
          onSelect: (nodeId: string) => setSelectedNode(nodeId),
        },
        draggable: true,
      });
    });

    return nodes;
  }, [analysisResult]);

  // React Flow edge'lerini oluştur
  const initialEdges: Edge[] = useMemo(() => {
    return analysisResult.graph.edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      type: 'smoothstep',
      animated: true,
      label: edge.label,
      style: {
        stroke: '#6366f1',
        strokeWidth: 2,
      },
      labelStyle: {
        fontSize: 10,
        fontWeight: 600,
      },
      labelBgStyle: {
        fill: '#ffffff',
        fillOpacity: 0.8,
      },
    }));
  }, [analysisResult]);

  const [nodes, , onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Node seçimi değiştiğinde node'ları güncelle
  const updatedNodes = useMemo(() => {
    return nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        isSelected: node.id === selectedNode,
      },
    }));
  }, [nodes, selectedNode]);

  const getLanguageColor = (language?: string): string => {
    const colors: Record<string, string> = {
      javascript: '#f7df1e',
      typescript: '#3178c6',
      python: '#3776ab',
      java: '#ed8b00',
      cpp: '#00599c',
      c: '#a8b9cc',
      csharp: '#239120',
      php: '#777bb4',
      ruby: '#cc342d',
      go: '#00add8',
      rust: '#dea584',
      swift: '#fa7343',
      kotlin: '#7f52ff',
      html: '#e34f26',
      css: '#1572b6',
      scss: '#cf649a',
      json: '#000000',
      markdown: '#083fa1',
    };
    return colors[language || ''] || '#6b7280';
  };

  return (
    <div className="h-[600px] w-full">
      <ReactFlow
        nodes={updatedNodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Controls />
        <MiniMap 
          nodeColor={(node) => {
            const fileNode = node.data?.fileNode as FileNode;
            return getLanguageColor(fileNode?.language);
          }}
          nodeStrokeWidth={3}
          zoomable
          pannable
        />
        <Background 
          variant={BackgroundVariant.Dots} 
          gap={20} 
          size={1}
          color="#e5e7eb"
        />
      </ReactFlow>

      {/* Selected Node Info Panel */}
      {selectedNode && (
        <div className="absolute top-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 max-h-96 overflow-y-auto">
          {(() => {
            const node = analysisResult.graph.nodes.find(n => n.id === selectedNode);
            if (!node) return null;

            return (
              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {node.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {node.path}
                  </p>
                </div>

                {node.language && (
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Dil: 
                    </span>
                    <span 
                      className="ml-2 text-sm px-2 py-1 rounded text-white"
                      style={{ backgroundColor: getLanguageColor(node.language) }}
                    >
                      {node.language}
                    </span>
                  </div>
                )}

                {node.size && (
                  <div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Boyut: 
                    </span>
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                      {(node.size / 1024).toFixed(1)} KB
                    </span>
                  </div>
                )}

                {node.dependencies && node.dependencies.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bağımlılıklar ({node.dependencies.length})
                    </h4>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {node.dependencies.map((dep, index) => (
                        <div
                          key={index}
                          className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded"
                        >
                          {dep}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                  {node.content && (
                    <button
                      onClick={() => setEditingNode(node)}
                      className="flex-1 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 py-2"
                    >
                      Düzenle
                    </button>
                  )}
                  <button
                    onClick={() => setSelectedNode(null)}
                    className="flex-1 text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 py-2"
                  >
                    Kapat
                  </button>
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* Code Editor Modal */}
      {editingNode && (
        <CodeEditor
          fileNode={editingNode}
          onClose={() => setEditingNode(null)}
          onSave={(content) => {
            console.log('Saving content for', editingNode.name, content);
            setEditingNode(null);
          }}
        />
      )}
    </div>
  );
}
