# BaseGraph

GitHub kod tabanlarını görsel olarak analiz eden ve düzenleme imkanı sunan modern web uygulaması.

## 🚀 Özellikler

- **GitHub Repository Analizi**: Herhangi bir public GitHub repository'sini analiz edin
- **Görsel Kod Haritası**: İki farklı görünüm modu:
  - **Tree View**: Hiyerarşik dosya yapısı görünümü
  - **Node Graph**: Unreal Blueprint/Blender benzeri node tabanlı görsel editör
- **Kod Bağımlılık Analizi**: Dosyalar arası import/require ilişkilerini otomatik tespit
- **AI Destekli Fonksiyon Analizi**: Fonksiyon tanımları, çağrıları ve ilişkilerini AI ile tespit
- **Çoklu AI Sağlayıcı Desteği**: OpenA<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ollama ve diğer OpenAI-compatible sağlayıcılar
- **Çoklu Dil Desteği**: JavaScript, TypeScript, Python, Java, C++, Go, Rust ve daha fazlası
- **Interaktif Kod Editörü**: Dosyaları doğrudan tarayıcıda görüntüleyin ve düzenleyin
- **Responsive Tasarım**: Masaüstü ve mobil cihazlarda mükemmel çalışır
- **Dark Mode**: Göz dostu karanlık tema desteği

## 🛠️ Teknoloji Yığını

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Görselleştirme**: React Flow (node tabanlı UI)
- **API**: GitHub REST API
- **İkonlar**: Lucide React
- **Kod Analizi**: Custom parsers for multiple languages

## 📦 Kurulum

1. Repository'yi klonlayın:
```bash
git clone https://github.com/inkbytefo/basegraph.git
cd basegraph
```

2. Bağımlılıkları yükleyin:
```bash
npm install
```

3. Development server'ı başlatın:
```bash
npm run dev
```

4. Tarayıcınızda [http://localhost:3000](http://localhost:3000) adresini açın.

## ⚙️ AI Konfigürasyonu

BaseGraph, kod analizi için AI destekli fonksiyon tespiti kullanır. Farklı AI sağlayıcılarını destekler:

### Desteklenen AI Sağlayıcılar

- **OpenAI**: GPT-4, GPT-4o, GPT-3.5-turbo
- **Anthropic Claude**: Claude-3 Opus, Sonnet, Haiku
- **Groq**: Llama3, Mixtral modelleri (hızlı inference)
- **Ollama**: Yerel AI modelleri (API key gerektirmez)
- **Together AI**: Açık kaynak modeller
- **Diğer OpenAI-compatible sağlayıcılar**

### Konfigürasyon

1. **UI üzerinden**: Ana sayfada "AI Settings" butonuna tıklayın
2. **Environment variables**: `.env.local` dosyasını düzenleyin

```bash
# OpenAI (Varsayılan)
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# Alternatif sağlayıcılar için
OPENAI_BASE_URL=https://api.groq.com/openai/v1  # Groq örneği
OPENAI_MODEL=llama3-8b-8192

# Yerel Ollama
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_MODEL=codellama:7b
```

## 🎯 Kullanım

1. **Repository URL'si Girin**: Ana sayfada bir GitHub repository URL'si girin
   - Örnek: `https://github.com/facebook/react`
   - Örnek: `https://github.com/microsoft/vscode`

2. **Analiz Edin**: "Analiz Et" butonuna tıklayın ve repository'nin analiz edilmesini bekleyin

3. **Görünüm Modunu Seçin**:
   - **Tree View**: Klasik dosya ağacı görünümü ile dosyaları keşfedin
   - **Node Graph**: Dosyaları node'lar olarak görüp bağımlılıkları görsel olarak inceleyin

4. **Dosyaları İnceleyin**: Herhangi bir dosyaya tıklayarak detaylarını görün

5. **Kod Düzenleyin**: "Düzenle" butonuna tıklayarak dosyaları tam ekran editörde açın

## 🔧 Desteklenen Dosya Türleri

- **JavaScript/TypeScript**: .js, .jsx, .ts, .tsx
- **Python**: .py
- **Java**: .java
- **C/C++**: .c, .cpp, .h, .hpp
- **C#**: .cs
- **Go**: .go
- **Rust**: .rs
- **PHP**: .php
- **Ruby**: .rb
- **Swift**: .swift
- **Kotlin**: .kt
- **HTML/CSS**: .html, .css, .scss, .sass
- **Markup**: .json, .xml, .yaml, .md

## 🎨 Özellik Detayları

### Tree View
- Hiyerarşik dosya yapısı görünümü
- Dosya türlerine göre renkli ikonlar
- Bağımlılık sayısı gösterimi
- Fonksiyon listesi ve detayları
- Fonksiyon çağrı ilişkileri
- Dosya detayları paneli
- İçerik önizleme

### Node Graph
- Interactive node tabanlı görselleştirme
- Dosyalar arası bağımlılık okları (mavi çizgiler)
- Fonksiyon çağrı ilişkileri (yeşil kesikli çizgiler)
- Zoom ve pan desteği
- Mini harita navigasyonu
- Programlama diline göre renk kodlaması
- Bağlantı türleri legend'ı

### Kod Editörü
- Syntax highlighting
- Satır numaraları
- Dosya indirme
- Değişiklik takibi
- Tam ekran düzenleme

## 🚧 Gelecek Özellikler

- [ ] GitHub token desteği (private repository'ler için)
- [ ] Kod arama ve filtreleme
- [ ] Commit geçmişi analizi
- [ ] Daha gelişmiş syntax highlighting
- [ ] Dosya karşılaştırma
- [ ] Export/import işlevleri
- [ ] Collaborative editing

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın.

## 👨‍💻 Geliştirici

**inkbytefo** - [GitHub](https://github.com/inkbytefo)

## 🙏 Teşekkürler

- [React Flow](https://reactflow.dev/) - Mükemmel node tabanlı UI kütüphanesi
- [Lucide](https://lucide.dev/) - Güzel ikonlar
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [GitHub API](https://docs.github.com/en/rest) - Repository verilerine erişim
