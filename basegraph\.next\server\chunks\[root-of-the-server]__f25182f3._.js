module.exports = {

"[project]/.next-internal/server/app/api/analyze/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/github.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GitHubService": ()=>GitHubService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$rest$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/rest/dist-src/index.js [app-route] (ecmascript)");
;
class GitHubService {
    octokit;
    constructor(token){
        this.octokit = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$rest$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Octokit"]({
            auth: token
        });
    }
    async getRepository(owner, repo) {
        try {
            const { data } = await this.octokit.rest.repos.get({
                owner,
                repo
            });
            return data;
        } catch (error) {
            throw new Error(`Failed to fetch repository: ${error}`);
        }
    }
    async getRepositoryContents(owner, repo, path = '') {
        try {
            const { data } = await this.octokit.rest.repos.getContent({
                owner,
                repo,
                path
            });
            return Array.isArray(data) ? data : [
                data
            ];
        } catch (error) {
            throw new Error(`Failed to fetch repository contents: ${error}`);
        }
    }
    async getFileContent(owner, repo, path) {
        try {
            const { data } = await this.octokit.rest.repos.getContent({
                owner,
                repo,
                path
            });
            if ('content' in data && data.content) {
                return Buffer.from(data.content, 'base64').toString('utf-8');
            }
            throw new Error('File content not found');
        } catch (error) {
            throw new Error(`Failed to fetch file content: ${error}`);
        }
    }
    async getRepositoryLanguages(owner, repo) {
        try {
            const { data } = await this.octokit.rest.repos.listLanguages({
                owner,
                repo
            });
            return data;
        } catch (error) {
            throw new Error(`Failed to fetch repository languages: ${error}`);
        }
    }
    parseRepoUrl(url) {
        const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (match) {
            return {
                owner: match[1],
                repo: match[2].replace(/\.git$/, '')
            };
        }
        return null;
    }
}
}),
"[project]/src/lib/codeAnalyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CodeAnalyzer": ()=>CodeAnalyzer
});
class CodeAnalyzer {
    static SUPPORTED_EXTENSIONS = {
        '.js': 'javascript',
        '.jsx': 'javascript',
        '.ts': 'typescript',
        '.tsx': 'typescript',
        '.py': 'python',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby',
        '.go': 'go',
        '.rs': 'rust',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.sass': 'sass',
        '.less': 'less',
        '.json': 'json',
        '.xml': 'xml',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.md': 'markdown',
        '.sql': 'sql'
    };
    static getLanguageFromExtension(filename) {
        const ext = filename.substring(filename.lastIndexOf('.'));
        return this.SUPPORTED_EXTENSIONS[ext];
    }
    static extractDependencies(content, language) {
        const dependencies = [];
        switch(language){
            case 'javascript':
            case 'typescript':
                dependencies.push(...this.extractJavaScriptDependencies(content));
                break;
            case 'python':
                dependencies.push(...this.extractPythonDependencies(content));
                break;
            case 'java':
                dependencies.push(...this.extractJavaDependencies(content));
                break;
            case 'cpp':
            case 'c':
                dependencies.push(...this.extractCDependencies(content));
                break;
            case 'csharp':
                dependencies.push(...this.extractCSharpDependencies(content));
                break;
            case 'go':
                dependencies.push(...this.extractGoDependencies(content));
                break;
            case 'rust':
                dependencies.push(...this.extractRustDependencies(content));
                break;
            default:
                break;
        }
        return [
            ...new Set(dependencies)
        ]; // Remove duplicates
    }
    static extractJavaScriptDependencies(content) {
        const dependencies = [];
        // ES6 imports
        const importRegex = /import\s+(?:.*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
        let match;
        while((match = importRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        // CommonJS requires
        const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
        while((match = requireRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        // Dynamic imports
        const dynamicImportRegex = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
        while((match = dynamicImportRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        return dependencies;
    }
    static extractPythonDependencies(content) {
        const dependencies = [];
        // import statements
        const importRegex = /^import\s+([^\s#]+)/gm;
        let match;
        while((match = importRegex.exec(content)) !== null){
            dependencies.push(match[1].split('.')[0]);
        }
        // from ... import statements
        const fromImportRegex = /^from\s+([^\s#]+)\s+import/gm;
        while((match = fromImportRegex.exec(content)) !== null){
            dependencies.push(match[1].split('.')[0]);
        }
        return dependencies;
    }
    static extractJavaDependencies(content) {
        const dependencies = [];
        // import statements
        const importRegex = /^import\s+(?:static\s+)?([^;]+);/gm;
        let match;
        while((match = importRegex.exec(content)) !== null){
            const packageName = match[1].trim();
            dependencies.push(packageName);
        }
        return dependencies;
    }
    static extractCDependencies(content) {
        const dependencies = [];
        // #include statements
        const includeRegex = /#include\s*[<"]([^>"]+)[>"]/g;
        let match;
        while((match = includeRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        return dependencies;
    }
    static extractCSharpDependencies(content) {
        const dependencies = [];
        // using statements
        const usingRegex = /^using\s+([^;]+);/gm;
        let match;
        while((match = usingRegex.exec(content)) !== null){
            dependencies.push(match[1].trim());
        }
        return dependencies;
    }
    static extractGoDependencies(content) {
        const dependencies = [];
        // import statements
        const importRegex = /import\s+(?:\(\s*([^)]+)\s*\)|"([^"]+)")/g;
        let match;
        while((match = importRegex.exec(content)) !== null){
            if (match[1]) {
                // Multi-line import
                const imports = match[1].split('\n').map((line)=>{
                    const trimmed = line.trim();
                    const quoted = trimmed.match(/"([^"]+)"/);
                    return quoted ? quoted[1] : null;
                }).filter(Boolean);
                dependencies.push(...imports);
            } else if (match[2]) {
                // Single import
                dependencies.push(match[2]);
            }
        }
        return dependencies;
    }
    static extractRustDependencies(content) {
        const dependencies = [];
        // use statements
        const useRegex = /^use\s+([^;]+);/gm;
        let match;
        while((match = useRegex.exec(content)) !== null){
            const usePath = match[1].trim();
            const rootModule = usePath.split('::')[0];
            dependencies.push(rootModule);
        }
        // extern crate statements
        const externRegex = /^extern\s+crate\s+([^;]+);/gm;
        while((match = externRegex.exec(content)) !== null){
            dependencies.push(match[1].trim());
        }
        return dependencies;
    }
    static createDependencyEdges(nodes) {
        const edges = [];
        const nodeMap = new Map(nodes.map((node)=>[
                node.path,
                node
            ]));
        nodes.forEach((node)=>{
            if (node.dependencies) {
                node.dependencies.forEach((dep)=>{
                    // Try to find the dependency in the current project
                    const targetNode = this.findDependencyTarget(dep, nodeMap, node.path);
                    if (targetNode) {
                        edges.push({
                            id: `${node.id}-${targetNode.id}`,
                            source: node.id,
                            target: targetNode.id,
                            type: 'import',
                            label: dep
                        });
                    }
                });
            }
        });
        return edges;
    }
    static findDependencyTarget(dependency, nodeMap, sourcePath) {
        // Handle relative imports
        if (dependency.startsWith('./') || dependency.startsWith('../')) {
            const resolvedPath = this.resolvePath(sourcePath, dependency);
            return nodeMap.get(resolvedPath) || null;
        }
        // Handle absolute imports within the project
        for (const [path, node] of nodeMap){
            if (path.includes(dependency) || node.name.includes(dependency)) {
                return node;
            }
        }
        return null;
    }
    static resolvePath(basePath, relativePath) {
        const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
        const parts = baseDir.split('/').concat(relativePath.split('/'));
        const resolved = [];
        parts.forEach((part)=>{
            if (part === '..') {
                resolved.pop();
            } else if (part !== '.' && part !== '') {
                resolved.push(part);
            }
        });
        return resolved.join('/');
    }
}
}),
"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/github.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/codeAnalyzer.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { url, token } = await request.json();
        if (!url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Repository URL is required'
            }, {
                status: 400
            });
        }
        const githubService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GitHubService"](token);
        const repoInfo = githubService.parseRepoUrl(url);
        if (!repoInfo) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid GitHub URL format'
            }, {
                status: 400
            });
        }
        const { owner, repo } = repoInfo;
        try {
            // Repository bilgilerini al
            const repository = await githubService.getRepository(owner, repo);
            const languages = await githubService.getRepositoryLanguages(owner, repo);
            // Repository içeriğini analiz et
            const nodes = await analyzeRepositoryContents(githubService, owner, repo);
            const edges = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].createDependencyEdges(nodes);
            const graph = {
                nodes,
                edges
            };
            const result = {
                repo: repository,
                graph,
                languages,
                stats: {
                    totalFiles: nodes.filter((n)=>n.type === 'file').length,
                    totalDirectories: nodes.filter((n)=>n.type === 'directory').length,
                    totalDependencies: edges.length
                }
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
        } catch (error) {
            console.error('GitHub API Error:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to fetch repository data. Please check if the repository exists and is public.'
            }, {
                status: 404
            });
        }
    } catch (error) {
        console.error('Analysis Error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function analyzeRepositoryContents(githubService, owner, repo, path = '', depth = 0) {
    const nodes = [];
    const maxDepth = 3; // Maksimum derinlik sınırı
    const maxFiles = 100; // Maksimum dosya sayısı sınırı
    if (depth >= maxDepth) {
        return nodes;
    }
    try {
        const contents = await githubService.getRepositoryContents(owner, repo, path);
        // İçerikleri sırala: önce klasörler, sonra dosyalar
        const sortedContents = contents.sort((a, b)=>{
            if (a.type === 'dir' && b.type === 'file') return -1;
            if (a.type === 'file' && b.type === 'dir') return 1;
            return a.name.localeCompare(b.name);
        });
        let fileCount = 0;
        for (const item of sortedContents){
            if (fileCount >= maxFiles) break;
            const nodeId = `${path}/${item.name}`.replace(/^\//, '') || item.name;
            const node = {
                id: nodeId,
                name: item.name,
                path: item.path,
                type: item.type === 'dir' ? 'directory' : 'file',
                size: item.size
            };
            if (item.type === 'file') {
                fileCount++;
                const language = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].getLanguageFromExtension(item.name);
                if (language) {
                    node.language = language;
                    // Sadece küçük dosyaları analiz et (50KB'den küçük)
                    if (item.size && item.size < 50000) {
                        try {
                            const content = await githubService.getFileContent(owner, repo, item.path);
                            node.content = content;
                            node.dependencies = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].extractDependencies(content, language);
                        } catch (contentError) {
                            console.warn(`Could not fetch content for ${item.path}:`, contentError);
                        }
                    }
                }
            } else if (item.type === 'dir') {
                // Belirli klasörleri atla
                const skipDirs = [
                    'node_modules',
                    '.git',
                    'dist',
                    'build',
                    '.next',
                    'coverage',
                    '__pycache__'
                ];
                if (!skipDirs.includes(item.name)) {
                    const children = await analyzeRepositoryContents(githubService, owner, repo, item.path, depth + 1);
                    node.children = children;
                    nodes.push(...children);
                }
            }
            nodes.push(node);
        }
    } catch (error) {
        console.warn(`Could not analyze path ${path}:`, error);
    }
    return nodes;
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f25182f3._.js.map