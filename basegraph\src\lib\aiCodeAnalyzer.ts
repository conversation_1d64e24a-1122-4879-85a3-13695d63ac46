import OpenAI from 'openai';
import { FunctionInfo, FunctionCall } from '@/types';

export interface AICodeAnalysisResult {
  functions: FunctionInfo[];
  imports: string[];
  exports: string[];
  classes?: {
    name: string;
    methods: string[];
    startLine: number;
    endLine: number;
  }[];
}

export interface AIConfig {
  apiKey: string;
  baseURL?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

export class AICodeAnalyzer {
  private openai: OpenAI;
  private model: string;
  private maxTokens: number;
  private temperature: number;
  private static instance: AICodeAnalyzer;

  constructor(config?: AIConfig) {
    const apiKey = config?.apiKey || process.env.OPENAI_API_KEY || '';
    const baseURL = config?.baseURL || process.env.OPENAI_BASE_URL;

    this.model = config?.model || process.env.OPENAI_MODEL || 'gpt-4o-mini';
    this.maxTokens = config?.maxTokens || parseInt(process.env.OPENAI_MAX_TOKENS || '4000');
    this.temperature = config?.temperature || parseFloat(process.env.OPENAI_TEMPERATURE || '0.1');

    this.openai = new OpenAI({
      apiKey,
      baseURL,
    });
  }

  static getInstance(config?: AIConfig): AICodeAnalyzer {
    if (!AICodeAnalyzer.instance) {
      AICodeAnalyzer.instance = new AICodeAnalyzer(config);
    }
    return AICodeAnalyzer.instance;
  }

  async analyzeCode(content: string, language: string, filename: string): Promise<AICodeAnalysisResult> {
    try {
      const prompt = this.createAnalysisPrompt(content, language, filename);
      
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert code analyzer. Analyze the provided code and return a detailed JSON response with function definitions, calls, imports, and exports.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: this.temperature,
        max_tokens: this.maxTokens,
      });

      const result = response.choices[0]?.message?.content;
      if (!result) {
        throw new Error('No response from AI model');
      }

      return this.parseAIResponse(result);
    } catch (error) {
      console.error('AI Code Analysis Error:', error);
      // Fallback to empty result
      return {
        functions: [],
        imports: [],
        exports: [],
        classes: []
      };
    }
  }

  private createAnalysisPrompt(content: string, language: string, filename: string): string {
    return `
Analyze this ${language} code file (${filename}) and extract detailed information about functions, classes, imports, and exports.

CODE:
\`\`\`${language}
${content}
\`\`\`

Please return a JSON response with the following structure:
{
  "functions": [
    {
      "name": "functionName",
      "startLine": 10,
      "endLine": 25,
      "parameters": ["param1: string", "param2: number"],
      "returnType": "string",
      "isExported": true,
      "isAsync": false,
      "calls": [
        {
          "functionName": "otherFunction",
          "line": 15,
          "isExternal": false,
          "targetFile": null
        }
      ]
    }
  ],
  "imports": ["./utils", "lodash", "react"],
  "exports": ["mainFunction", "helperFunction"],
  "classes": [
    {
      "name": "ClassName",
      "methods": ["method1", "method2"],
      "startLine": 30,
      "endLine": 50
    }
  ]
}

IMPORTANT RULES:
1. Include ALL function definitions (regular functions, arrow functions, methods, constructors)
2. For each function, list ALL function calls made within it
3. Mark isExternal=true for calls to functions not defined in this file
4. Include accurate line numbers (1-based indexing)
5. Extract parameter types and return types when available
6. Include both named and default exports
7. Return valid JSON only, no additional text
8. If you can't determine something, use null or empty array
9. For function calls, include the exact line number where the call occurs
10. Consider method calls on objects as function calls too (e.g., obj.method())

Focus on accuracy and completeness. This analysis will be used to build a code dependency graph.
`;
  }

  private parseAIResponse(response: string): AICodeAnalysisResult {
    try {
      // Clean the response - remove markdown code blocks if present
      const cleanedResponse = response
        .replace(/```json\n?/g, '')
        .replace(/```\n?/g, '')
        .trim();

      const parsed = JSON.parse(cleanedResponse);
      
      // Validate and normalize the response
      return {
        functions: Array.isArray(parsed.functions) ? parsed.functions : [],
        imports: Array.isArray(parsed.imports) ? parsed.imports : [],
        exports: Array.isArray(parsed.exports) ? parsed.exports : [],
        classes: Array.isArray(parsed.classes) ? parsed.classes : []
      };
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      console.error('Raw response:', response);
      
      // Return empty result on parse error
      return {
        functions: [],
        imports: [],
        exports: [],
        classes: []
      };
    }
  }

  // Cache mechanism for repeated analysis of same content
  private static cache = new Map<string, AICodeAnalysisResult>();

  async analyzeCodeWithCache(content: string, language: string, filename: string): Promise<AICodeAnalysisResult> {
    const cacheKey = this.generateCacheKey(content, language);
    
    if (AICodeAnalyzer.cache.has(cacheKey)) {
      return AICodeAnalyzer.cache.get(cacheKey)!;
    }

    const result = await this.analyzeCode(content, language, filename);
    AICodeAnalyzer.cache.set(cacheKey, result);
    
    return result;
  }

  private generateCacheKey(content: string, language: string): string {
    // Simple hash function for caching
    let hash = 0;
    const str = content + language;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Clear cache when needed
  static clearCache(): void {
    AICodeAnalyzer.cache.clear();
  }
}
