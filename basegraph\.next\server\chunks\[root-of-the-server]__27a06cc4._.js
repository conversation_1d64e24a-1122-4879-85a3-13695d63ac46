module.exports = {

"[project]/.next-internal/server/app/api/analyze/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/github.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GitHubService": ()=>GitHubService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$rest$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@octokit/rest/dist-src/index.js [app-route] (ecmascript)");
;
class GitHubService {
    octokit;
    rateLimitRemaining = 60;
    rateLimitReset = 0;
    constructor(token){
        this.octokit = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$octokit$2f$rest$2f$dist$2d$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Octokit"]({
            auth: token,
            request: {
                retries: 3,
                retryAfter: 2
            }
        });
    }
    async checkRateLimit() {
        try {
            const { data } = await this.octokit.rest.rateLimit.get();
            this.rateLimitRemaining = data.rate.remaining;
            this.rateLimitReset = data.rate.reset;
            if (this.rateLimitRemaining < 10) {
                const resetTime = new Date(this.rateLimitReset * 1000);
                const waitTime = resetTime.getTime() - Date.now();
                if (waitTime > 0) {
                    console.warn(`GitHub API rate limit low (${this.rateLimitRemaining} remaining). Reset at ${resetTime.toISOString()}`);
                    throw new Error(`GitHub API rate limit exceeded. Please wait until ${resetTime.toLocaleString()} or use a GitHub token for higher limits.`);
                }
            }
        } catch (error) {
            // If rate limit check fails, continue but log the error
            console.warn('Failed to check GitHub rate limit:', error);
        }
    }
    handleGitHubError(error, context) {
        // Type guard for GitHub API errors
        const isGitHubError = (err)=>{
            return typeof err === 'object' && err !== null && 'status' in err;
        };
        if (isGitHubError(error)) {
            if (error.status === 403 && error.message?.includes('rate limit')) {
                const resetTime = error.response?.headers?.['x-ratelimit-reset'];
                const resetDate = resetTime ? new Date(parseInt(resetTime) * 1000) : new Date(Date.now() + 3600000);
                throw new Error(`GitHub API rate limit exceeded. ` + `Reset time: ${resetDate.toLocaleString()}. ` + `To get higher limits, add a GitHub token in the repository input field.`);
            }
            if (error.status === 404) {
                throw new Error(`Repository not found or is private. Please check the URL and ensure the repository is public.`);
            }
            if (error.status === 401) {
                throw new Error(`GitHub authentication failed. Please check your token.`);
            }
        }
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`${context}: ${errorMessage}`);
    }
    async getRepository(owner, repo) {
        try {
            await this.checkRateLimit();
            const { data } = await this.octokit.rest.repos.get({
                owner,
                repo
            });
            return data;
        } catch (error) {
            this.handleGitHubError(error, 'Failed to fetch repository');
        }
    }
    async getRepositoryContents(owner, repo, path = '') {
        try {
            const { data } = await this.octokit.rest.repos.getContent({
                owner,
                repo,
                path
            });
            return Array.isArray(data) ? data : [
                data
            ];
        } catch (error) {
            this.handleGitHubError(error, `Failed to fetch repository contents for path: ${path}`);
        }
    }
    async getFileContent(owner, repo, path) {
        try {
            const { data } = await this.octokit.rest.repos.getContent({
                owner,
                repo,
                path
            });
            if ('content' in data && data.content) {
                return Buffer.from(data.content, 'base64').toString('utf-8');
            }
            throw new Error('File content not found');
        } catch (error) {
            this.handleGitHubError(error, `Failed to fetch file content for: ${path}`);
        }
    }
    async getRepositoryLanguages(owner, repo) {
        try {
            const { data } = await this.octokit.rest.repos.listLanguages({
                owner,
                repo
            });
            return data;
        } catch (error) {
            this.handleGitHubError(error, 'Failed to fetch repository languages');
        }
    }
    parseRepoUrl(url) {
        const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
        if (match) {
            return {
                owner: match[1],
                repo: match[2].replace(/\.git$/, '')
            };
        }
        return null;
    }
}
}),
"[project]/src/lib/aiCodeAnalyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AICodeAnalyzer": ()=>AICodeAnalyzer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
class AICodeAnalyzer {
    openai;
    model;
    maxTokens;
    temperature;
    static instance;
    constructor(config){
        const apiKey = config?.apiKey || process.env.OPENAI_API_KEY || '';
        const baseURL = config?.baseURL || process.env.OPENAI_BASE_URL;
        this.model = config?.model || process.env.OPENAI_MODEL || 'gpt-4o-mini';
        this.maxTokens = config?.maxTokens || parseInt(process.env.OPENAI_MAX_TOKENS || '4000');
        this.temperature = config?.temperature || parseFloat(process.env.OPENAI_TEMPERATURE || '0.1');
        this.openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            apiKey,
            baseURL
        });
    }
    static getInstance(config) {
        if (!AICodeAnalyzer.instance) {
            AICodeAnalyzer.instance = new AICodeAnalyzer(config);
        }
        return AICodeAnalyzer.instance;
    }
    async analyzeCode(content, language, filename) {
        try {
            const prompt = this.createAnalysisPrompt(content, language, filename);
            const response = await this.openai.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert code analyzer. Analyze the provided code and return a detailed JSON response with function definitions, calls, imports, and exports.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: this.temperature,
                max_tokens: this.maxTokens
            });
            const result = response.choices[0]?.message?.content;
            if (!result) {
                throw new Error('No response from AI model');
            }
            return this.parseAIResponse(result);
        } catch (error) {
            console.error('AI Code Analysis Error:', error);
            // Fallback to empty result
            return {
                functions: [],
                imports: [],
                exports: [],
                classes: []
            };
        }
    }
    createAnalysisPrompt(content, language, filename) {
        return `
Analyze this ${language} code file (${filename}) and extract detailed information about functions, classes, imports, and exports.

CODE:
\`\`\`${language}
${content}
\`\`\`

Please return a JSON response with the following structure:
{
  "functions": [
    {
      "name": "functionName",
      "startLine": 10,
      "endLine": 25,
      "parameters": ["param1: string", "param2: number"],
      "returnType": "string",
      "isExported": true,
      "isAsync": false,
      "calls": [
        {
          "functionName": "otherFunction",
          "line": 15,
          "isExternal": false,
          "targetFile": null
        }
      ]
    }
  ],
  "imports": ["./utils", "lodash", "react"],
  "exports": ["mainFunction", "helperFunction"],
  "classes": [
    {
      "name": "ClassName",
      "methods": ["method1", "method2"],
      "startLine": 30,
      "endLine": 50
    }
  ]
}

IMPORTANT RULES:
1. Include ALL function definitions (regular functions, arrow functions, methods, constructors)
2. For each function, list ALL function calls made within it
3. Mark isExternal=true for calls to functions not defined in this file
4. Include accurate line numbers (1-based indexing)
5. Extract parameter types and return types when available
6. Include both named and default exports
7. Return valid JSON only, no additional text
8. If you can't determine something, use null or empty array
9. For function calls, include the exact line number where the call occurs
10. Consider method calls on objects as function calls too (e.g., obj.method())

Focus on accuracy and completeness. This analysis will be used to build a code dependency graph.
`;
    }
    parseAIResponse(response) {
        try {
            // Clean the response - remove markdown code blocks if present
            const cleanedResponse = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            const parsed = JSON.parse(cleanedResponse);
            // Validate and normalize the response
            return {
                functions: Array.isArray(parsed.functions) ? parsed.functions : [],
                imports: Array.isArray(parsed.imports) ? parsed.imports : [],
                exports: Array.isArray(parsed.exports) ? parsed.exports : [],
                classes: Array.isArray(parsed.classes) ? parsed.classes : []
            };
        } catch (error) {
            console.error('Failed to parse AI response:', error);
            console.error('Raw response:', response);
            // Return empty result on parse error
            return {
                functions: [],
                imports: [],
                exports: [],
                classes: []
            };
        }
    }
    // Cache mechanism for repeated analysis of same content
    static cache = new Map();
    async analyzeCodeWithCache(content, language, filename) {
        const cacheKey = this.generateCacheKey(content, language);
        if (AICodeAnalyzer.cache.has(cacheKey)) {
            return AICodeAnalyzer.cache.get(cacheKey);
        }
        const result = await this.analyzeCode(content, language, filename);
        AICodeAnalyzer.cache.set(cacheKey, result);
        return result;
    }
    generateCacheKey(content, language) {
        // Simple hash function for caching
        let hash = 0;
        const str = content + language;
        for(let i = 0; i < str.length; i++){
            const char = str.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }
    // Clear cache when needed
    static clearCache() {
        AICodeAnalyzer.cache.clear();
    }
}
}),
"[project]/src/lib/codeAnalyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CodeAnalyzer": ()=>CodeAnalyzer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$aiCodeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/aiCodeAnalyzer.ts [app-route] (ecmascript)");
;
class CodeAnalyzer {
    static SUPPORTED_EXTENSIONS = {
        '.js': 'javascript',
        '.jsx': 'javascript',
        '.ts': 'typescript',
        '.tsx': 'typescript',
        '.py': 'python',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby',
        '.go': 'go',
        '.rs': 'rust',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.sass': 'sass',
        '.less': 'less',
        '.json': 'json',
        '.xml': 'xml',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.md': 'markdown',
        '.sql': 'sql'
    };
    static getLanguageFromExtension(filename) {
        const ext = filename.substring(filename.lastIndexOf('.'));
        return this.SUPPORTED_EXTENSIONS[ext];
    }
    static extractDependencies(content, language) {
        const dependencies = [];
        switch(language){
            case 'javascript':
            case 'typescript':
                dependencies.push(...this.extractJavaScriptDependencies(content));
                break;
            case 'python':
                dependencies.push(...this.extractPythonDependencies(content));
                break;
            case 'java':
                dependencies.push(...this.extractJavaDependencies(content));
                break;
            case 'cpp':
            case 'c':
                dependencies.push(...this.extractCDependencies(content));
                break;
            case 'csharp':
                dependencies.push(...this.extractCSharpDependencies(content));
                break;
            case 'go':
                dependencies.push(...this.extractGoDependencies(content));
                break;
            case 'rust':
                dependencies.push(...this.extractRustDependencies(content));
                break;
            default:
                break;
        }
        return [
            ...new Set(dependencies)
        ]; // Remove duplicates
    }
    static async extractFunctions(content, language, filename = 'unknown') {
        // Check if AI analysis is enabled
        const enableAI = process.env.ENABLE_AI_ANALYSIS === 'true' && process.env.OPENAI_API_KEY;
        if (enableAI) {
            try {
                // Try to get config from localStorage first, then fall back to environment variables
                let aiConfig;
                if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
                ;
                else {
                    // Server-side: use environment variables
                    aiConfig = {
                        apiKey: process.env.OPENAI_API_KEY || '',
                        baseURL: process.env.OPENAI_BASE_URL,
                        model: process.env.OPENAI_MODEL,
                        maxTokens: process.env.OPENAI_MAX_TOKENS ? parseInt(process.env.OPENAI_MAX_TOKENS) : undefined,
                        temperature: process.env.OPENAI_TEMPERATURE ? parseFloat(process.env.OPENAI_TEMPERATURE) : undefined
                    };
                }
                const aiAnalyzer = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$aiCodeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AICodeAnalyzer"].getInstance(aiConfig);
                const result = await aiAnalyzer.analyzeCodeWithCache(content, language, filename);
                return result.functions;
            } catch (error) {
                console.warn('AI analysis failed, falling back to regex analysis:', error);
            }
        }
        // Fallback to regex-based analysis
        return this.extractFunctionsRegex(content, language);
    }
    static extractFunctionsRegex(content, language) {
        // Simple regex-based fallback for basic function detection
        const functions = [];
        const lines = content.split('\n');
        switch(language){
            case 'javascript':
            case 'typescript':
                functions.push(...this.extractJavaScriptFunctionsRegex(lines));
                break;
            case 'python':
                functions.push(...this.extractPythonFunctionsRegex(lines));
                break;
            default:
                // For other languages, provide basic function detection
                functions.push(...this.extractGenericFunctionsRegex(lines, language));
                break;
        }
        return functions;
    }
    static extractJavaScriptFunctionsRegex(lines) {
        const functions = [];
        for(let i = 0; i < lines.length; i++){
            const line = lines[i].trim();
            // Function declarations: function name() {}
            const funcMatch = line.match(/^(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(/);
            if (funcMatch) {
                functions.push({
                    name: funcMatch[1],
                    startLine: i + 1,
                    endLine: i + 1,
                    parameters: [],
                    isExported: line.includes('export'),
                    isAsync: line.includes('async'),
                    calls: []
                });
            }
            // Arrow functions: const name = () => {}
            const arrowMatch = line.match(/^(?:export\s+)?const\s+(\w+)\s*=\s*(?:async\s+)?\(/);
            if (arrowMatch) {
                functions.push({
                    name: arrowMatch[1],
                    startLine: i + 1,
                    endLine: i + 1,
                    parameters: [],
                    isExported: line.includes('export'),
                    isAsync: line.includes('async'),
                    calls: []
                });
            }
        }
        return functions;
    }
    static extractPythonFunctionsRegex(lines) {
        const functions = [];
        for(let i = 0; i < lines.length; i++){
            const line = lines[i].trim();
            // Python function definitions: def name():
            const funcMatch = line.match(/^(?:async\s+)?def\s+(\w+)\s*\(/);
            if (funcMatch) {
                functions.push({
                    name: funcMatch[1],
                    startLine: i + 1,
                    endLine: i + 1,
                    parameters: [],
                    isAsync: line.includes('async'),
                    calls: []
                });
            }
        }
        return functions;
    }
    static extractGenericFunctionsRegex(lines, language) {
        // Very basic function detection for other languages
        const functions = [];
        for(let i = 0; i < lines.length; i++){
            const line = lines[i].trim();
            // Generic pattern for function-like constructs
            const patterns = [
                /function\s+(\w+)/,
                /def\s+(\w+)/,
                /fn\s+(\w+)/,
                /func\s+(\w+)/,
                /(\w+)\s*\(/ // Generic name(
            ];
            for (const pattern of patterns){
                const match = line.match(pattern);
                if (match) {
                    functions.push({
                        name: match[1],
                        startLine: i + 1,
                        endLine: i + 1,
                        parameters: [],
                        calls: []
                    });
                    break;
                }
            }
        }
        return functions;
    }
    static extractJavaScriptDependencies(content) {
        const dependencies = [];
        // ES6 imports
        const importRegex = /import\s+(?:.*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
        let match;
        while((match = importRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        // CommonJS requires
        const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
        while((match = requireRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        // Dynamic imports
        const dynamicImportRegex = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
        while((match = dynamicImportRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        return dependencies;
    }
    static extractPythonDependencies(content) {
        const dependencies = [];
        // import statements
        const importRegex = /^import\s+([^\s#]+)/gm;
        let match;
        while((match = importRegex.exec(content)) !== null){
            dependencies.push(match[1].split('.')[0]);
        }
        // from ... import statements
        const fromImportRegex = /^from\s+([^\s#]+)\s+import/gm;
        while((match = fromImportRegex.exec(content)) !== null){
            dependencies.push(match[1].split('.')[0]);
        }
        return dependencies;
    }
    static extractJavaDependencies(content) {
        const dependencies = [];
        // import statements
        const importRegex = /^import\s+(?:static\s+)?([^;]+);/gm;
        let match;
        while((match = importRegex.exec(content)) !== null){
            const packageName = match[1].trim();
            dependencies.push(packageName);
        }
        return dependencies;
    }
    static extractCDependencies(content) {
        const dependencies = [];
        // #include statements
        const includeRegex = /#include\s*[<"]([^>"]+)[>"]/g;
        let match;
        while((match = includeRegex.exec(content)) !== null){
            dependencies.push(match[1]);
        }
        return dependencies;
    }
    static extractCSharpDependencies(content) {
        const dependencies = [];
        // using statements
        const usingRegex = /^using\s+([^;]+);/gm;
        let match;
        while((match = usingRegex.exec(content)) !== null){
            dependencies.push(match[1].trim());
        }
        return dependencies;
    }
    static extractGoDependencies(content) {
        const dependencies = [];
        // import statements
        const importRegex = /import\s+(?:\(\s*([^)]+)\s*\)|"([^"]+)")/g;
        let match;
        while((match = importRegex.exec(content)) !== null){
            if (match[1]) {
                // Multi-line import
                const imports = match[1].split('\n').map((line)=>{
                    const trimmed = line.trim();
                    const quoted = trimmed.match(/"([^"]+)"/);
                    return quoted ? quoted[1] : null;
                }).filter(Boolean);
                dependencies.push(...imports);
            } else if (match[2]) {
                // Single import
                dependencies.push(match[2]);
            }
        }
        return dependencies;
    }
    static extractRustDependencies(content) {
        const dependencies = [];
        // use statements
        const useRegex = /^use\s+([^;]+);/gm;
        let match;
        while((match = useRegex.exec(content)) !== null){
            const usePath = match[1].trim();
            const rootModule = usePath.split('::')[0];
            dependencies.push(rootModule);
        }
        // extern crate statements
        const externRegex = /^extern\s+crate\s+([^;]+);/gm;
        while((match = externRegex.exec(content)) !== null){
            dependencies.push(match[1].trim());
        }
        return dependencies;
    }
    static createDependencyEdges(nodes) {
        const edges = [];
        const nodeMap = new Map(nodes.map((node)=>[
                node.path,
                node
            ]));
        const functionMap = new Map();
        // Build function map for quick lookup
        nodes.forEach((node)=>{
            if (node.functions) {
                node.functions.forEach((func)=>{
                    functionMap.set(`${node.path}:${func.name}`, {
                        node,
                        function: func
                    });
                });
            }
        });
        nodes.forEach((node)=>{
            // Add import/require edges
            if (node.dependencies) {
                node.dependencies.forEach((dep)=>{
                    const targetNode = this.findDependencyTarget(dep, nodeMap, node.path);
                    if (targetNode) {
                        edges.push({
                            id: `import-${node.id}-${targetNode.id}`,
                            source: node.id,
                            target: targetNode.id,
                            type: 'import',
                            label: dep
                        });
                    }
                });
            }
            // Add function call edges
            if (node.functions) {
                node.functions.forEach((func)=>{
                    if (func.calls) {
                        func.calls.forEach((call)=>{
                            if (!call.isExternal) {
                                // Try to find the target function in the same file first
                                const sameFileTarget = functionMap.get(`${node.path}:${call.functionName}`);
                                if (sameFileTarget) {
                                    edges.push({
                                        id: `call-${node.id}-${func.name}-${call.functionName}-${call.line}`,
                                        source: node.id,
                                        target: node.id,
                                        type: 'function_call',
                                        label: `${func.name} → ${call.functionName}`,
                                        sourceFunction: func.name,
                                        targetFunction: call.functionName,
                                        line: call.line
                                    });
                                } else if (call.targetFile) {
                                    // Try to find in other files
                                    const targetNode = nodeMap.get(call.targetFile);
                                    if (targetNode) {
                                        edges.push({
                                            id: `call-${node.id}-${targetNode.id}-${func.name}-${call.functionName}-${call.line}`,
                                            source: node.id,
                                            target: targetNode.id,
                                            type: 'function_call',
                                            label: `${func.name} → ${call.functionName}`,
                                            sourceFunction: func.name,
                                            targetFunction: call.functionName,
                                            line: call.line
                                        });
                                    }
                                }
                            }
                        });
                    }
                });
            }
        });
        return edges;
    }
    static findDependencyTarget(dependency, nodeMap, sourcePath) {
        // Handle relative imports
        if (dependency.startsWith('./') || dependency.startsWith('../')) {
            const resolvedPath = this.resolvePath(sourcePath, dependency);
            return nodeMap.get(resolvedPath) || null;
        }
        // Handle absolute imports within the project
        for (const [path, node] of nodeMap){
            if (path.includes(dependency) || node.name.includes(dependency)) {
                return node;
            }
        }
        return null;
    }
    static resolvePath(basePath, relativePath) {
        const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
        const parts = baseDir.split('/').concat(relativePath.split('/'));
        const resolved = [];
        parts.forEach((part)=>{
            if (part === '..') {
                resolved.pop();
            } else if (part !== '.' && part !== '') {
                resolved.push(part);
            }
        });
        return resolved.join('/');
    }
}
}),
"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/github.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/codeAnalyzer.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { url, token } = await request.json();
        if (!url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Repository URL is required'
            }, {
                status: 400
            });
        }
        const githubService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$github$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GitHubService"](token);
        const repoInfo = githubService.parseRepoUrl(url);
        if (!repoInfo) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid GitHub URL format'
            }, {
                status: 400
            });
        }
        const { owner, repo } = repoInfo;
        try {
            // Repository bilgilerini al
            const repository = await githubService.getRepository(owner, repo);
            const languages = await githubService.getRepositoryLanguages(owner, repo);
            // Repository içeriğini analiz et
            const nodes = await analyzeRepositoryContents(githubService, owner, repo);
            const edges = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].createDependencyEdges(nodes);
            const graph = {
                nodes,
                edges
            };
            const result = {
                repo: repository,
                graph,
                languages,
                stats: {
                    totalFiles: nodes.filter((n)=>n.type === 'file').length,
                    totalDirectories: nodes.filter((n)=>n.type === 'directory').length,
                    totalDependencies: edges.filter((e)=>e.type === 'import' || e.type === 'require' || e.type === 'include').length,
                    totalFunctions: nodes.reduce((sum, n)=>sum + (n.functions?.length || 0), 0),
                    totalFunctionCalls: edges.filter((e)=>e.type === 'function_call').length
                }
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
        } catch (error) {
            console.error('GitHub API Error:', error);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Failed to fetch repository data. Please check if the repository exists and is public.'
            }, {
                status: 404
            });
        }
    } catch (error) {
        console.error('Analysis Error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function analyzeRepositoryContents(githubService, owner, repo, path = '', depth = 0) {
    const nodes = [];
    const maxDepth = 3; // Maksimum derinlik sınırı
    const maxFiles = 100; // Maksimum dosya sayısı sınırı
    if (depth >= maxDepth) {
        return nodes;
    }
    try {
        const contents = await githubService.getRepositoryContents(owner, repo, path);
        // İçerikleri sırala: önce klasörler, sonra dosyalar
        const sortedContents = contents.sort((a, b)=>{
            if (a.type === 'dir' && b.type === 'file') return -1;
            if (a.type === 'file' && b.type === 'dir') return 1;
            return a.name.localeCompare(b.name);
        });
        let fileCount = 0;
        for (const item of sortedContents){
            if (fileCount >= maxFiles) break;
            const nodeId = `${path}/${item.name}`.replace(/^\//, '') || item.name;
            const node = {
                id: nodeId,
                name: item.name,
                path: item.path,
                type: item.type === 'dir' ? 'directory' : 'file',
                size: item.size
            };
            if (item.type === 'file') {
                fileCount++;
                const language = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].getLanguageFromExtension(item.name);
                if (language) {
                    node.language = language;
                    // Sadece küçük dosyaları analiz et (50KB'den küçük)
                    if (item.size && item.size < 50000) {
                        try {
                            const content = await githubService.getFileContent(owner, repo, item.path);
                            node.content = content;
                            node.dependencies = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].extractDependencies(content, language);
                            // Extract functions using AI or fallback to regex
                            try {
                                node.functions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$codeAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CodeAnalyzer"].extractFunctions(content, language, item.name);
                            } catch (functionError) {
                                console.warn(`Could not extract functions for ${item.path}:`, functionError);
                                node.functions = [];
                            }
                        } catch (contentError) {
                            console.warn(`Could not fetch content for ${item.path}:`, contentError);
                        }
                    }
                }
            } else if (item.type === 'dir') {
                // Belirli klasörleri atla
                const skipDirs = [
                    'node_modules',
                    '.git',
                    'dist',
                    'build',
                    '.next',
                    'coverage',
                    '__pycache__'
                ];
                if (!skipDirs.includes(item.name)) {
                    const children = await analyzeRepositoryContents(githubService, owner, repo, item.path, depth + 1);
                    node.children = children;
                    nodes.push(...children);
                }
            }
            nodes.push(node);
        }
    } catch (error) {
        console.warn(`Could not analyze path ${path}:`, error);
    }
    return nodes;
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__27a06cc4._.js.map