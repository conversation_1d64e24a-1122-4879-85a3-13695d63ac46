'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AnalysisResult } from '@/types';
import { Github, AlertCircle } from 'lucide-react';

interface GitHubRepoInputProps {
  onAnalysisStart: () => void;
  onAnalysisComplete: (result: AnalysisResult) => void;
}

export function GitHubRepoInput({ onAnalysisStart, onAnalysisComplete }: GitHubRepoInputProps) {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzeRepository = async () => {
    if (!url.trim()) {
      setError('Lütfen bir GitHub repository URL&apos;si girin');
      return;
    }

    setError('');
    setIsAnalyzing(true);
    onAnalysisStart();

    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analiz sırasında bir hata oluştu');
      }

      const result: AnalysisResult = await response.json();
      onAnalysisComplete(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analiz sırasında bir hata oluştu');
    } finally {
      setIsAnalyzing(false);
    }
  };



  return (
    <div className="space-y-4">
      <div className="flex space-x-2">
        <div className="flex-1">
          <Input
            type="url"
            placeholder="https://github.com/username/repository"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="w-full"
            disabled={isAnalyzing}
          />
        </div>
        <Button
          onClick={analyzeRepository}
          disabled={isAnalyzing || !url.trim()}
          className="flex items-center space-x-2"
        >
          <Github className="w-4 h-4" />
          <span>{isAnalyzing ? 'Analiz Ediliyor...' : 'Analiz Et'}</span>
        </Button>
      </div>

      {error && (
        <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
          <AlertCircle className="w-4 h-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      <div className="text-xs text-gray-500 dark:text-gray-400">
        <p>
          Örnek: https://github.com/facebook/react veya https://github.com/microsoft/vscode
        </p>
        <p className="mt-1">
          Not: Büyük repository&apos;ler için analiz biraz zaman alabilir.
        </p>
      </div>
    </div>
  );
}
