{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/node_modules/%40octokit/src/version.ts"], "sourcesContent": ["export const VERSION = \"16.0.0\";\n"], "names": [], "mappings": ";;;AAAO,MAAM,UAAU", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/node_modules/%40octokit/src/generated/endpoints.ts"], "sourcesContent": ["import type { EndpointsDefaultsAndDecorations } from \"../types.js\";\nconst Endpoints: EndpointsDefaultsAndDecorations = {\n  actions: {\n    addCustomLabelsToSelfHostedRunnerForOrg: [\n      \"POST /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    addCustomLabelsToSelfHostedRunnerForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    addRepoAccessToSelfHostedRunnerGroupInOrg: [\n      \"PUT /orgs/{org}/actions/runner-groups/{runner_group_id}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgVariable: [\n      \"PUT /orgs/{org}/actions/variables/{name}/repositories/{repository_id}\",\n    ],\n    approveWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve\",\n    ],\n    cancelWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel\",\n    ],\n    createEnvironmentVariable: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n    ],\n    createHostedRunnerForOrg: [\"POST /orgs/{org}/actions/hosted-runners\"],\n    createOrUpdateEnvironmentSecret: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    createOrUpdateOrgSecret: [\"PUT /orgs/{org}/actions/secrets/{secret_name}\"],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n    ],\n    createOrgVariable: [\"POST /orgs/{org}/actions/variables\"],\n    createRegistrationTokenForOrg: [\n      \"POST /orgs/{org}/actions/runners/registration-token\",\n    ],\n    createRegistrationTokenForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/registration-token\",\n    ],\n    createRemoveTokenForOrg: [\"POST /orgs/{org}/actions/runners/remove-token\"],\n    createRemoveTokenForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/remove-token\",\n    ],\n    createRepoVariable: [\"POST /repos/{owner}/{repo}/actions/variables\"],\n    createWorkflowDispatch: [\n      \"POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches\",\n    ],\n    deleteActionsCacheById: [\n      \"DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}\",\n    ],\n    deleteActionsCacheByKey: [\n      \"DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}\",\n    ],\n    deleteArtifact: [\n      \"DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\",\n    ],\n    deleteEnvironmentSecret: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    deleteEnvironmentVariable: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    deleteHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/actions/secrets/{secret_name}\"],\n    deleteOrgVariable: [\"DELETE /orgs/{org}/actions/variables/{name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}\",\n    ],\n    deleteRepoVariable: [\n      \"DELETE /repos/{owner}/{repo}/actions/variables/{name}\",\n    ],\n    deleteSelfHostedRunnerFromOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}\",\n    ],\n    deleteSelfHostedRunnerFromRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n    ],\n    deleteWorkflowRun: [\"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n    deleteWorkflowRunLogs: [\n      \"DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n    ],\n    disableSelectedRepositoryGithubActionsOrganization: [\n      \"DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n    ],\n    disableWorkflow: [\n      \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable\",\n    ],\n    downloadArtifact: [\n      \"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}\",\n    ],\n    downloadJobLogsForWorkflowRun: [\n      \"GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs\",\n    ],\n    downloadWorkflowRunAttemptLogs: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs\",\n    ],\n    downloadWorkflowRunLogs: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs\",\n    ],\n    enableSelectedRepositoryGithubActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/repositories/{repository_id}\",\n    ],\n    enableWorkflow: [\n      \"PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable\",\n    ],\n    forceCancelWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/force-cancel\",\n    ],\n    generateRunnerJitconfigForOrg: [\n      \"POST /orgs/{org}/actions/runners/generate-jitconfig\",\n    ],\n    generateRunnerJitconfigForRepo: [\n      \"POST /repos/{owner}/{repo}/actions/runners/generate-jitconfig\",\n    ],\n    getActionsCacheList: [\"GET /repos/{owner}/{repo}/actions/caches\"],\n    getActionsCacheUsage: [\"GET /repos/{owner}/{repo}/actions/cache/usage\"],\n    getActionsCacheUsageByRepoForOrg: [\n      \"GET /orgs/{org}/actions/cache/usage-by-repository\",\n    ],\n    getActionsCacheUsageForOrg: [\"GET /orgs/{org}/actions/cache/usage\"],\n    getAllowedActionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/selected-actions\",\n    ],\n    getAllowedActionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n    ],\n    getArtifact: [\"GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}\"],\n    getCustomOidcSubClaimForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/oidc/customization/sub\",\n    ],\n    getEnvironmentPublicKey: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/public-key\",\n    ],\n    getEnvironmentSecret: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets/{secret_name}\",\n    ],\n    getEnvironmentVariable: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    getGithubActionsDefaultWorkflowPermissionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/workflow\",\n    ],\n    getGithubActionsDefaultWorkflowPermissionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/workflow\",\n    ],\n    getGithubActionsPermissionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions\",\n    ],\n    getGithubActionsPermissionsRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions\",\n    ],\n    getHostedRunnerForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    getHostedRunnersGithubOwnedImagesForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/images/github-owned\",\n    ],\n    getHostedRunnersLimitsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/limits\",\n    ],\n    getHostedRunnersMachineSpecsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/machine-sizes\",\n    ],\n    getHostedRunnersPartnerImagesForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/images/partner\",\n    ],\n    getHostedRunnersPlatformsForOrg: [\n      \"GET /orgs/{org}/actions/hosted-runners/platforms\",\n    ],\n    getJobForWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/jobs/{job_id}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/actions/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/actions/secrets/{secret_name}\"],\n    getOrgVariable: [\"GET /orgs/{org}/actions/variables/{name}\"],\n    getPendingDeploymentsForRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n    ],\n    getRepoPermissions: [\n      \"GET /repos/{owner}/{repo}/actions/permissions\",\n      {},\n      { renamed: [\"actions\", \"getGithubActionsPermissionsRepository\"] },\n    ],\n    getRepoPublicKey: [\"GET /repos/{owner}/{repo}/actions/secrets/public-key\"],\n    getRepoSecret: [\"GET /repos/{owner}/{repo}/actions/secrets/{secret_name}\"],\n    getRepoVariable: [\"GET /repos/{owner}/{repo}/actions/variables/{name}\"],\n    getReviewsForRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals\",\n    ],\n    getSelfHostedRunnerForOrg: [\"GET /orgs/{org}/actions/runners/{runner_id}\"],\n    getSelfHostedRunnerForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}\",\n    ],\n    getWorkflow: [\"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}\"],\n    getWorkflowAccessToRepository: [\n      \"GET /repos/{owner}/{repo}/actions/permissions/access\",\n    ],\n    getWorkflowRun: [\"GET /repos/{owner}/{repo}/actions/runs/{run_id}\"],\n    getWorkflowRunAttempt: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}\",\n    ],\n    getWorkflowRunUsage: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing\",\n    ],\n    getWorkflowUsage: [\n      \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing\",\n    ],\n    listArtifactsForRepo: [\"GET /repos/{owner}/{repo}/actions/artifacts\"],\n    listEnvironmentSecrets: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/secrets\",\n    ],\n    listEnvironmentVariables: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/variables\",\n    ],\n    listGithubHostedRunnersInGroupForOrg: [\n      \"GET /orgs/{org}/actions/runner-groups/{runner_group_id}/hosted-runners\",\n    ],\n    listHostedRunnersForOrg: [\"GET /orgs/{org}/actions/hosted-runners\"],\n    listJobsForWorkflowRun: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs\",\n    ],\n    listJobsForWorkflowRunAttempt: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs\",\n    ],\n    listLabelsForSelfHostedRunnerForOrg: [\n      \"GET /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    listLabelsForSelfHostedRunnerForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    listOrgSecrets: [\"GET /orgs/{org}/actions/secrets\"],\n    listOrgVariables: [\"GET /orgs/{org}/actions/variables\"],\n    listRepoOrganizationSecrets: [\n      \"GET /repos/{owner}/{repo}/actions/organization-secrets\",\n    ],\n    listRepoOrganizationVariables: [\n      \"GET /repos/{owner}/{repo}/actions/organization-variables\",\n    ],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/actions/secrets\"],\n    listRepoVariables: [\"GET /repos/{owner}/{repo}/actions/variables\"],\n    listRepoWorkflows: [\"GET /repos/{owner}/{repo}/actions/workflows\"],\n    listRunnerApplicationsForOrg: [\"GET /orgs/{org}/actions/runners/downloads\"],\n    listRunnerApplicationsForRepo: [\n      \"GET /repos/{owner}/{repo}/actions/runners/downloads\",\n    ],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    ],\n    listSelectedReposForOrgVariable: [\n      \"GET /orgs/{org}/actions/variables/{name}/repositories\",\n    ],\n    listSelectedRepositoriesEnabledGithubActionsOrganization: [\n      \"GET /orgs/{org}/actions/permissions/repositories\",\n    ],\n    listSelfHostedRunnersForOrg: [\"GET /orgs/{org}/actions/runners\"],\n    listSelfHostedRunnersForRepo: [\"GET /repos/{owner}/{repo}/actions/runners\"],\n    listWorkflowRunArtifacts: [\n      \"GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts\",\n    ],\n    listWorkflowRuns: [\n      \"GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs\",\n    ],\n    listWorkflowRunsForRepo: [\"GET /repos/{owner}/{repo}/actions/runs\"],\n    reRunJobForWorkflowRun: [\n      \"POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun\",\n    ],\n    reRunWorkflow: [\"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun\"],\n    reRunWorkflowFailedJobs: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs\",\n    ],\n    removeAllCustomLabelsFromSelfHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    removeAllCustomLabelsFromSelfHostedRunnerForRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    removeCustomLabelFromSelfHostedRunnerForOrg: [\n      \"DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}\",\n    ],\n    removeCustomLabelFromSelfHostedRunnerForRepo: [\n      \"DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    removeSelectedRepoFromOrgVariable: [\n      \"DELETE /orgs/{org}/actions/variables/{name}/repositories/{repository_id}\",\n    ],\n    reviewCustomGatesForRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/deployment_protection_rule\",\n    ],\n    reviewPendingDeploymentsForRun: [\n      \"POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments\",\n    ],\n    setAllowedActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/selected-actions\",\n    ],\n    setAllowedActionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/selected-actions\",\n    ],\n    setCustomLabelsForSelfHostedRunnerForOrg: [\n      \"PUT /orgs/{org}/actions/runners/{runner_id}/labels\",\n    ],\n    setCustomLabelsForSelfHostedRunnerForRepo: [\n      \"PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels\",\n    ],\n    setCustomOidcSubClaimForRepo: [\n      \"PUT /repos/{owner}/{repo}/actions/oidc/customization/sub\",\n    ],\n    setGithubActionsDefaultWorkflowPermissionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/workflow\",\n    ],\n    setGithubActionsDefaultWorkflowPermissionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/workflow\",\n    ],\n    setGithubActionsPermissionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions\",\n    ],\n    setGithubActionsPermissionsRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/actions/secrets/{secret_name}/repositories\",\n    ],\n    setSelectedReposForOrgVariable: [\n      \"PUT /orgs/{org}/actions/variables/{name}/repositories\",\n    ],\n    setSelectedRepositoriesEnabledGithubActionsOrganization: [\n      \"PUT /orgs/{org}/actions/permissions/repositories\",\n    ],\n    setWorkflowAccessToRepository: [\n      \"PUT /repos/{owner}/{repo}/actions/permissions/access\",\n    ],\n    updateEnvironmentVariable: [\n      \"PATCH /repos/{owner}/{repo}/environments/{environment_name}/variables/{name}\",\n    ],\n    updateHostedRunnerForOrg: [\n      \"PATCH /orgs/{org}/actions/hosted-runners/{hosted_runner_id}\",\n    ],\n    updateOrgVariable: [\"PATCH /orgs/{org}/actions/variables/{name}\"],\n    updateRepoVariable: [\n      \"PATCH /repos/{owner}/{repo}/actions/variables/{name}\",\n    ],\n  },\n  activity: {\n    checkRepoIsStarredByAuthenticatedUser: [\"GET /user/starred/{owner}/{repo}\"],\n    deleteRepoSubscription: [\"DELETE /repos/{owner}/{repo}/subscription\"],\n    deleteThreadSubscription: [\n      \"DELETE /notifications/threads/{thread_id}/subscription\",\n    ],\n    getFeeds: [\"GET /feeds\"],\n    getRepoSubscription: [\"GET /repos/{owner}/{repo}/subscription\"],\n    getThread: [\"GET /notifications/threads/{thread_id}\"],\n    getThreadSubscriptionForAuthenticatedUser: [\n      \"GET /notifications/threads/{thread_id}/subscription\",\n    ],\n    listEventsForAuthenticatedUser: [\"GET /users/{username}/events\"],\n    listNotificationsForAuthenticatedUser: [\"GET /notifications\"],\n    listOrgEventsForAuthenticatedUser: [\n      \"GET /users/{username}/events/orgs/{org}\",\n    ],\n    listPublicEvents: [\"GET /events\"],\n    listPublicEventsForRepoNetwork: [\"GET /networks/{owner}/{repo}/events\"],\n    listPublicEventsForUser: [\"GET /users/{username}/events/public\"],\n    listPublicOrgEvents: [\"GET /orgs/{org}/events\"],\n    listReceivedEventsForUser: [\"GET /users/{username}/received_events\"],\n    listReceivedPublicEventsForUser: [\n      \"GET /users/{username}/received_events/public\",\n    ],\n    listRepoEvents: [\"GET /repos/{owner}/{repo}/events\"],\n    listRepoNotificationsForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/notifications\",\n    ],\n    listReposStarredByAuthenticatedUser: [\"GET /user/starred\"],\n    listReposStarredByUser: [\"GET /users/{username}/starred\"],\n    listReposWatchedByUser: [\"GET /users/{username}/subscriptions\"],\n    listStargazersForRepo: [\"GET /repos/{owner}/{repo}/stargazers\"],\n    listWatchedReposForAuthenticatedUser: [\"GET /user/subscriptions\"],\n    listWatchersForRepo: [\"GET /repos/{owner}/{repo}/subscribers\"],\n    markNotificationsAsRead: [\"PUT /notifications\"],\n    markRepoNotificationsAsRead: [\"PUT /repos/{owner}/{repo}/notifications\"],\n    markThreadAsDone: [\"DELETE /notifications/threads/{thread_id}\"],\n    markThreadAsRead: [\"PATCH /notifications/threads/{thread_id}\"],\n    setRepoSubscription: [\"PUT /repos/{owner}/{repo}/subscription\"],\n    setThreadSubscription: [\n      \"PUT /notifications/threads/{thread_id}/subscription\",\n    ],\n    starRepoForAuthenticatedUser: [\"PUT /user/starred/{owner}/{repo}\"],\n    unstarRepoForAuthenticatedUser: [\"DELETE /user/starred/{owner}/{repo}\"],\n  },\n  apps: {\n    addRepoToInstallation: [\n      \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n      {},\n      { renamed: [\"apps\", \"addRepoToInstallationForAuthenticatedUser\"] },\n    ],\n    addRepoToInstallationForAuthenticatedUser: [\n      \"PUT /user/installations/{installation_id}/repositories/{repository_id}\",\n    ],\n    checkToken: [\"POST /applications/{client_id}/token\"],\n    createFromManifest: [\"POST /app-manifests/{code}/conversions\"],\n    createInstallationAccessToken: [\n      \"POST /app/installations/{installation_id}/access_tokens\",\n    ],\n    deleteAuthorization: [\"DELETE /applications/{client_id}/grant\"],\n    deleteInstallation: [\"DELETE /app/installations/{installation_id}\"],\n    deleteToken: [\"DELETE /applications/{client_id}/token\"],\n    getAuthenticated: [\"GET /app\"],\n    getBySlug: [\"GET /apps/{app_slug}\"],\n    getInstallation: [\"GET /app/installations/{installation_id}\"],\n    getOrgInstallation: [\"GET /orgs/{org}/installation\"],\n    getRepoInstallation: [\"GET /repos/{owner}/{repo}/installation\"],\n    getSubscriptionPlanForAccount: [\n      \"GET /marketplace_listing/accounts/{account_id}\",\n    ],\n    getSubscriptionPlanForAccountStubbed: [\n      \"GET /marketplace_listing/stubbed/accounts/{account_id}\",\n    ],\n    getUserInstallation: [\"GET /users/{username}/installation\"],\n    getWebhookConfigForApp: [\"GET /app/hook/config\"],\n    getWebhookDelivery: [\"GET /app/hook/deliveries/{delivery_id}\"],\n    listAccountsForPlan: [\"GET /marketplace_listing/plans/{plan_id}/accounts\"],\n    listAccountsForPlanStubbed: [\n      \"GET /marketplace_listing/stubbed/plans/{plan_id}/accounts\",\n    ],\n    listInstallationReposForAuthenticatedUser: [\n      \"GET /user/installations/{installation_id}/repositories\",\n    ],\n    listInstallationRequestsForAuthenticatedApp: [\n      \"GET /app/installation-requests\",\n    ],\n    listInstallations: [\"GET /app/installations\"],\n    listInstallationsForAuthenticatedUser: [\"GET /user/installations\"],\n    listPlans: [\"GET /marketplace_listing/plans\"],\n    listPlansStubbed: [\"GET /marketplace_listing/stubbed/plans\"],\n    listReposAccessibleToInstallation: [\"GET /installation/repositories\"],\n    listSubscriptionsForAuthenticatedUser: [\"GET /user/marketplace_purchases\"],\n    listSubscriptionsForAuthenticatedUserStubbed: [\n      \"GET /user/marketplace_purchases/stubbed\",\n    ],\n    listWebhookDeliveries: [\"GET /app/hook/deliveries\"],\n    redeliverWebhookDelivery: [\n      \"POST /app/hook/deliveries/{delivery_id}/attempts\",\n    ],\n    removeRepoFromInstallation: [\n      \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n      {},\n      { renamed: [\"apps\", \"removeRepoFromInstallationForAuthenticatedUser\"] },\n    ],\n    removeRepoFromInstallationForAuthenticatedUser: [\n      \"DELETE /user/installations/{installation_id}/repositories/{repository_id}\",\n    ],\n    resetToken: [\"PATCH /applications/{client_id}/token\"],\n    revokeInstallationAccessToken: [\"DELETE /installation/token\"],\n    scopeToken: [\"POST /applications/{client_id}/token/scoped\"],\n    suspendInstallation: [\"PUT /app/installations/{installation_id}/suspended\"],\n    unsuspendInstallation: [\n      \"DELETE /app/installations/{installation_id}/suspended\",\n    ],\n    updateWebhookConfigForApp: [\"PATCH /app/hook/config\"],\n  },\n  billing: {\n    getGithubActionsBillingOrg: [\"GET /orgs/{org}/settings/billing/actions\"],\n    getGithubActionsBillingUser: [\n      \"GET /users/{username}/settings/billing/actions\",\n    ],\n    getGithubBillingUsageReportOrg: [\n      \"GET /organizations/{org}/settings/billing/usage\",\n    ],\n    getGithubBillingUsageReportUser: [\n      \"GET /users/{username}/settings/billing/usage\",\n    ],\n    getGithubPackagesBillingOrg: [\"GET /orgs/{org}/settings/billing/packages\"],\n    getGithubPackagesBillingUser: [\n      \"GET /users/{username}/settings/billing/packages\",\n    ],\n    getSharedStorageBillingOrg: [\n      \"GET /orgs/{org}/settings/billing/shared-storage\",\n    ],\n    getSharedStorageBillingUser: [\n      \"GET /users/{username}/settings/billing/shared-storage\",\n    ],\n  },\n  campaigns: {\n    createCampaign: [\"POST /orgs/{org}/campaigns\"],\n    deleteCampaign: [\"DELETE /orgs/{org}/campaigns/{campaign_number}\"],\n    getCampaignSummary: [\"GET /orgs/{org}/campaigns/{campaign_number}\"],\n    listOrgCampaigns: [\"GET /orgs/{org}/campaigns\"],\n    updateCampaign: [\"PATCH /orgs/{org}/campaigns/{campaign_number}\"],\n  },\n  checks: {\n    create: [\"POST /repos/{owner}/{repo}/check-runs\"],\n    createSuite: [\"POST /repos/{owner}/{repo}/check-suites\"],\n    get: [\"GET /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n    getSuite: [\"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}\"],\n    listAnnotations: [\n      \"GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations\",\n    ],\n    listForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-runs\"],\n    listForSuite: [\n      \"GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs\",\n    ],\n    listSuitesForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/check-suites\"],\n    rerequestRun: [\n      \"POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest\",\n    ],\n    rerequestSuite: [\n      \"POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest\",\n    ],\n    setSuitesPreferences: [\n      \"PATCH /repos/{owner}/{repo}/check-suites/preferences\",\n    ],\n    update: [\"PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}\"],\n  },\n  codeScanning: {\n    commitAutofix: [\n      \"POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix/commits\",\n    ],\n    createAutofix: [\n      \"POST /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix\",\n    ],\n    createVariantAnalysis: [\n      \"POST /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses\",\n    ],\n    deleteAnalysis: [\n      \"DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}\",\n    ],\n    deleteCodeqlDatabase: [\n      \"DELETE /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}\",\n    ],\n    getAlert: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n      {},\n      { renamedParameters: { alert_id: \"alert_number\" } },\n    ],\n    getAnalysis: [\n      \"GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}\",\n    ],\n    getAutofix: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/autofix\",\n    ],\n    getCodeqlDatabase: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}\",\n    ],\n    getDefaultSetup: [\"GET /repos/{owner}/{repo}/code-scanning/default-setup\"],\n    getSarif: [\"GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}\"],\n    getVariantAnalysis: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}\",\n    ],\n    getVariantAnalysisRepoTask: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/variant-analyses/{codeql_variant_analysis_id}/repos/{repo_owner}/{repo_name}\",\n    ],\n    listAlertInstances: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/code-scanning/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/code-scanning/alerts\"],\n    listAlertsInstances: [\n      \"GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances\",\n      {},\n      { renamed: [\"codeScanning\", \"listAlertInstances\"] },\n    ],\n    listCodeqlDatabases: [\n      \"GET /repos/{owner}/{repo}/code-scanning/codeql/databases\",\n    ],\n    listRecentAnalyses: [\"GET /repos/{owner}/{repo}/code-scanning/analyses\"],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}\",\n    ],\n    updateDefaultSetup: [\n      \"PATCH /repos/{owner}/{repo}/code-scanning/default-setup\",\n    ],\n    uploadSarif: [\"POST /repos/{owner}/{repo}/code-scanning/sarifs\"],\n  },\n  codeSecurity: {\n    attachConfiguration: [\n      \"POST /orgs/{org}/code-security/configurations/{configuration_id}/attach\",\n    ],\n    attachEnterpriseConfiguration: [\n      \"POST /enterprises/{enterprise}/code-security/configurations/{configuration_id}/attach\",\n    ],\n    createConfiguration: [\"POST /orgs/{org}/code-security/configurations\"],\n    createConfigurationForEnterprise: [\n      \"POST /enterprises/{enterprise}/code-security/configurations\",\n    ],\n    deleteConfiguration: [\n      \"DELETE /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    deleteConfigurationForEnterprise: [\n      \"DELETE /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n    detachConfiguration: [\n      \"DELETE /orgs/{org}/code-security/configurations/detach\",\n    ],\n    getConfiguration: [\n      \"GET /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    getConfigurationForRepository: [\n      \"GET /repos/{owner}/{repo}/code-security-configuration\",\n    ],\n    getConfigurationsForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations\",\n    ],\n    getConfigurationsForOrg: [\"GET /orgs/{org}/code-security/configurations\"],\n    getDefaultConfigurations: [\n      \"GET /orgs/{org}/code-security/configurations/defaults\",\n    ],\n    getDefaultConfigurationsForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/defaults\",\n    ],\n    getRepositoriesForConfiguration: [\n      \"GET /orgs/{org}/code-security/configurations/{configuration_id}/repositories\",\n    ],\n    getRepositoriesForEnterpriseConfiguration: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}/repositories\",\n    ],\n    getSingleConfigurationForEnterprise: [\n      \"GET /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n    setConfigurationAsDefault: [\n      \"PUT /orgs/{org}/code-security/configurations/{configuration_id}/defaults\",\n    ],\n    setConfigurationAsDefaultForEnterprise: [\n      \"PUT /enterprises/{enterprise}/code-security/configurations/{configuration_id}/defaults\",\n    ],\n    updateConfiguration: [\n      \"PATCH /orgs/{org}/code-security/configurations/{configuration_id}\",\n    ],\n    updateEnterpriseConfiguration: [\n      \"PATCH /enterprises/{enterprise}/code-security/configurations/{configuration_id}\",\n    ],\n  },\n  codesOfConduct: {\n    getAllCodesOfConduct: [\"GET /codes_of_conduct\"],\n    getConductCode: [\"GET /codes_of_conduct/{key}\"],\n  },\n  codespaces: {\n    addRepositoryForSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    checkPermissionsForDevcontainer: [\n      \"GET /repos/{owner}/{repo}/codespaces/permissions_check\",\n    ],\n    codespaceMachinesForAuthenticatedUser: [\n      \"GET /user/codespaces/{codespace_name}/machines\",\n    ],\n    createForAuthenticatedUser: [\"POST /user/codespaces\"],\n    createOrUpdateOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}\",\n    ],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    createOrUpdateSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}\",\n    ],\n    createWithPrForAuthenticatedUser: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces\",\n    ],\n    createWithRepoForAuthenticatedUser: [\n      \"POST /repos/{owner}/{repo}/codespaces\",\n    ],\n    deleteForAuthenticatedUser: [\"DELETE /user/codespaces/{codespace_name}\"],\n    deleteFromOrganization: [\n      \"DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/codespaces/secrets/{secret_name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    deleteSecretForAuthenticatedUser: [\n      \"DELETE /user/codespaces/secrets/{secret_name}\",\n    ],\n    exportForAuthenticatedUser: [\n      \"POST /user/codespaces/{codespace_name}/exports\",\n    ],\n    getCodespacesForUserInOrg: [\n      \"GET /orgs/{org}/members/{username}/codespaces\",\n    ],\n    getExportDetailsForAuthenticatedUser: [\n      \"GET /user/codespaces/{codespace_name}/exports/{export_id}\",\n    ],\n    getForAuthenticatedUser: [\"GET /user/codespaces/{codespace_name}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/codespaces/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/codespaces/secrets/{secret_name}\"],\n    getPublicKeyForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/public-key\",\n    ],\n    getRepoPublicKey: [\n      \"GET /repos/{owner}/{repo}/codespaces/secrets/public-key\",\n    ],\n    getRepoSecret: [\n      \"GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}\",\n    ],\n    getSecretForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/{secret_name}\",\n    ],\n    listDevcontainersInRepositoryForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/devcontainers\",\n    ],\n    listForAuthenticatedUser: [\"GET /user/codespaces\"],\n    listInOrganization: [\n      \"GET /orgs/{org}/codespaces\",\n      {},\n      { renamedParameters: { org_id: \"org\" } },\n    ],\n    listInRepositoryForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces\",\n    ],\n    listOrgSecrets: [\"GET /orgs/{org}/codespaces/secrets\"],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/codespaces/secrets\"],\n    listRepositoriesForSecretForAuthenticatedUser: [\n      \"GET /user/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    listSecretsForAuthenticatedUser: [\"GET /user/codespaces/secrets\"],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    preFlightWithRepoForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/new\",\n    ],\n    publishForAuthenticatedUser: [\n      \"POST /user/codespaces/{codespace_name}/publish\",\n    ],\n    removeRepositoryForSecretForAuthenticatedUser: [\n      \"DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    repoMachinesForAuthenticatedUser: [\n      \"GET /repos/{owner}/{repo}/codespaces/machines\",\n    ],\n    setRepositoriesForSecretForAuthenticatedUser: [\n      \"PUT /user/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/codespaces/secrets/{secret_name}/repositories\",\n    ],\n    startForAuthenticatedUser: [\"POST /user/codespaces/{codespace_name}/start\"],\n    stopForAuthenticatedUser: [\"POST /user/codespaces/{codespace_name}/stop\"],\n    stopInOrganization: [\n      \"POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop\",\n    ],\n    updateForAuthenticatedUser: [\"PATCH /user/codespaces/{codespace_name}\"],\n  },\n  copilot: {\n    addCopilotSeatsForTeams: [\n      \"POST /orgs/{org}/copilot/billing/selected_teams\",\n    ],\n    addCopilotSeatsForUsers: [\n      \"POST /orgs/{org}/copilot/billing/selected_users\",\n    ],\n    cancelCopilotSeatAssignmentForTeams: [\n      \"DELETE /orgs/{org}/copilot/billing/selected_teams\",\n    ],\n    cancelCopilotSeatAssignmentForUsers: [\n      \"DELETE /orgs/{org}/copilot/billing/selected_users\",\n    ],\n    copilotMetricsForOrganization: [\"GET /orgs/{org}/copilot/metrics\"],\n    copilotMetricsForTeam: [\"GET /orgs/{org}/team/{team_slug}/copilot/metrics\"],\n    getCopilotOrganizationDetails: [\"GET /orgs/{org}/copilot/billing\"],\n    getCopilotSeatDetailsForUser: [\n      \"GET /orgs/{org}/members/{username}/copilot\",\n    ],\n    listCopilotSeats: [\"GET /orgs/{org}/copilot/billing/seats\"],\n  },\n  credentials: { revoke: [\"POST /credentials/revoke\"] },\n  dependabot: {\n    addSelectedRepoToOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    createOrUpdateOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}\",\n    ],\n    createOrUpdateRepoSecret: [\n      \"PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    deleteOrgSecret: [\"DELETE /orgs/{org}/dependabot/secrets/{secret_name}\"],\n    deleteRepoSecret: [\n      \"DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    getAlert: [\"GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/dependabot/secrets/public-key\"],\n    getOrgSecret: [\"GET /orgs/{org}/dependabot/secrets/{secret_name}\"],\n    getRepoPublicKey: [\n      \"GET /repos/{owner}/{repo}/dependabot/secrets/public-key\",\n    ],\n    getRepoSecret: [\n      \"GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}\",\n    ],\n    listAlertsForEnterprise: [\n      \"GET /enterprises/{enterprise}/dependabot/alerts\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/dependabot/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/dependabot/alerts\"],\n    listOrgSecrets: [\"GET /orgs/{org}/dependabot/secrets\"],\n    listRepoSecrets: [\"GET /repos/{owner}/{repo}/dependabot/secrets\"],\n    listSelectedReposForOrgSecret: [\n      \"GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    ],\n    removeSelectedRepoFromOrgSecret: [\n      \"DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}\",\n    ],\n    setSelectedReposForOrgSecret: [\n      \"PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories\",\n    ],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}\",\n    ],\n  },\n  dependencyGraph: {\n    createRepositorySnapshot: [\n      \"POST /repos/{owner}/{repo}/dependency-graph/snapshots\",\n    ],\n    diffRange: [\n      \"GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}\",\n    ],\n    exportSbom: [\"GET /repos/{owner}/{repo}/dependency-graph/sbom\"],\n  },\n  emojis: { get: [\"GET /emojis\"] },\n  gists: {\n    checkIsStarred: [\"GET /gists/{gist_id}/star\"],\n    create: [\"POST /gists\"],\n    createComment: [\"POST /gists/{gist_id}/comments\"],\n    delete: [\"DELETE /gists/{gist_id}\"],\n    deleteComment: [\"DELETE /gists/{gist_id}/comments/{comment_id}\"],\n    fork: [\"POST /gists/{gist_id}/forks\"],\n    get: [\"GET /gists/{gist_id}\"],\n    getComment: [\"GET /gists/{gist_id}/comments/{comment_id}\"],\n    getRevision: [\"GET /gists/{gist_id}/{sha}\"],\n    list: [\"GET /gists\"],\n    listComments: [\"GET /gists/{gist_id}/comments\"],\n    listCommits: [\"GET /gists/{gist_id}/commits\"],\n    listForUser: [\"GET /users/{username}/gists\"],\n    listForks: [\"GET /gists/{gist_id}/forks\"],\n    listPublic: [\"GET /gists/public\"],\n    listStarred: [\"GET /gists/starred\"],\n    star: [\"PUT /gists/{gist_id}/star\"],\n    unstar: [\"DELETE /gists/{gist_id}/star\"],\n    update: [\"PATCH /gists/{gist_id}\"],\n    updateComment: [\"PATCH /gists/{gist_id}/comments/{comment_id}\"],\n  },\n  git: {\n    createBlob: [\"POST /repos/{owner}/{repo}/git/blobs\"],\n    createCommit: [\"POST /repos/{owner}/{repo}/git/commits\"],\n    createRef: [\"POST /repos/{owner}/{repo}/git/refs\"],\n    createTag: [\"POST /repos/{owner}/{repo}/git/tags\"],\n    createTree: [\"POST /repos/{owner}/{repo}/git/trees\"],\n    deleteRef: [\"DELETE /repos/{owner}/{repo}/git/refs/{ref}\"],\n    getBlob: [\"GET /repos/{owner}/{repo}/git/blobs/{file_sha}\"],\n    getCommit: [\"GET /repos/{owner}/{repo}/git/commits/{commit_sha}\"],\n    getRef: [\"GET /repos/{owner}/{repo}/git/ref/{ref}\"],\n    getTag: [\"GET /repos/{owner}/{repo}/git/tags/{tag_sha}\"],\n    getTree: [\"GET /repos/{owner}/{repo}/git/trees/{tree_sha}\"],\n    listMatchingRefs: [\"GET /repos/{owner}/{repo}/git/matching-refs/{ref}\"],\n    updateRef: [\"PATCH /repos/{owner}/{repo}/git/refs/{ref}\"],\n  },\n  gitignore: {\n    getAllTemplates: [\"GET /gitignore/templates\"],\n    getTemplate: [\"GET /gitignore/templates/{name}\"],\n  },\n  hostedCompute: {\n    createNetworkConfigurationForOrg: [\n      \"POST /orgs/{org}/settings/network-configurations\",\n    ],\n    deleteNetworkConfigurationFromOrg: [\n      \"DELETE /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n    getNetworkConfigurationForOrg: [\n      \"GET /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n    getNetworkSettingsForOrg: [\n      \"GET /orgs/{org}/settings/network-settings/{network_settings_id}\",\n    ],\n    listNetworkConfigurationsForOrg: [\n      \"GET /orgs/{org}/settings/network-configurations\",\n    ],\n    updateNetworkConfigurationForOrg: [\n      \"PATCH /orgs/{org}/settings/network-configurations/{network_configuration_id}\",\n    ],\n  },\n  interactions: {\n    getRestrictionsForAuthenticatedUser: [\"GET /user/interaction-limits\"],\n    getRestrictionsForOrg: [\"GET /orgs/{org}/interaction-limits\"],\n    getRestrictionsForRepo: [\"GET /repos/{owner}/{repo}/interaction-limits\"],\n    getRestrictionsForYourPublicRepos: [\n      \"GET /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"getRestrictionsForAuthenticatedUser\"] },\n    ],\n    removeRestrictionsForAuthenticatedUser: [\"DELETE /user/interaction-limits\"],\n    removeRestrictionsForOrg: [\"DELETE /orgs/{org}/interaction-limits\"],\n    removeRestrictionsForRepo: [\n      \"DELETE /repos/{owner}/{repo}/interaction-limits\",\n    ],\n    removeRestrictionsForYourPublicRepos: [\n      \"DELETE /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"removeRestrictionsForAuthenticatedUser\"] },\n    ],\n    setRestrictionsForAuthenticatedUser: [\"PUT /user/interaction-limits\"],\n    setRestrictionsForOrg: [\"PUT /orgs/{org}/interaction-limits\"],\n    setRestrictionsForRepo: [\"PUT /repos/{owner}/{repo}/interaction-limits\"],\n    setRestrictionsForYourPublicRepos: [\n      \"PUT /user/interaction-limits\",\n      {},\n      { renamed: [\"interactions\", \"setRestrictionsForAuthenticatedUser\"] },\n    ],\n  },\n  issues: {\n    addAssignees: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n    ],\n    addLabels: [\"POST /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n    addSubIssue: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n    ],\n    checkUserCanBeAssigned: [\"GET /repos/{owner}/{repo}/assignees/{assignee}\"],\n    checkUserCanBeAssignedToIssue: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/assignees/{assignee}\",\n    ],\n    create: [\"POST /repos/{owner}/{repo}/issues\"],\n    createComment: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/comments\",\n    ],\n    createLabel: [\"POST /repos/{owner}/{repo}/labels\"],\n    createMilestone: [\"POST /repos/{owner}/{repo}/milestones\"],\n    deleteComment: [\n      \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}\",\n    ],\n    deleteLabel: [\"DELETE /repos/{owner}/{repo}/labels/{name}\"],\n    deleteMilestone: [\n      \"DELETE /repos/{owner}/{repo}/milestones/{milestone_number}\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}/issues/{issue_number}\"],\n    getComment: [\"GET /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n    getEvent: [\"GET /repos/{owner}/{repo}/issues/events/{event_id}\"],\n    getLabel: [\"GET /repos/{owner}/{repo}/labels/{name}\"],\n    getMilestone: [\"GET /repos/{owner}/{repo}/milestones/{milestone_number}\"],\n    list: [\"GET /issues\"],\n    listAssignees: [\"GET /repos/{owner}/{repo}/assignees\"],\n    listComments: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/comments\"],\n    listCommentsForRepo: [\"GET /repos/{owner}/{repo}/issues/comments\"],\n    listEvents: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/events\"],\n    listEventsForRepo: [\"GET /repos/{owner}/{repo}/issues/events\"],\n    listEventsForTimeline: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/timeline\",\n    ],\n    listForAuthenticatedUser: [\"GET /user/issues\"],\n    listForOrg: [\"GET /orgs/{org}/issues\"],\n    listForRepo: [\"GET /repos/{owner}/{repo}/issues\"],\n    listLabelsForMilestone: [\n      \"GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels\",\n    ],\n    listLabelsForRepo: [\"GET /repos/{owner}/{repo}/labels\"],\n    listLabelsOnIssue: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    ],\n    listMilestones: [\"GET /repos/{owner}/{repo}/milestones\"],\n    listSubIssues: [\n      \"GET /repos/{owner}/{repo}/issues/{issue_number}/sub_issues\",\n    ],\n    lock: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n    removeAllLabels: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels\",\n    ],\n    removeAssignees: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees\",\n    ],\n    removeLabel: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}\",\n    ],\n    removeSubIssue: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/sub_issue\",\n    ],\n    reprioritizeSubIssue: [\n      \"PATCH /repos/{owner}/{repo}/issues/{issue_number}/sub_issues/priority\",\n    ],\n    setLabels: [\"PUT /repos/{owner}/{repo}/issues/{issue_number}/labels\"],\n    unlock: [\"DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock\"],\n    update: [\"PATCH /repos/{owner}/{repo}/issues/{issue_number}\"],\n    updateComment: [\"PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}\"],\n    updateLabel: [\"PATCH /repos/{owner}/{repo}/labels/{name}\"],\n    updateMilestone: [\n      \"PATCH /repos/{owner}/{repo}/milestones/{milestone_number}\",\n    ],\n  },\n  licenses: {\n    get: [\"GET /licenses/{license}\"],\n    getAllCommonlyUsed: [\"GET /licenses\"],\n    getForRepo: [\"GET /repos/{owner}/{repo}/license\"],\n  },\n  markdown: {\n    render: [\"POST /markdown\"],\n    renderRaw: [\n      \"POST /markdown/raw\",\n      { headers: { \"content-type\": \"text/plain; charset=utf-8\" } },\n    ],\n  },\n  meta: {\n    get: [\"GET /meta\"],\n    getAllVersions: [\"GET /versions\"],\n    getOctocat: [\"GET /octocat\"],\n    getZen: [\"GET /zen\"],\n    root: [\"GET /\"],\n  },\n  migrations: {\n    deleteArchiveForAuthenticatedUser: [\n      \"DELETE /user/migrations/{migration_id}/archive\",\n    ],\n    deleteArchiveForOrg: [\n      \"DELETE /orgs/{org}/migrations/{migration_id}/archive\",\n    ],\n    downloadArchiveForOrg: [\n      \"GET /orgs/{org}/migrations/{migration_id}/archive\",\n    ],\n    getArchiveForAuthenticatedUser: [\n      \"GET /user/migrations/{migration_id}/archive\",\n    ],\n    getStatusForAuthenticatedUser: [\"GET /user/migrations/{migration_id}\"],\n    getStatusForOrg: [\"GET /orgs/{org}/migrations/{migration_id}\"],\n    listForAuthenticatedUser: [\"GET /user/migrations\"],\n    listForOrg: [\"GET /orgs/{org}/migrations\"],\n    listReposForAuthenticatedUser: [\n      \"GET /user/migrations/{migration_id}/repositories\",\n    ],\n    listReposForOrg: [\"GET /orgs/{org}/migrations/{migration_id}/repositories\"],\n    listReposForUser: [\n      \"GET /user/migrations/{migration_id}/repositories\",\n      {},\n      { renamed: [\"migrations\", \"listReposForAuthenticatedUser\"] },\n    ],\n    startForAuthenticatedUser: [\"POST /user/migrations\"],\n    startForOrg: [\"POST /orgs/{org}/migrations\"],\n    unlockRepoForAuthenticatedUser: [\n      \"DELETE /user/migrations/{migration_id}/repos/{repo_name}/lock\",\n    ],\n    unlockRepoForOrg: [\n      \"DELETE /orgs/{org}/migrations/{migration_id}/repos/{repo_name}/lock\",\n    ],\n  },\n  oidc: {\n    getOidcCustomSubTemplateForOrg: [\n      \"GET /orgs/{org}/actions/oidc/customization/sub\",\n    ],\n    updateOidcCustomSubTemplateForOrg: [\n      \"PUT /orgs/{org}/actions/oidc/customization/sub\",\n    ],\n  },\n  orgs: {\n    addSecurityManagerTeam: [\n      \"PUT /orgs/{org}/security-managers/teams/{team_slug}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.addSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#add-a-security-manager-team\",\n      },\n    ],\n    assignTeamToOrgRole: [\n      \"PUT /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}\",\n    ],\n    assignUserToOrgRole: [\n      \"PUT /orgs/{org}/organization-roles/users/{username}/{role_id}\",\n    ],\n    blockUser: [\"PUT /orgs/{org}/blocks/{username}\"],\n    cancelInvitation: [\"DELETE /orgs/{org}/invitations/{invitation_id}\"],\n    checkBlockedUser: [\"GET /orgs/{org}/blocks/{username}\"],\n    checkMembershipForUser: [\"GET /orgs/{org}/members/{username}\"],\n    checkPublicMembershipForUser: [\"GET /orgs/{org}/public_members/{username}\"],\n    convertMemberToOutsideCollaborator: [\n      \"PUT /orgs/{org}/outside_collaborators/{username}\",\n    ],\n    createInvitation: [\"POST /orgs/{org}/invitations\"],\n    createIssueType: [\"POST /orgs/{org}/issue-types\"],\n    createOrUpdateCustomProperties: [\"PATCH /orgs/{org}/properties/schema\"],\n    createOrUpdateCustomPropertiesValuesForRepos: [\n      \"PATCH /orgs/{org}/properties/values\",\n    ],\n    createOrUpdateCustomProperty: [\n      \"PUT /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    createWebhook: [\"POST /orgs/{org}/hooks\"],\n    delete: [\"DELETE /orgs/{org}\"],\n    deleteIssueType: [\"DELETE /orgs/{org}/issue-types/{issue_type_id}\"],\n    deleteWebhook: [\"DELETE /orgs/{org}/hooks/{hook_id}\"],\n    enableOrDisableSecurityProductOnAllOrgRepos: [\n      \"POST /orgs/{org}/{security_product}/{enablement}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.enableOrDisableSecurityProductOnAllOrgRepos() is deprecated, see https://docs.github.com/rest/orgs/orgs#enable-or-disable-a-security-feature-for-an-organization\",\n      },\n    ],\n    get: [\"GET /orgs/{org}\"],\n    getAllCustomProperties: [\"GET /orgs/{org}/properties/schema\"],\n    getCustomProperty: [\n      \"GET /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    getMembershipForAuthenticatedUser: [\"GET /user/memberships/orgs/{org}\"],\n    getMembershipForUser: [\"GET /orgs/{org}/memberships/{username}\"],\n    getOrgRole: [\"GET /orgs/{org}/organization-roles/{role_id}\"],\n    getOrgRulesetHistory: [\"GET /orgs/{org}/rulesets/{ruleset_id}/history\"],\n    getOrgRulesetVersion: [\n      \"GET /orgs/{org}/rulesets/{ruleset_id}/history/{version_id}\",\n    ],\n    getWebhook: [\"GET /orgs/{org}/hooks/{hook_id}\"],\n    getWebhookConfigForOrg: [\"GET /orgs/{org}/hooks/{hook_id}/config\"],\n    getWebhookDelivery: [\n      \"GET /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}\",\n    ],\n    list: [\"GET /organizations\"],\n    listAppInstallations: [\"GET /orgs/{org}/installations\"],\n    listAttestations: [\"GET /orgs/{org}/attestations/{subject_digest}\"],\n    listBlockedUsers: [\"GET /orgs/{org}/blocks\"],\n    listCustomPropertiesValuesForRepos: [\"GET /orgs/{org}/properties/values\"],\n    listFailedInvitations: [\"GET /orgs/{org}/failed_invitations\"],\n    listForAuthenticatedUser: [\"GET /user/orgs\"],\n    listForUser: [\"GET /users/{username}/orgs\"],\n    listInvitationTeams: [\"GET /orgs/{org}/invitations/{invitation_id}/teams\"],\n    listIssueTypes: [\"GET /orgs/{org}/issue-types\"],\n    listMembers: [\"GET /orgs/{org}/members\"],\n    listMembershipsForAuthenticatedUser: [\"GET /user/memberships/orgs\"],\n    listOrgRoleTeams: [\"GET /orgs/{org}/organization-roles/{role_id}/teams\"],\n    listOrgRoleUsers: [\"GET /orgs/{org}/organization-roles/{role_id}/users\"],\n    listOrgRoles: [\"GET /orgs/{org}/organization-roles\"],\n    listOrganizationFineGrainedPermissions: [\n      \"GET /orgs/{org}/organization-fine-grained-permissions\",\n    ],\n    listOutsideCollaborators: [\"GET /orgs/{org}/outside_collaborators\"],\n    listPatGrantRepositories: [\n      \"GET /orgs/{org}/personal-access-tokens/{pat_id}/repositories\",\n    ],\n    listPatGrantRequestRepositories: [\n      \"GET /orgs/{org}/personal-access-token-requests/{pat_request_id}/repositories\",\n    ],\n    listPatGrantRequests: [\"GET /orgs/{org}/personal-access-token-requests\"],\n    listPatGrants: [\"GET /orgs/{org}/personal-access-tokens\"],\n    listPendingInvitations: [\"GET /orgs/{org}/invitations\"],\n    listPublicMembers: [\"GET /orgs/{org}/public_members\"],\n    listSecurityManagerTeams: [\n      \"GET /orgs/{org}/security-managers\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.listSecurityManagerTeams() is deprecated, see https://docs.github.com/rest/orgs/security-managers#list-security-manager-teams\",\n      },\n    ],\n    listWebhookDeliveries: [\"GET /orgs/{org}/hooks/{hook_id}/deliveries\"],\n    listWebhooks: [\"GET /orgs/{org}/hooks\"],\n    pingWebhook: [\"POST /orgs/{org}/hooks/{hook_id}/pings\"],\n    redeliverWebhookDelivery: [\n      \"POST /orgs/{org}/hooks/{hook_id}/deliveries/{delivery_id}/attempts\",\n    ],\n    removeCustomProperty: [\n      \"DELETE /orgs/{org}/properties/schema/{custom_property_name}\",\n    ],\n    removeMember: [\"DELETE /orgs/{org}/members/{username}\"],\n    removeMembershipForUser: [\"DELETE /orgs/{org}/memberships/{username}\"],\n    removeOutsideCollaborator: [\n      \"DELETE /orgs/{org}/outside_collaborators/{username}\",\n    ],\n    removePublicMembershipForAuthenticatedUser: [\n      \"DELETE /orgs/{org}/public_members/{username}\",\n    ],\n    removeSecurityManagerTeam: [\n      \"DELETE /orgs/{org}/security-managers/teams/{team_slug}\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.orgs.removeSecurityManagerTeam() is deprecated, see https://docs.github.com/rest/orgs/security-managers#remove-a-security-manager-team\",\n      },\n    ],\n    reviewPatGrantRequest: [\n      \"POST /orgs/{org}/personal-access-token-requests/{pat_request_id}\",\n    ],\n    reviewPatGrantRequestsInBulk: [\n      \"POST /orgs/{org}/personal-access-token-requests\",\n    ],\n    revokeAllOrgRolesTeam: [\n      \"DELETE /orgs/{org}/organization-roles/teams/{team_slug}\",\n    ],\n    revokeAllOrgRolesUser: [\n      \"DELETE /orgs/{org}/organization-roles/users/{username}\",\n    ],\n    revokeOrgRoleTeam: [\n      \"DELETE /orgs/{org}/organization-roles/teams/{team_slug}/{role_id}\",\n    ],\n    revokeOrgRoleUser: [\n      \"DELETE /orgs/{org}/organization-roles/users/{username}/{role_id}\",\n    ],\n    setMembershipForUser: [\"PUT /orgs/{org}/memberships/{username}\"],\n    setPublicMembershipForAuthenticatedUser: [\n      \"PUT /orgs/{org}/public_members/{username}\",\n    ],\n    unblockUser: [\"DELETE /orgs/{org}/blocks/{username}\"],\n    update: [\"PATCH /orgs/{org}\"],\n    updateIssueType: [\"PUT /orgs/{org}/issue-types/{issue_type_id}\"],\n    updateMembershipForAuthenticatedUser: [\n      \"PATCH /user/memberships/orgs/{org}\",\n    ],\n    updatePatAccess: [\"POST /orgs/{org}/personal-access-tokens/{pat_id}\"],\n    updatePatAccesses: [\"POST /orgs/{org}/personal-access-tokens\"],\n    updateWebhook: [\"PATCH /orgs/{org}/hooks/{hook_id}\"],\n    updateWebhookConfigForOrg: [\"PATCH /orgs/{org}/hooks/{hook_id}/config\"],\n  },\n  packages: {\n    deletePackageForAuthenticatedUser: [\n      \"DELETE /user/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageForOrg: [\n      \"DELETE /orgs/{org}/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageForUser: [\n      \"DELETE /users/{username}/packages/{package_type}/{package_name}\",\n    ],\n    deletePackageVersionForAuthenticatedUser: [\n      \"DELETE /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    deletePackageVersionForOrg: [\n      \"DELETE /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    deletePackageVersionForUser: [\n      \"DELETE /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getAllPackageVersionsForAPackageOwnedByAnOrg: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n      {},\n      { renamed: [\"packages\", \"getAllPackageVersionsForPackageOwnedByOrg\"] },\n    ],\n    getAllPackageVersionsForAPackageOwnedByTheAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions\",\n      {},\n      {\n        renamed: [\n          \"packages\",\n          \"getAllPackageVersionsForPackageOwnedByAuthenticatedUser\",\n        ],\n      },\n    ],\n    getAllPackageVersionsForPackageOwnedByAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions\",\n    ],\n    getAllPackageVersionsForPackageOwnedByOrg: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions\",\n    ],\n    getAllPackageVersionsForPackageOwnedByUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}/versions\",\n    ],\n    getPackageForAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}\",\n    ],\n    getPackageForOrganization: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}\",\n    ],\n    getPackageForUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}\",\n    ],\n    getPackageVersionForAuthenticatedUser: [\n      \"GET /user/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getPackageVersionForOrganization: [\n      \"GET /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    getPackageVersionForUser: [\n      \"GET /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}\",\n    ],\n    listDockerMigrationConflictingPackagesForAuthenticatedUser: [\n      \"GET /user/docker/conflicts\",\n    ],\n    listDockerMigrationConflictingPackagesForOrganization: [\n      \"GET /orgs/{org}/docker/conflicts\",\n    ],\n    listDockerMigrationConflictingPackagesForUser: [\n      \"GET /users/{username}/docker/conflicts\",\n    ],\n    listPackagesForAuthenticatedUser: [\"GET /user/packages\"],\n    listPackagesForOrganization: [\"GET /orgs/{org}/packages\"],\n    listPackagesForUser: [\"GET /users/{username}/packages\"],\n    restorePackageForAuthenticatedUser: [\n      \"POST /user/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageForOrg: [\n      \"POST /orgs/{org}/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageForUser: [\n      \"POST /users/{username}/packages/{package_type}/{package_name}/restore{?token}\",\n    ],\n    restorePackageVersionForAuthenticatedUser: [\n      \"POST /user/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n    restorePackageVersionForOrg: [\n      \"POST /orgs/{org}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n    restorePackageVersionForUser: [\n      \"POST /users/{username}/packages/{package_type}/{package_name}/versions/{package_version_id}/restore\",\n    ],\n  },\n  privateRegistries: {\n    createOrgPrivateRegistry: [\"POST /orgs/{org}/private-registries\"],\n    deleteOrgPrivateRegistry: [\n      \"DELETE /orgs/{org}/private-registries/{secret_name}\",\n    ],\n    getOrgPrivateRegistry: [\"GET /orgs/{org}/private-registries/{secret_name}\"],\n    getOrgPublicKey: [\"GET /orgs/{org}/private-registries/public-key\"],\n    listOrgPrivateRegistries: [\"GET /orgs/{org}/private-registries\"],\n    updateOrgPrivateRegistry: [\n      \"PATCH /orgs/{org}/private-registries/{secret_name}\",\n    ],\n  },\n  pulls: {\n    checkIfMerged: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n    create: [\"POST /repos/{owner}/{repo}/pulls\"],\n    createReplyForReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments/{comment_id}/replies\",\n    ],\n    createReview: [\"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n    createReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    ],\n    deletePendingReview: [\n      \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    deleteReviewComment: [\n      \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n    ],\n    dismissReview: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/dismissals\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}\"],\n    getReview: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    getReviewComment: [\"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}\"],\n    list: [\"GET /repos/{owner}/{repo}/pulls\"],\n    listCommentsForReview: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/comments\",\n    ],\n    listCommits: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/commits\"],\n    listFiles: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/files\"],\n    listRequestedReviewers: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    listReviewComments: [\n      \"GET /repos/{owner}/{repo}/pulls/{pull_number}/comments\",\n    ],\n    listReviewCommentsForRepo: [\"GET /repos/{owner}/{repo}/pulls/comments\"],\n    listReviews: [\"GET /repos/{owner}/{repo}/pulls/{pull_number}/reviews\"],\n    merge: [\"PUT /repos/{owner}/{repo}/pulls/{pull_number}/merge\"],\n    removeRequestedReviewers: [\n      \"DELETE /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    requestReviewers: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/requested_reviewers\",\n    ],\n    submitReview: [\n      \"POST /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}/events\",\n    ],\n    update: [\"PATCH /repos/{owner}/{repo}/pulls/{pull_number}\"],\n    updateBranch: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/update-branch\",\n    ],\n    updateReview: [\n      \"PUT /repos/{owner}/{repo}/pulls/{pull_number}/reviews/{review_id}\",\n    ],\n    updateReviewComment: [\n      \"PATCH /repos/{owner}/{repo}/pulls/comments/{comment_id}\",\n    ],\n  },\n  rateLimit: { get: [\"GET /rate_limit\"] },\n  reactions: {\n    createForCommitComment: [\n      \"POST /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    ],\n    createForIssue: [\n      \"POST /repos/{owner}/{repo}/issues/{issue_number}/reactions\",\n    ],\n    createForIssueComment: [\n      \"POST /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    ],\n    createForPullRequestReviewComment: [\n      \"POST /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    ],\n    createForRelease: [\n      \"POST /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    ],\n    createForTeamDiscussionCommentInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    ],\n    createForTeamDiscussionInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    ],\n    deleteForCommitComment: [\n      \"DELETE /repos/{owner}/{repo}/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForIssue: [\n      \"DELETE /repos/{owner}/{repo}/issues/{issue_number}/reactions/{reaction_id}\",\n    ],\n    deleteForIssueComment: [\n      \"DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForPullRequestComment: [\n      \"DELETE /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions/{reaction_id}\",\n    ],\n    deleteForRelease: [\n      \"DELETE /repos/{owner}/{repo}/releases/{release_id}/reactions/{reaction_id}\",\n    ],\n    deleteForTeamDiscussion: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions/{reaction_id}\",\n    ],\n    deleteForTeamDiscussionComment: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions/{reaction_id}\",\n    ],\n    listForCommitComment: [\n      \"GET /repos/{owner}/{repo}/comments/{comment_id}/reactions\",\n    ],\n    listForIssue: [\"GET /repos/{owner}/{repo}/issues/{issue_number}/reactions\"],\n    listForIssueComment: [\n      \"GET /repos/{owner}/{repo}/issues/comments/{comment_id}/reactions\",\n    ],\n    listForPullRequestReviewComment: [\n      \"GET /repos/{owner}/{repo}/pulls/comments/{comment_id}/reactions\",\n    ],\n    listForRelease: [\n      \"GET /repos/{owner}/{repo}/releases/{release_id}/reactions\",\n    ],\n    listForTeamDiscussionCommentInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}/reactions\",\n    ],\n    listForTeamDiscussionInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/reactions\",\n    ],\n  },\n  repos: {\n    acceptInvitation: [\n      \"PATCH /user/repository_invitations/{invitation_id}\",\n      {},\n      { renamed: [\"repos\", \"acceptInvitationForAuthenticatedUser\"] },\n    ],\n    acceptInvitationForAuthenticatedUser: [\n      \"PATCH /user/repository_invitations/{invitation_id}\",\n    ],\n    addAppAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    addCollaborator: [\"PUT /repos/{owner}/{repo}/collaborators/{username}\"],\n    addStatusCheckContexts: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    addTeamAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    addUserAccessRestrictions: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    cancelPagesDeployment: [\n      \"POST /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}/cancel\",\n    ],\n    checkAutomatedSecurityFixes: [\n      \"GET /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    checkCollaborator: [\"GET /repos/{owner}/{repo}/collaborators/{username}\"],\n    checkPrivateVulnerabilityReporting: [\n      \"GET /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    checkVulnerabilityAlerts: [\n      \"GET /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    codeownersErrors: [\"GET /repos/{owner}/{repo}/codeowners/errors\"],\n    compareCommits: [\"GET /repos/{owner}/{repo}/compare/{base}...{head}\"],\n    compareCommitsWithBasehead: [\n      \"GET /repos/{owner}/{repo}/compare/{basehead}\",\n    ],\n    createAttestation: [\"POST /repos/{owner}/{repo}/attestations\"],\n    createAutolink: [\"POST /repos/{owner}/{repo}/autolinks\"],\n    createCommitComment: [\n      \"POST /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    ],\n    createCommitSignatureProtection: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    createCommitStatus: [\"POST /repos/{owner}/{repo}/statuses/{sha}\"],\n    createDeployKey: [\"POST /repos/{owner}/{repo}/keys\"],\n    createDeployment: [\"POST /repos/{owner}/{repo}/deployments\"],\n    createDeploymentBranchPolicy: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n    ],\n    createDeploymentProtectionRule: [\n      \"POST /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules\",\n    ],\n    createDeploymentStatus: [\n      \"POST /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    ],\n    createDispatchEvent: [\"POST /repos/{owner}/{repo}/dispatches\"],\n    createForAuthenticatedUser: [\"POST /user/repos\"],\n    createFork: [\"POST /repos/{owner}/{repo}/forks\"],\n    createInOrg: [\"POST /orgs/{org}/repos\"],\n    createOrUpdateCustomPropertiesValues: [\n      \"PATCH /repos/{owner}/{repo}/properties/values\",\n    ],\n    createOrUpdateEnvironment: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    createOrUpdateFileContents: [\"PUT /repos/{owner}/{repo}/contents/{path}\"],\n    createOrgRuleset: [\"POST /orgs/{org}/rulesets\"],\n    createPagesDeployment: [\"POST /repos/{owner}/{repo}/pages/deployments\"],\n    createPagesSite: [\"POST /repos/{owner}/{repo}/pages\"],\n    createRelease: [\"POST /repos/{owner}/{repo}/releases\"],\n    createRepoRuleset: [\"POST /repos/{owner}/{repo}/rulesets\"],\n    createUsingTemplate: [\n      \"POST /repos/{template_owner}/{template_repo}/generate\",\n    ],\n    createWebhook: [\"POST /repos/{owner}/{repo}/hooks\"],\n    declineInvitation: [\n      \"DELETE /user/repository_invitations/{invitation_id}\",\n      {},\n      { renamed: [\"repos\", \"declineInvitationForAuthenticatedUser\"] },\n    ],\n    declineInvitationForAuthenticatedUser: [\n      \"DELETE /user/repository_invitations/{invitation_id}\",\n    ],\n    delete: [\"DELETE /repos/{owner}/{repo}\"],\n    deleteAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n    ],\n    deleteAdminBranchProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    deleteAnEnvironment: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    deleteAutolink: [\"DELETE /repos/{owner}/{repo}/autolinks/{autolink_id}\"],\n    deleteBranchProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    deleteCommitComment: [\"DELETE /repos/{owner}/{repo}/comments/{comment_id}\"],\n    deleteCommitSignatureProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    deleteDeployKey: [\"DELETE /repos/{owner}/{repo}/keys/{key_id}\"],\n    deleteDeployment: [\n      \"DELETE /repos/{owner}/{repo}/deployments/{deployment_id}\",\n    ],\n    deleteDeploymentBranchPolicy: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    deleteFile: [\"DELETE /repos/{owner}/{repo}/contents/{path}\"],\n    deleteInvitation: [\n      \"DELETE /repos/{owner}/{repo}/invitations/{invitation_id}\",\n    ],\n    deleteOrgRuleset: [\"DELETE /orgs/{org}/rulesets/{ruleset_id}\"],\n    deletePagesSite: [\"DELETE /repos/{owner}/{repo}/pages\"],\n    deletePullRequestReviewProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    deleteRelease: [\"DELETE /repos/{owner}/{repo}/releases/{release_id}\"],\n    deleteReleaseAsset: [\n      \"DELETE /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n    ],\n    deleteRepoRuleset: [\"DELETE /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    deleteWebhook: [\"DELETE /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    disableAutomatedSecurityFixes: [\n      \"DELETE /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    disableDeploymentProtectionRule: [\n      \"DELETE /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}\",\n    ],\n    disablePrivateVulnerabilityReporting: [\n      \"DELETE /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    disableVulnerabilityAlerts: [\n      \"DELETE /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    downloadArchive: [\n      \"GET /repos/{owner}/{repo}/zipball/{ref}\",\n      {},\n      { renamed: [\"repos\", \"downloadZipballArchive\"] },\n    ],\n    downloadTarballArchive: [\"GET /repos/{owner}/{repo}/tarball/{ref}\"],\n    downloadZipballArchive: [\"GET /repos/{owner}/{repo}/zipball/{ref}\"],\n    enableAutomatedSecurityFixes: [\n      \"PUT /repos/{owner}/{repo}/automated-security-fixes\",\n    ],\n    enablePrivateVulnerabilityReporting: [\n      \"PUT /repos/{owner}/{repo}/private-vulnerability-reporting\",\n    ],\n    enableVulnerabilityAlerts: [\n      \"PUT /repos/{owner}/{repo}/vulnerability-alerts\",\n    ],\n    generateReleaseNotes: [\n      \"POST /repos/{owner}/{repo}/releases/generate-notes\",\n    ],\n    get: [\"GET /repos/{owner}/{repo}\"],\n    getAccessRestrictions: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions\",\n    ],\n    getAdminBranchProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    getAllDeploymentProtectionRules: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules\",\n    ],\n    getAllEnvironments: [\"GET /repos/{owner}/{repo}/environments\"],\n    getAllStatusCheckContexts: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n    ],\n    getAllTopics: [\"GET /repos/{owner}/{repo}/topics\"],\n    getAppsWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n    ],\n    getAutolink: [\"GET /repos/{owner}/{repo}/autolinks/{autolink_id}\"],\n    getBranch: [\"GET /repos/{owner}/{repo}/branches/{branch}\"],\n    getBranchProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    getBranchRules: [\"GET /repos/{owner}/{repo}/rules/branches/{branch}\"],\n    getClones: [\"GET /repos/{owner}/{repo}/traffic/clones\"],\n    getCodeFrequencyStats: [\"GET /repos/{owner}/{repo}/stats/code_frequency\"],\n    getCollaboratorPermissionLevel: [\n      \"GET /repos/{owner}/{repo}/collaborators/{username}/permission\",\n    ],\n    getCombinedStatusForRef: [\"GET /repos/{owner}/{repo}/commits/{ref}/status\"],\n    getCommit: [\"GET /repos/{owner}/{repo}/commits/{ref}\"],\n    getCommitActivityStats: [\"GET /repos/{owner}/{repo}/stats/commit_activity\"],\n    getCommitComment: [\"GET /repos/{owner}/{repo}/comments/{comment_id}\"],\n    getCommitSignatureProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_signatures\",\n    ],\n    getCommunityProfileMetrics: [\"GET /repos/{owner}/{repo}/community/profile\"],\n    getContent: [\"GET /repos/{owner}/{repo}/contents/{path}\"],\n    getContributorsStats: [\"GET /repos/{owner}/{repo}/stats/contributors\"],\n    getCustomDeploymentProtectionRule: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/{protection_rule_id}\",\n    ],\n    getCustomPropertiesValues: [\"GET /repos/{owner}/{repo}/properties/values\"],\n    getDeployKey: [\"GET /repos/{owner}/{repo}/keys/{key_id}\"],\n    getDeployment: [\"GET /repos/{owner}/{repo}/deployments/{deployment_id}\"],\n    getDeploymentBranchPolicy: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    getDeploymentStatus: [\n      \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses/{status_id}\",\n    ],\n    getEnvironment: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}\",\n    ],\n    getLatestPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/latest\"],\n    getLatestRelease: [\"GET /repos/{owner}/{repo}/releases/latest\"],\n    getOrgRuleSuite: [\"GET /orgs/{org}/rulesets/rule-suites/{rule_suite_id}\"],\n    getOrgRuleSuites: [\"GET /orgs/{org}/rulesets/rule-suites\"],\n    getOrgRuleset: [\"GET /orgs/{org}/rulesets/{ruleset_id}\"],\n    getOrgRulesets: [\"GET /orgs/{org}/rulesets\"],\n    getPages: [\"GET /repos/{owner}/{repo}/pages\"],\n    getPagesBuild: [\"GET /repos/{owner}/{repo}/pages/builds/{build_id}\"],\n    getPagesDeployment: [\n      \"GET /repos/{owner}/{repo}/pages/deployments/{pages_deployment_id}\",\n    ],\n    getPagesHealthCheck: [\"GET /repos/{owner}/{repo}/pages/health\"],\n    getParticipationStats: [\"GET /repos/{owner}/{repo}/stats/participation\"],\n    getPullRequestReviewProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    getPunchCardStats: [\"GET /repos/{owner}/{repo}/stats/punch_card\"],\n    getReadme: [\"GET /repos/{owner}/{repo}/readme\"],\n    getReadmeInDirectory: [\"GET /repos/{owner}/{repo}/readme/{dir}\"],\n    getRelease: [\"GET /repos/{owner}/{repo}/releases/{release_id}\"],\n    getReleaseAsset: [\"GET /repos/{owner}/{repo}/releases/assets/{asset_id}\"],\n    getReleaseByTag: [\"GET /repos/{owner}/{repo}/releases/tags/{tag}\"],\n    getRepoRuleSuite: [\n      \"GET /repos/{owner}/{repo}/rulesets/rule-suites/{rule_suite_id}\",\n    ],\n    getRepoRuleSuites: [\"GET /repos/{owner}/{repo}/rulesets/rule-suites\"],\n    getRepoRuleset: [\"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    getRepoRulesetHistory: [\n      \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history\",\n    ],\n    getRepoRulesetVersion: [\n      \"GET /repos/{owner}/{repo}/rulesets/{ruleset_id}/history/{version_id}\",\n    ],\n    getRepoRulesets: [\"GET /repos/{owner}/{repo}/rulesets\"],\n    getStatusChecksProtection: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    getTeamsWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n    ],\n    getTopPaths: [\"GET /repos/{owner}/{repo}/traffic/popular/paths\"],\n    getTopReferrers: [\"GET /repos/{owner}/{repo}/traffic/popular/referrers\"],\n    getUsersWithAccessToProtectedBranch: [\n      \"GET /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n    ],\n    getViews: [\"GET /repos/{owner}/{repo}/traffic/views\"],\n    getWebhook: [\"GET /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    getWebhookConfigForRepo: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n    ],\n    getWebhookDelivery: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}\",\n    ],\n    listActivities: [\"GET /repos/{owner}/{repo}/activity\"],\n    listAttestations: [\n      \"GET /repos/{owner}/{repo}/attestations/{subject_digest}\",\n    ],\n    listAutolinks: [\"GET /repos/{owner}/{repo}/autolinks\"],\n    listBranches: [\"GET /repos/{owner}/{repo}/branches\"],\n    listBranchesForHeadCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/branches-where-head\",\n    ],\n    listCollaborators: [\"GET /repos/{owner}/{repo}/collaborators\"],\n    listCommentsForCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/comments\",\n    ],\n    listCommitCommentsForRepo: [\"GET /repos/{owner}/{repo}/comments\"],\n    listCommitStatusesForRef: [\n      \"GET /repos/{owner}/{repo}/commits/{ref}/statuses\",\n    ],\n    listCommits: [\"GET /repos/{owner}/{repo}/commits\"],\n    listContributors: [\"GET /repos/{owner}/{repo}/contributors\"],\n    listCustomDeploymentRuleIntegrations: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment_protection_rules/apps\",\n    ],\n    listDeployKeys: [\"GET /repos/{owner}/{repo}/keys\"],\n    listDeploymentBranchPolicies: [\n      \"GET /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies\",\n    ],\n    listDeploymentStatuses: [\n      \"GET /repos/{owner}/{repo}/deployments/{deployment_id}/statuses\",\n    ],\n    listDeployments: [\"GET /repos/{owner}/{repo}/deployments\"],\n    listForAuthenticatedUser: [\"GET /user/repos\"],\n    listForOrg: [\"GET /orgs/{org}/repos\"],\n    listForUser: [\"GET /users/{username}/repos\"],\n    listForks: [\"GET /repos/{owner}/{repo}/forks\"],\n    listInvitations: [\"GET /repos/{owner}/{repo}/invitations\"],\n    listInvitationsForAuthenticatedUser: [\"GET /user/repository_invitations\"],\n    listLanguages: [\"GET /repos/{owner}/{repo}/languages\"],\n    listPagesBuilds: [\"GET /repos/{owner}/{repo}/pages/builds\"],\n    listPublic: [\"GET /repositories\"],\n    listPullRequestsAssociatedWithCommit: [\n      \"GET /repos/{owner}/{repo}/commits/{commit_sha}/pulls\",\n    ],\n    listReleaseAssets: [\n      \"GET /repos/{owner}/{repo}/releases/{release_id}/assets\",\n    ],\n    listReleases: [\"GET /repos/{owner}/{repo}/releases\"],\n    listTags: [\"GET /repos/{owner}/{repo}/tags\"],\n    listTeams: [\"GET /repos/{owner}/{repo}/teams\"],\n    listWebhookDeliveries: [\n      \"GET /repos/{owner}/{repo}/hooks/{hook_id}/deliveries\",\n    ],\n    listWebhooks: [\"GET /repos/{owner}/{repo}/hooks\"],\n    merge: [\"POST /repos/{owner}/{repo}/merges\"],\n    mergeUpstream: [\"POST /repos/{owner}/{repo}/merge-upstream\"],\n    pingWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/pings\"],\n    redeliverWebhookDelivery: [\n      \"POST /repos/{owner}/{repo}/hooks/{hook_id}/deliveries/{delivery_id}/attempts\",\n    ],\n    removeAppAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    removeCollaborator: [\n      \"DELETE /repos/{owner}/{repo}/collaborators/{username}\",\n    ],\n    removeStatusCheckContexts: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    removeStatusCheckProtection: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    removeTeamAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    removeUserAccessRestrictions: [\n      \"DELETE /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    renameBranch: [\"POST /repos/{owner}/{repo}/branches/{branch}/rename\"],\n    replaceAllTopics: [\"PUT /repos/{owner}/{repo}/topics\"],\n    requestPagesBuild: [\"POST /repos/{owner}/{repo}/pages/builds\"],\n    setAdminBranchProtection: [\n      \"POST /repos/{owner}/{repo}/branches/{branch}/protection/enforce_admins\",\n    ],\n    setAppAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/apps\",\n      {},\n      { mapToData: \"apps\" },\n    ],\n    setStatusCheckContexts: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks/contexts\",\n      {},\n      { mapToData: \"contexts\" },\n    ],\n    setTeamAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/teams\",\n      {},\n      { mapToData: \"teams\" },\n    ],\n    setUserAccessRestrictions: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection/restrictions/users\",\n      {},\n      { mapToData: \"users\" },\n    ],\n    testPushWebhook: [\"POST /repos/{owner}/{repo}/hooks/{hook_id}/tests\"],\n    transfer: [\"POST /repos/{owner}/{repo}/transfer\"],\n    update: [\"PATCH /repos/{owner}/{repo}\"],\n    updateBranchProtection: [\n      \"PUT /repos/{owner}/{repo}/branches/{branch}/protection\",\n    ],\n    updateCommitComment: [\"PATCH /repos/{owner}/{repo}/comments/{comment_id}\"],\n    updateDeploymentBranchPolicy: [\n      \"PUT /repos/{owner}/{repo}/environments/{environment_name}/deployment-branch-policies/{branch_policy_id}\",\n    ],\n    updateInformationAboutPagesSite: [\"PUT /repos/{owner}/{repo}/pages\"],\n    updateInvitation: [\n      \"PATCH /repos/{owner}/{repo}/invitations/{invitation_id}\",\n    ],\n    updateOrgRuleset: [\"PUT /orgs/{org}/rulesets/{ruleset_id}\"],\n    updatePullRequestReviewProtection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_pull_request_reviews\",\n    ],\n    updateRelease: [\"PATCH /repos/{owner}/{repo}/releases/{release_id}\"],\n    updateReleaseAsset: [\n      \"PATCH /repos/{owner}/{repo}/releases/assets/{asset_id}\",\n    ],\n    updateRepoRuleset: [\"PUT /repos/{owner}/{repo}/rulesets/{ruleset_id}\"],\n    updateStatusCheckPotection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n      {},\n      { renamed: [\"repos\", \"updateStatusCheckProtection\"] },\n    ],\n    updateStatusCheckProtection: [\n      \"PATCH /repos/{owner}/{repo}/branches/{branch}/protection/required_status_checks\",\n    ],\n    updateWebhook: [\"PATCH /repos/{owner}/{repo}/hooks/{hook_id}\"],\n    updateWebhookConfigForRepo: [\n      \"PATCH /repos/{owner}/{repo}/hooks/{hook_id}/config\",\n    ],\n    uploadReleaseAsset: [\n      \"POST /repos/{owner}/{repo}/releases/{release_id}/assets{?name,label}\",\n      { baseUrl: \"https://uploads.github.com\" },\n    ],\n  },\n  search: {\n    code: [\"GET /search/code\"],\n    commits: [\"GET /search/commits\"],\n    issuesAndPullRequests: [\n      \"GET /search/issues\",\n      {},\n      {\n        deprecated:\n          \"octokit.rest.search.issuesAndPullRequests() is deprecated, see https://docs.github.com/rest/search/search#search-issues-and-pull-requests\",\n      },\n    ],\n    labels: [\"GET /search/labels\"],\n    repos: [\"GET /search/repositories\"],\n    topics: [\"GET /search/topics\"],\n    users: [\"GET /search/users\"],\n  },\n  secretScanning: {\n    createPushProtectionBypass: [\n      \"POST /repos/{owner}/{repo}/secret-scanning/push-protection-bypasses\",\n    ],\n    getAlert: [\n      \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n    ],\n    getScanHistory: [\"GET /repos/{owner}/{repo}/secret-scanning/scan-history\"],\n    listAlertsForEnterprise: [\n      \"GET /enterprises/{enterprise}/secret-scanning/alerts\",\n    ],\n    listAlertsForOrg: [\"GET /orgs/{org}/secret-scanning/alerts\"],\n    listAlertsForRepo: [\"GET /repos/{owner}/{repo}/secret-scanning/alerts\"],\n    listLocationsForAlert: [\n      \"GET /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}/locations\",\n    ],\n    updateAlert: [\n      \"PATCH /repos/{owner}/{repo}/secret-scanning/alerts/{alert_number}\",\n    ],\n  },\n  securityAdvisories: {\n    createFork: [\n      \"POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/forks\",\n    ],\n    createPrivateVulnerabilityReport: [\n      \"POST /repos/{owner}/{repo}/security-advisories/reports\",\n    ],\n    createRepositoryAdvisory: [\n      \"POST /repos/{owner}/{repo}/security-advisories\",\n    ],\n    createRepositoryAdvisoryCveRequest: [\n      \"POST /repos/{owner}/{repo}/security-advisories/{ghsa_id}/cve\",\n    ],\n    getGlobalAdvisory: [\"GET /advisories/{ghsa_id}\"],\n    getRepositoryAdvisory: [\n      \"GET /repos/{owner}/{repo}/security-advisories/{ghsa_id}\",\n    ],\n    listGlobalAdvisories: [\"GET /advisories\"],\n    listOrgRepositoryAdvisories: [\"GET /orgs/{org}/security-advisories\"],\n    listRepositoryAdvisories: [\"GET /repos/{owner}/{repo}/security-advisories\"],\n    updateRepositoryAdvisory: [\n      \"PATCH /repos/{owner}/{repo}/security-advisories/{ghsa_id}\",\n    ],\n  },\n  teams: {\n    addOrUpdateMembershipForUserInOrg: [\n      \"PUT /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    addOrUpdateRepoPermissionsInOrg: [\n      \"PUT /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    checkPermissionsForRepoInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    create: [\"POST /orgs/{org}/teams\"],\n    createDiscussionCommentInOrg: [\n      \"POST /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    ],\n    createDiscussionInOrg: [\"POST /orgs/{org}/teams/{team_slug}/discussions\"],\n    deleteDiscussionCommentInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    deleteDiscussionInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    deleteInOrg: [\"DELETE /orgs/{org}/teams/{team_slug}\"],\n    getByName: [\"GET /orgs/{org}/teams/{team_slug}\"],\n    getDiscussionCommentInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    getDiscussionInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    getMembershipForUserInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    list: [\"GET /orgs/{org}/teams\"],\n    listChildInOrg: [\"GET /orgs/{org}/teams/{team_slug}/teams\"],\n    listDiscussionCommentsInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments\",\n    ],\n    listDiscussionsInOrg: [\"GET /orgs/{org}/teams/{team_slug}/discussions\"],\n    listForAuthenticatedUser: [\"GET /user/teams\"],\n    listMembersInOrg: [\"GET /orgs/{org}/teams/{team_slug}/members\"],\n    listPendingInvitationsInOrg: [\n      \"GET /orgs/{org}/teams/{team_slug}/invitations\",\n    ],\n    listReposInOrg: [\"GET /orgs/{org}/teams/{team_slug}/repos\"],\n    removeMembershipForUserInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/memberships/{username}\",\n    ],\n    removeRepoInOrg: [\n      \"DELETE /orgs/{org}/teams/{team_slug}/repos/{owner}/{repo}\",\n    ],\n    updateDiscussionCommentInOrg: [\n      \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}/comments/{comment_number}\",\n    ],\n    updateDiscussionInOrg: [\n      \"PATCH /orgs/{org}/teams/{team_slug}/discussions/{discussion_number}\",\n    ],\n    updateInOrg: [\"PATCH /orgs/{org}/teams/{team_slug}\"],\n  },\n  users: {\n    addEmailForAuthenticated: [\n      \"POST /user/emails\",\n      {},\n      { renamed: [\"users\", \"addEmailForAuthenticatedUser\"] },\n    ],\n    addEmailForAuthenticatedUser: [\"POST /user/emails\"],\n    addSocialAccountForAuthenticatedUser: [\"POST /user/social_accounts\"],\n    block: [\"PUT /user/blocks/{username}\"],\n    checkBlocked: [\"GET /user/blocks/{username}\"],\n    checkFollowingForUser: [\"GET /users/{username}/following/{target_user}\"],\n    checkPersonIsFollowedByAuthenticated: [\"GET /user/following/{username}\"],\n    createGpgKeyForAuthenticated: [\n      \"POST /user/gpg_keys\",\n      {},\n      { renamed: [\"users\", \"createGpgKeyForAuthenticatedUser\"] },\n    ],\n    createGpgKeyForAuthenticatedUser: [\"POST /user/gpg_keys\"],\n    createPublicSshKeyForAuthenticated: [\n      \"POST /user/keys\",\n      {},\n      { renamed: [\"users\", \"createPublicSshKeyForAuthenticatedUser\"] },\n    ],\n    createPublicSshKeyForAuthenticatedUser: [\"POST /user/keys\"],\n    createSshSigningKeyForAuthenticatedUser: [\"POST /user/ssh_signing_keys\"],\n    deleteEmailForAuthenticated: [\n      \"DELETE /user/emails\",\n      {},\n      { renamed: [\"users\", \"deleteEmailForAuthenticatedUser\"] },\n    ],\n    deleteEmailForAuthenticatedUser: [\"DELETE /user/emails\"],\n    deleteGpgKeyForAuthenticated: [\n      \"DELETE /user/gpg_keys/{gpg_key_id}\",\n      {},\n      { renamed: [\"users\", \"deleteGpgKeyForAuthenticatedUser\"] },\n    ],\n    deleteGpgKeyForAuthenticatedUser: [\"DELETE /user/gpg_keys/{gpg_key_id}\"],\n    deletePublicSshKeyForAuthenticated: [\n      \"DELETE /user/keys/{key_id}\",\n      {},\n      { renamed: [\"users\", \"deletePublicSshKeyForAuthenticatedUser\"] },\n    ],\n    deletePublicSshKeyForAuthenticatedUser: [\"DELETE /user/keys/{key_id}\"],\n    deleteSocialAccountForAuthenticatedUser: [\"DELETE /user/social_accounts\"],\n    deleteSshSigningKeyForAuthenticatedUser: [\n      \"DELETE /user/ssh_signing_keys/{ssh_signing_key_id}\",\n    ],\n    follow: [\"PUT /user/following/{username}\"],\n    getAuthenticated: [\"GET /user\"],\n    getById: [\"GET /user/{account_id}\"],\n    getByUsername: [\"GET /users/{username}\"],\n    getContextForUser: [\"GET /users/{username}/hovercard\"],\n    getGpgKeyForAuthenticated: [\n      \"GET /user/gpg_keys/{gpg_key_id}\",\n      {},\n      { renamed: [\"users\", \"getGpgKeyForAuthenticatedUser\"] },\n    ],\n    getGpgKeyForAuthenticatedUser: [\"GET /user/gpg_keys/{gpg_key_id}\"],\n    getPublicSshKeyForAuthenticated: [\n      \"GET /user/keys/{key_id}\",\n      {},\n      { renamed: [\"users\", \"getPublicSshKeyForAuthenticatedUser\"] },\n    ],\n    getPublicSshKeyForAuthenticatedUser: [\"GET /user/keys/{key_id}\"],\n    getSshSigningKeyForAuthenticatedUser: [\n      \"GET /user/ssh_signing_keys/{ssh_signing_key_id}\",\n    ],\n    list: [\"GET /users\"],\n    listAttestations: [\"GET /users/{username}/attestations/{subject_digest}\"],\n    listBlockedByAuthenticated: [\n      \"GET /user/blocks\",\n      {},\n      { renamed: [\"users\", \"listBlockedByAuthenticatedUser\"] },\n    ],\n    listBlockedByAuthenticatedUser: [\"GET /user/blocks\"],\n    listEmailsForAuthenticated: [\n      \"GET /user/emails\",\n      {},\n      { renamed: [\"users\", \"listEmailsForAuthenticatedUser\"] },\n    ],\n    listEmailsForAuthenticatedUser: [\"GET /user/emails\"],\n    listFollowedByAuthenticated: [\n      \"GET /user/following\",\n      {},\n      { renamed: [\"users\", \"listFollowedByAuthenticatedUser\"] },\n    ],\n    listFollowedByAuthenticatedUser: [\"GET /user/following\"],\n    listFollowersForAuthenticatedUser: [\"GET /user/followers\"],\n    listFollowersForUser: [\"GET /users/{username}/followers\"],\n    listFollowingForUser: [\"GET /users/{username}/following\"],\n    listGpgKeysForAuthenticated: [\n      \"GET /user/gpg_keys\",\n      {},\n      { renamed: [\"users\", \"listGpgKeysForAuthenticatedUser\"] },\n    ],\n    listGpgKeysForAuthenticatedUser: [\"GET /user/gpg_keys\"],\n    listGpgKeysForUser: [\"GET /users/{username}/gpg_keys\"],\n    listPublicEmailsForAuthenticated: [\n      \"GET /user/public_emails\",\n      {},\n      { renamed: [\"users\", \"listPublicEmailsForAuthenticatedUser\"] },\n    ],\n    listPublicEmailsForAuthenticatedUser: [\"GET /user/public_emails\"],\n    listPublicKeysForUser: [\"GET /users/{username}/keys\"],\n    listPublicSshKeysForAuthenticated: [\n      \"GET /user/keys\",\n      {},\n      { renamed: [\"users\", \"listPublicSshKeysForAuthenticatedUser\"] },\n    ],\n    listPublicSshKeysForAuthenticatedUser: [\"GET /user/keys\"],\n    listSocialAccountsForAuthenticatedUser: [\"GET /user/social_accounts\"],\n    listSocialAccountsForUser: [\"GET /users/{username}/social_accounts\"],\n    listSshSigningKeysForAuthenticatedUser: [\"GET /user/ssh_signing_keys\"],\n    listSshSigningKeysForUser: [\"GET /users/{username}/ssh_signing_keys\"],\n    setPrimaryEmailVisibilityForAuthenticated: [\n      \"PATCH /user/email/visibility\",\n      {},\n      { renamed: [\"users\", \"setPrimaryEmailVisibilityForAuthenticatedUser\"] },\n    ],\n    setPrimaryEmailVisibilityForAuthenticatedUser: [\n      \"PATCH /user/email/visibility\",\n    ],\n    unblock: [\"DELETE /user/blocks/{username}\"],\n    unfollow: [\"DELETE /user/following/{username}\"],\n    updateAuthenticated: [\"PATCH /user\"],\n  },\n};\n\nexport default Endpoints;\n"], "names": [], "mappings": ";;;AACA,MAAM,YAA6C;IACjD,SAAS;QACP,yCAAyC;YACvC;SACF;QACA,0CAA0C;YACxC;SACF;QACA,2CAA2C;YACzC;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,oBAAoB;YAClB;SACF;QACA,mBAAmB;YACjB;SACF;QACA,2BAA2B;YACzB;SACF;QACA,0BAA0B;YAAC,yCAAyC;SAAA;QACpE,iCAAiC;YAC/B;SACF;QACA,yBAAyB;YAAC,+CAA+C;SAAA;QACzE,0BAA0B;YACxB;SACF;QACA,mBAAmB;YAAC,oCAAoC;SAAA;QACxD,+BAA+B;YAC7B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,yBAAyB;YAAC,+CAA+C;SAAA;QACzE,0BAA0B;YACxB;SACF;QACA,oBAAoB;YAAC,8CAA8C;SAAA;QACnE,wBAAwB;YACtB;SACF;QACA,wBAAwB;YACtB;SACF;QACA,yBAAyB;YACvB;SACF;QACA,gBAAgB;YACd;SACF;QACA,yBAAyB;YACvB;SACF;QACA,2BAA2B;YACzB;SACF;QACA,0BAA0B;YACxB;SACF;QACA,iBAAiB;YAAC,kDAAkD;SAAA;QACpE,mBAAmB;YAAC,6CAA6C;SAAA;QACjE,kBAAkB;YAChB;SACF;QACA,oBAAoB;YAClB;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,mBAAmB;YAAC,oDAAoD;SAAA;QACxE,uBAAuB;YACrB;SACF;QACA,oDAAoD;YAClD;SACF;QACA,iBAAiB;YACf;SACF;QACA,kBAAkB;YAChB;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,yBAAyB;YACvB;SACF;QACA,mDAAmD;YACjD;SACF;QACA,gBAAgB;YACd;SACF;QACA,wBAAwB;YACtB;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,qBAAqB;YAAC,0CAA0C;SAAA;QAChE,sBAAsB;YAAC,+CAA+C;SAAA;QACtE,kCAAkC;YAChC;SACF;QACA,4BAA4B;YAAC,qCAAqC;SAAA;QAClE,+BAA+B;YAC7B;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,aAAa;YAAC,2DAA2D;SAAA;QACzE,8BAA8B;YAC5B;SACF;QACA,yBAAyB;YACvB;SACF;QACA,sBAAsB;YACpB;SACF;QACA,wBAAwB;YACtB;SACF;QACA,wDAAwD;YACtD;SACF;QACA,sDAAsD;YACpD;SACF;QACA,yCAAyC;YACvC;SACF;QACA,uCAAuC;YACrC;SACF;QACA,uBAAuB;YACrB;SACF;QACA,yCAAyC;YACvC;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,oCAAoC;YAClC;SACF;QACA,qCAAqC;YACnC;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,sBAAsB;YAAC,iDAAiD;SAAA;QACxE,iBAAiB;YAAC,4CAA4C;SAAA;QAC9D,cAAc;YAAC,+CAA+C;SAAA;QAC9D,gBAAgB;YAAC,0CAA0C;SAAA;QAC3D,6BAA6B;YAC3B;SACF;QACA,oBAAoB;YAClB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAW,uCAAuC;iBAAA;YAAE;SAClE;QACA,kBAAkB;YAAC,sDAAsD;SAAA;QACzE,eAAe;YAAC,yDAAyD;SAAA;QACzE,iBAAiB;YAAC,oDAAoD;SAAA;QACtE,kBAAkB;YAChB;SACF;QACA,2BAA2B;YAAC,6CAA6C;SAAA;QACzE,4BAA4B;YAC1B;SACF;QACA,aAAa;YAAC,2DAA2D;SAAA;QACzE,+BAA+B;YAC7B;SACF;QACA,gBAAgB;YAAC,iDAAiD;SAAA;QAClE,uBAAuB;YACrB;SACF;QACA,qBAAqB;YACnB;SACF;QACA,kBAAkB;YAChB;SACF;QACA,sBAAsB;YAAC,6CAA6C;SAAA;QACpE,wBAAwB;YACtB;SACF;QACA,0BAA0B;YACxB;SACF;QACA,sCAAsC;YACpC;SACF;QACA,yBAAyB;YAAC,wCAAwC;SAAA;QAClE,wBAAwB;YACtB;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,qCAAqC;YACnC;SACF;QACA,sCAAsC;YACpC;SACF;QACA,gBAAgB;YAAC,iCAAiC;SAAA;QAClD,kBAAkB;YAAC,mCAAmC;SAAA;QACtD,6BAA6B;YAC3B;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,iBAAiB;YAAC,2CAA2C;SAAA;QAC7D,mBAAmB;YAAC,6CAA6C;SAAA;QACjE,mBAAmB;YAAC,6CAA6C;SAAA;QACjE,8BAA8B;YAAC,2CAA2C;SAAA;QAC1E,+BAA+B;YAC7B;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,0DAA0D;YACxD;SACF;QACA,6BAA6B;YAAC,iCAAiC;SAAA;QAC/D,8BAA8B;YAAC,2CAA2C;SAAA;QAC1E,0BAA0B;YACxB;SACF;QACA,kBAAkB;YAChB;SACF;QACA,yBAAyB;YAAC,wCAAwC;SAAA;QAClE,wBAAwB;YACtB;SACF;QACA,eAAe;YAAC,wDAAwD;SAAA;QACxE,yBAAyB;YACvB;SACF;QACA,iDAAiD;YAC/C;SACF;QACA,kDAAkD;YAChD;SACF;QACA,6CAA6C;YAC3C;SACF;QACA,8CAA8C;YAC5C;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,mCAAmC;YACjC;SACF;QACA,yBAAyB;YACvB;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,0CAA0C;YACxC;SACF;QACA,2CAA2C;YACzC;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,wDAAwD;YACtD;SACF;QACA,sDAAsD;YACpD;SACF;QACA,yCAAyC;YACvC;SACF;QACA,uCAAuC;YACrC;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,yDAAyD;YACvD;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,2BAA2B;YACzB;SACF;QACA,0BAA0B;YACxB;SACF;QACA,mBAAmB;YAAC,4CAA4C;SAAA;QAChE,oBAAoB;YAClB;SACF;IACF;IACA,UAAU;QACR,uCAAuC;YAAC,kCAAkC;SAAA;QAC1E,wBAAwB;YAAC,2CAA2C;SAAA;QACpE,0BAA0B;YACxB;SACF;QACA,UAAU;YAAC,YAAY;SAAA;QACvB,qBAAqB;YAAC,wCAAwC;SAAA;QAC9D,WAAW;YAAC,wCAAwC;SAAA;QACpD,2CAA2C;YACzC;SACF;QACA,gCAAgC;YAAC,8BAA8B;SAAA;QAC/D,uCAAuC;YAAC,oBAAoB;SAAA;QAC5D,mCAAmC;YACjC;SACF;QACA,kBAAkB;YAAC,aAAa;SAAA;QAChC,gCAAgC;YAAC,qCAAqC;SAAA;QACtE,yBAAyB;YAAC,qCAAqC;SAAA;QAC/D,qBAAqB;YAAC,wBAAwB;SAAA;QAC9C,2BAA2B;YAAC,uCAAuC;SAAA;QACnE,iCAAiC;YAC/B;SACF;QACA,gBAAgB;YAAC,kCAAkC;SAAA;QACnD,2CAA2C;YACzC;SACF;QACA,qCAAqC;YAAC,mBAAmB;SAAA;QACzD,wBAAwB;YAAC,+BAA+B;SAAA;QACxD,wBAAwB;YAAC,qCAAqC;SAAA;QAC9D,uBAAuB;YAAC,sCAAsC;SAAA;QAC9D,sCAAsC;YAAC,yBAAyB;SAAA;QAChE,qBAAqB;YAAC,uCAAuC;SAAA;QAC7D,yBAAyB;YAAC,oBAAoB;SAAA;QAC9C,6BAA6B;YAAC,yCAAyC;SAAA;QACvE,kBAAkB;YAAC,2CAA2C;SAAA;QAC9D,kBAAkB;YAAC,0CAA0C;SAAA;QAC7D,qBAAqB;YAAC,wCAAwC;SAAA;QAC9D,uBAAuB;YACrB;SACF;QACA,8BAA8B;YAAC,kCAAkC;SAAA;QACjE,gCAAgC;YAAC,qCAAqC;SAAA;IACxE;IACA,MAAM;QACJ,uBAAuB;YACrB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAQ,2CAA2C;iBAAA;YAAE;SACnE;QACA,2CAA2C;YACzC;SACF;QACA,YAAY;YAAC,sCAAsC;SAAA;QACnD,oBAAoB;YAAC,wCAAwC;SAAA;QAC7D,+BAA+B;YAC7B;SACF;QACA,qBAAqB;YAAC,wCAAwC;SAAA;QAC9D,oBAAoB;YAAC,6CAA6C;SAAA;QAClE,aAAa;YAAC,wCAAwC;SAAA;QACtD,kBAAkB;YAAC,UAAU;SAAA;QAC7B,WAAW;YAAC,sBAAsB;SAAA;QAClC,iBAAiB;YAAC,0CAA0C;SAAA;QAC5D,oBAAoB;YAAC,8BAA8B;SAAA;QACnD,qBAAqB;YAAC,wCAAwC;SAAA;QAC9D,+BAA+B;YAC7B;SACF;QACA,sCAAsC;YACpC;SACF;QACA,qBAAqB;YAAC,oCAAoC;SAAA;QAC1D,wBAAwB;YAAC,sBAAsB;SAAA;QAC/C,oBAAoB;YAAC,wCAAwC;SAAA;QAC7D,qBAAqB;YAAC,mDAAmD;SAAA;QACzE,4BAA4B;YAC1B;SACF;QACA,2CAA2C;YACzC;SACF;QACA,6CAA6C;YAC3C;SACF;QACA,mBAAmB;YAAC,wBAAwB;SAAA;QAC5C,uCAAuC;YAAC,yBAAyB;SAAA;QACjE,WAAW;YAAC,gCAAgC;SAAA;QAC5C,kBAAkB;YAAC,wCAAwC;SAAA;QAC3D,mCAAmC;YAAC,gCAAgC;SAAA;QACpE,uCAAuC;YAAC,iCAAiC;SAAA;QACzE,8CAA8C;YAC5C;SACF;QACA,uBAAuB;YAAC,0BAA0B;SAAA;QAClD,0BAA0B;YACxB;SACF;QACA,4BAA4B;YAC1B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAQ,gDAAgD;iBAAA;YAAE;SACxE;QACA,gDAAgD;YAC9C;SACF;QACA,YAAY;YAAC,uCAAuC;SAAA;QACpD,+BAA+B;YAAC,4BAA4B;SAAA;QAC5D,YAAY;YAAC,6CAA6C;SAAA;QAC1D,qBAAqB;YAAC,oDAAoD;SAAA;QAC1E,uBAAuB;YACrB;SACF;QACA,2BAA2B;YAAC,wBAAwB;SAAA;IACtD;IACA,SAAS;QACP,4BAA4B;YAAC,0CAA0C;SAAA;QACvE,6BAA6B;YAC3B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,6BAA6B;YAAC,2CAA2C;SAAA;QACzE,8BAA8B;YAC5B;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,6BAA6B;YAC3B;SACF;IACF;IACA,WAAW;QACT,gBAAgB;YAAC,4BAA4B;SAAA;QAC7C,gBAAgB;YAAC,gDAAgD;SAAA;QACjE,oBAAoB;YAAC,6CAA6C;SAAA;QAClE,kBAAkB;YAAC,2BAA2B;SAAA;QAC9C,gBAAgB;YAAC,+CAA+C;SAAA;IAClE;IACA,QAAQ;QACN,QAAQ;YAAC,uCAAuC;SAAA;QAChD,aAAa;YAAC,yCAAyC;SAAA;QACvD,KAAK;YAAC,qDAAqD;SAAA;QAC3D,UAAU;YAAC,yDAAyD;SAAA;QACpE,iBAAiB;YACf;SACF;QACA,YAAY;YAAC,oDAAoD;SAAA;QACjE,cAAc;YACZ;SACF;QACA,kBAAkB;YAAC,sDAAsD;SAAA;QACzE,cAAc;YACZ;SACF;QACA,gBAAgB;YACd;SACF;QACA,sBAAsB;YACpB;SACF;QACA,QAAQ;YAAC,uDAAuD;SAAA;IAClE;IACA,cAAc;QACZ,eAAe;YACb;SACF;QACA,eAAe;YACb;SACF;QACA,uBAAuB;YACrB;SACF;QACA,gBAAgB;YACd;SACF;QACA,sBAAsB;YACpB;SACF;QACA,UAAU;YACR;YACA,CAAC;YACD;gBAAE,mBAAmB;oBAAE,UAAU;gBAAe;YAAE;SACpD;QACA,aAAa;YACX;SACF;QACA,YAAY;YACV;SACF;QACA,mBAAmB;YACjB;SACF;QACA,iBAAiB;YAAC,uDAAuD;SAAA;QACzE,UAAU;YAAC,2DAA2D;SAAA;QACtE,oBAAoB;YAClB;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,oBAAoB;YAClB;SACF;QACA,kBAAkB;YAAC,sCAAsC;SAAA;QACzD,mBAAmB;YAAC,gDAAgD;SAAA;QACpE,qBAAqB;YACnB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAgB,oBAAoB;iBAAA;YAAE;SACpD;QACA,qBAAqB;YACnB;SACF;QACA,oBAAoB;YAAC,kDAAkD;SAAA;QACvE,aAAa;YACX;SACF;QACA,oBAAoB;YAClB;SACF;QACA,aAAa;YAAC,iDAAiD;SAAA;IACjE;IACA,cAAc;QACZ,qBAAqB;YACnB;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,qBAAqB;YAAC,+CAA+C;SAAA;QACrE,kCAAkC;YAChC;SACF;QACA,qBAAqB;YACnB;SACF;QACA,kCAAkC;YAChC;SACF;QACA,qBAAqB;YACnB;SACF;QACA,kBAAkB;YAChB;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,yBAAyB;YAAC,8CAA8C;SAAA;QACxE,0BAA0B;YACxB;SACF;QACA,uCAAuC;YACrC;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,2CAA2C;YACzC;SACF;QACA,qCAAqC;YACnC;SACF;QACA,2BAA2B;YACzB;SACF;QACA,wCAAwC;YACtC;SACF;QACA,qBAAqB;YACnB;SACF;QACA,+BAA+B;YAC7B;SACF;IACF;IACA,gBAAgB;QACd,sBAAsB;YAAC,uBAAuB;SAAA;QAC9C,gBAAgB;YAAC,6BAA6B;SAAA;IAChD;IACA,YAAY;QACV,4CAA4C;YAC1C;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,uCAAuC;YACrC;SACF;QACA,4BAA4B;YAAC,uBAAuB;SAAA;QACpD,yBAAyB;YACvB;SACF;QACA,0BAA0B;YACxB;SACF;QACA,0CAA0C;YACxC;SACF;QACA,kCAAkC;YAChC;SACF;QACA,oCAAoC;YAClC;SACF;QACA,4BAA4B;YAAC,0CAA0C;SAAA;QACvE,wBAAwB;YACtB;SACF;QACA,iBAAiB;YAAC,qDAAqD;SAAA;QACvE,kBAAkB;YAChB;SACF;QACA,kCAAkC;YAChC;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,2BAA2B;YACzB;SACF;QACA,sCAAsC;YACpC;SACF;QACA,yBAAyB;YAAC,uCAAuC;SAAA;QACjE,iBAAiB;YAAC,+CAA+C;SAAA;QACjE,cAAc;YAAC,kDAAkD;SAAA;QACjE,kCAAkC;YAChC;SACF;QACA,kBAAkB;YAChB;SACF;QACA,eAAe;YACb;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,mDAAmD;YACjD;SACF;QACA,0BAA0B;YAAC,sBAAsB;SAAA;QACjD,oBAAoB;YAClB;YACA,CAAC;YACD;gBAAE,mBAAmB;oBAAE,QAAQ;gBAAM;YAAE;SACzC;QACA,sCAAsC;YACpC;SACF;QACA,gBAAgB;YAAC,oCAAoC;SAAA;QACrD,iBAAiB;YAAC,8CAA8C;SAAA;QAChE,+CAA+C;YAC7C;SACF;QACA,iCAAiC;YAAC,8BAA8B;SAAA;QAChE,+BAA+B;YAC7B;SACF;QACA,uCAAuC;YACrC;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,+CAA+C;YAC7C;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,kCAAkC;YAChC;SACF;QACA,8CAA8C;YAC5C;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,2BAA2B;YAAC,8CAA8C;SAAA;QAC1E,0BAA0B;YAAC,6CAA6C;SAAA;QACxE,oBAAoB;YAClB;SACF;QACA,4BAA4B;YAAC,yCAAyC;SAAA;IACxE;IACA,SAAS;QACP,yBAAyB;YACvB;SACF;QACA,yBAAyB;YACvB;SACF;QACA,qCAAqC;YACnC;SACF;QACA,qCAAqC;YACnC;SACF;QACA,+BAA+B;YAAC,iCAAiC;SAAA;QACjE,uBAAuB;YAAC,kDAAkD;SAAA;QAC1E,+BAA+B;YAAC,iCAAiC;SAAA;QACjE,8BAA8B;YAC5B;SACF;QACA,kBAAkB;YAAC,uCAAuC;SAAA;IAC5D;IACA,aAAa;QAAE,QAAQ;YAAC,0BAA0B;SAAA;IAAE;IACpD,YAAY;QACV,4BAA4B;YAC1B;SACF;QACA,yBAAyB;YACvB;SACF;QACA,0BAA0B;YACxB;SACF;QACA,iBAAiB;YAAC,qDAAqD;SAAA;QACvE,kBAAkB;YAChB;SACF;QACA,UAAU;YAAC,4DAA4D;SAAA;QACvE,iBAAiB;YAAC,+CAA+C;SAAA;QACjE,cAAc;YAAC,kDAAkD;SAAA;QACjE,kBAAkB;YAChB;SACF;QACA,eAAe;YACb;SACF;QACA,yBAAyB;YACvB;SACF;QACA,kBAAkB;YAAC,mCAAmC;SAAA;QACtD,mBAAmB;YAAC,6CAA6C;SAAA;QACjE,gBAAgB;YAAC,oCAAoC;SAAA;QACrD,iBAAiB;YAAC,8CAA8C;SAAA;QAChE,+BAA+B;YAC7B;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,aAAa;YACX;SACF;IACF;IACA,iBAAiB;QACf,0BAA0B;YACxB;SACF;QACA,WAAW;YACT;SACF;QACA,YAAY;YAAC,iDAAiD;SAAA;IAChE;IACA,QAAQ;QAAE,KAAK;YAAC,aAAa;SAAA;IAAE;IAC/B,OAAO;QACL,gBAAgB;YAAC,2BAA2B;SAAA;QAC5C,QAAQ;YAAC,aAAa;SAAA;QACtB,eAAe;YAAC,gCAAgC;SAAA;QAChD,QAAQ;YAAC,yBAAyB;SAAA;QAClC,eAAe;YAAC,+CAA+C;SAAA;QAC/D,MAAM;YAAC,6BAA6B;SAAA;QACpC,KAAK;YAAC,sBAAsB;SAAA;QAC5B,YAAY;YAAC,4CAA4C;SAAA;QACzD,aAAa;YAAC,4BAA4B;SAAA;QAC1C,MAAM;YAAC,YAAY;SAAA;QACnB,cAAc;YAAC,+BAA+B;SAAA;QAC9C,aAAa;YAAC,8BAA8B;SAAA;QAC5C,aAAa;YAAC,6BAA6B;SAAA;QAC3C,WAAW;YAAC,4BAA4B;SAAA;QACxC,YAAY;YAAC,mBAAmB;SAAA;QAChC,aAAa;YAAC,oBAAoB;SAAA;QAClC,MAAM;YAAC,2BAA2B;SAAA;QAClC,QAAQ;YAAC,8BAA8B;SAAA;QACvC,QAAQ;YAAC,wBAAwB;SAAA;QACjC,eAAe;YAAC,8CAA8C;SAAA;IAChE;IACA,KAAK;QACH,YAAY;YAAC,sCAAsC;SAAA;QACnD,cAAc;YAAC,wCAAwC;SAAA;QACvD,WAAW;YAAC,qCAAqC;SAAA;QACjD,WAAW;YAAC,qCAAqC;SAAA;QACjD,YAAY;YAAC,sCAAsC;SAAA;QACnD,WAAW;YAAC,6CAA6C;SAAA;QACzD,SAAS;YAAC,gDAAgD;SAAA;QAC1D,WAAW;YAAC,oDAAoD;SAAA;QAChE,QAAQ;YAAC,yCAAyC;SAAA;QAClD,QAAQ;YAAC,8CAA8C;SAAA;QACvD,SAAS;YAAC,gDAAgD;SAAA;QAC1D,kBAAkB;YAAC,mDAAmD;SAAA;QACtE,WAAW;YAAC,4CAA4C;SAAA;IAC1D;IACA,WAAW;QACT,iBAAiB;YAAC,0BAA0B;SAAA;QAC5C,aAAa;YAAC,iCAAiC;SAAA;IACjD;IACA,eAAe;QACb,kCAAkC;YAChC;SACF;QACA,mCAAmC;YACjC;SACF;QACA,+BAA+B;YAC7B;SACF;QACA,0BAA0B;YACxB;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,kCAAkC;YAChC;SACF;IACF;IACA,cAAc;QACZ,qCAAqC;YAAC,8BAA8B;SAAA;QACpE,uBAAuB;YAAC,oCAAoC;SAAA;QAC5D,wBAAwB;YAAC,8CAA8C;SAAA;QACvE,mCAAmC;YACjC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAgB,qCAAqC;iBAAA;YAAE;SACrE;QACA,wCAAwC;YAAC,iCAAiC;SAAA;QAC1E,0BAA0B;YAAC,uCAAuC;SAAA;QAClE,2BAA2B;YACzB;SACF;QACA,sCAAsC;YACpC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAgB,wCAAwC;iBAAA;YAAE;SACxE;QACA,qCAAqC;YAAC,8BAA8B;SAAA;QACpE,uBAAuB;YAAC,oCAAoC;SAAA;QAC5D,wBAAwB;YAAC,8CAA8C;SAAA;QACvE,mCAAmC;YACjC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAgB,qCAAqC;iBAAA;YAAE;SACrE;IACF;IACA,QAAQ;QACN,cAAc;YACZ;SACF;QACA,WAAW;YAAC,yDAAyD;SAAA;QACrE,aAAa;YACX;SACF;QACA,wBAAwB;YAAC,gDAAgD;SAAA;QACzE,+BAA+B;YAC7B;SACF;QACA,QAAQ;YAAC,mCAAmC;SAAA;QAC5C,eAAe;YACb;SACF;QACA,aAAa;YAAC,mCAAmC;SAAA;QACjD,iBAAiB;YAAC,uCAAuC;SAAA;QACzD,eAAe;YACb;SACF;QACA,aAAa;YAAC,4CAA4C;SAAA;QAC1D,iBAAiB;YACf;SACF;QACA,KAAK;YAAC,iDAAiD;SAAA;QACvD,YAAY;YAAC,wDAAwD;SAAA;QACrE,UAAU;YAAC,oDAAoD;SAAA;QAC/D,UAAU;YAAC,yCAAyC;SAAA;QACpD,cAAc;YAAC,yDAAyD;SAAA;QACxE,MAAM;YAAC,aAAa;SAAA;QACpB,eAAe;YAAC,qCAAqC;SAAA;QACrD,cAAc;YAAC,0DAA0D;SAAA;QACzE,qBAAqB;YAAC,2CAA2C;SAAA;QACjE,YAAY;YAAC,wDAAwD;SAAA;QACrE,mBAAmB;YAAC,yCAAyC;SAAA;QAC7D,uBAAuB;YACrB;SACF;QACA,0BAA0B;YAAC,kBAAkB;SAAA;QAC7C,YAAY;YAAC,wBAAwB;SAAA;QACrC,aAAa;YAAC,kCAAkC;SAAA;QAChD,wBAAwB;YACtB;SACF;QACA,mBAAmB;YAAC,kCAAkC;SAAA;QACtD,mBAAmB;YACjB;SACF;QACA,gBAAgB;YAAC,sCAAsC;SAAA;QACvD,eAAe;YACb;SACF;QACA,MAAM;YAAC,sDAAsD;SAAA;QAC7D,iBAAiB;YACf;SACF;QACA,iBAAiB;YACf;SACF;QACA,aAAa;YACX;SACF;QACA,gBAAgB;YACd;SACF;QACA,sBAAsB;YACpB;SACF;QACA,WAAW;YAAC,wDAAwD;SAAA;QACpE,QAAQ;YAAC,yDAAyD;SAAA;QAClE,QAAQ;YAAC,mDAAmD;SAAA;QAC5D,eAAe;YAAC,0DAA0D;SAAA;QAC1E,aAAa;YAAC,2CAA2C;SAAA;QACzD,iBAAiB;YACf;SACF;IACF;IACA,UAAU;QACR,KAAK;YAAC,yBAAyB;SAAA;QAC/B,oBAAoB;YAAC,eAAe;SAAA;QACpC,YAAY;YAAC,mCAAmC;SAAA;IAClD;IACA,UAAU;QACR,QAAQ;YAAC,gBAAgB;SAAA;QACzB,WAAW;YACT;YACA;gBAAE,SAAS;oBAAE,gBAAgB;gBAA4B;YAAE;SAC7D;IACF;IACA,MAAM;QACJ,KAAK;YAAC,WAAW;SAAA;QACjB,gBAAgB;YAAC,eAAe;SAAA;QAChC,YAAY;YAAC,cAAc;SAAA;QAC3B,QAAQ;YAAC,UAAU;SAAA;QACnB,MAAM;YAAC,OAAO;SAAA;IAChB;IACA,YAAY;QACV,mCAAmC;YACjC;SACF;QACA,qBAAqB;YACnB;SACF;QACA,uBAAuB;YACrB;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,+BAA+B;YAAC,qCAAqC;SAAA;QACrE,iBAAiB;YAAC,2CAA2C;SAAA;QAC7D,0BAA0B;YAAC,sBAAsB;SAAA;QACjD,YAAY;YAAC,4BAA4B;SAAA;QACzC,+BAA+B;YAC7B;SACF;QACA,iBAAiB;YAAC,wDAAwD;SAAA;QAC1E,kBAAkB;YAChB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAc,+BAA+B;iBAAA;YAAE;SAC7D;QACA,2BAA2B;YAAC,uBAAuB;SAAA;QACnD,aAAa;YAAC,6BAA6B;SAAA;QAC3C,gCAAgC;YAC9B;SACF;QACA,kBAAkB;YAChB;SACF;IACF;IACA,MAAM;QACJ,gCAAgC;YAC9B;SACF;QACA,mCAAmC;YACjC;SACF;IACF;IACA,MAAM;QACJ,wBAAwB;YACtB;YACA,CAAC;YACD;gBACE,YACE;YACJ;SACF;QACA,qBAAqB;YACnB;SACF;QACA,qBAAqB;YACnB;SACF;QACA,WAAW;YAAC,mCAAmC;SAAA;QAC/C,kBAAkB;YAAC,gDAAgD;SAAA;QACnE,kBAAkB;YAAC,mCAAmC;SAAA;QACtD,wBAAwB;YAAC,oCAAoC;SAAA;QAC7D,8BAA8B;YAAC,2CAA2C;SAAA;QAC1E,oCAAoC;YAClC;SACF;QACA,kBAAkB;YAAC,8BAA8B;SAAA;QACjD,iBAAiB;YAAC,8BAA8B;SAAA;QAChD,gCAAgC;YAAC,qCAAqC;SAAA;QACtE,8CAA8C;YAC5C;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,eAAe;YAAC,wBAAwB;SAAA;QACxC,QAAQ;YAAC,oBAAoB;SAAA;QAC7B,iBAAiB;YAAC,gDAAgD;SAAA;QAClE,eAAe;YAAC,oCAAoC;SAAA;QACpD,6CAA6C;YAC3C;YACA,CAAC;YACD;gBACE,YACE;YACJ;SACF;QACA,KAAK;YAAC,iBAAiB;SAAA;QACvB,wBAAwB;YAAC,mCAAmC;SAAA;QAC5D,mBAAmB;YACjB;SACF;QACA,mCAAmC;YAAC,kCAAkC;SAAA;QACtE,sBAAsB;YAAC,wCAAwC;SAAA;QAC/D,YAAY;YAAC,8CAA8C;SAAA;QAC3D,sBAAsB;YAAC,+CAA+C;SAAA;QACtE,sBAAsB;YACpB;SACF;QACA,YAAY;YAAC,iCAAiC;SAAA;QAC9C,wBAAwB;YAAC,wCAAwC;SAAA;QACjE,oBAAoB;YAClB;SACF;QACA,MAAM;YAAC,oBAAoB;SAAA;QAC3B,sBAAsB;YAAC,+BAA+B;SAAA;QACtD,kBAAkB;YAAC,+CAA+C;SAAA;QAClE,kBAAkB;YAAC,wBAAwB;SAAA;QAC3C,oCAAoC;YAAC,mCAAmC;SAAA;QACxE,uBAAuB;YAAC,oCAAoC;SAAA;QAC5D,0BAA0B;YAAC,gBAAgB;SAAA;QAC3C,aAAa;YAAC,4BAA4B;SAAA;QAC1C,qBAAqB;YAAC,mDAAmD;SAAA;QACzE,gBAAgB;YAAC,6BAA6B;SAAA;QAC9C,aAAa;YAAC,yBAAyB;SAAA;QACvC,qCAAqC;YAAC,4BAA4B;SAAA;QAClE,kBAAkB;YAAC,oDAAoD;SAAA;QACvE,kBAAkB;YAAC,oDAAoD;SAAA;QACvE,cAAc;YAAC,oCAAoC;SAAA;QACnD,wCAAwC;YACtC;SACF;QACA,0BAA0B;YAAC,uCAAuC;SAAA;QAClE,0BAA0B;YACxB;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,sBAAsB;YAAC,gDAAgD;SAAA;QACvE,eAAe;YAAC,wCAAwC;SAAA;QACxD,wBAAwB;YAAC,6BAA6B;SAAA;QACtD,mBAAmB;YAAC,gCAAgC;SAAA;QACpD,0BAA0B;YACxB;YACA,CAAC;YACD;gBACE,YACE;YACJ;SACF;QACA,uBAAuB;YAAC,4CAA4C;SAAA;QACpE,cAAc;YAAC,uBAAuB;SAAA;QACtC,aAAa;YAAC,wCAAwC;SAAA;QACtD,0BAA0B;YACxB;SACF;QACA,sBAAsB;YACpB;SACF;QACA,cAAc;YAAC,uCAAuC;SAAA;QACtD,yBAAyB;YAAC,2CAA2C;SAAA;QACrE,2BAA2B;YACzB;SACF;QACA,4CAA4C;YAC1C;SACF;QACA,2BAA2B;YACzB;YACA,CAAC;YACD;gBACE,YACE;YACJ;SACF;QACA,uBAAuB;YACrB;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,uBAAuB;YACrB;SACF;QACA,uBAAuB;YACrB;SACF;QACA,mBAAmB;YACjB;SACF;QACA,mBAAmB;YACjB;SACF;QACA,sBAAsB;YAAC,wCAAwC;SAAA;QAC/D,yCAAyC;YACvC;SACF;QACA,aAAa;YAAC,sCAAsC;SAAA;QACpD,QAAQ;YAAC,mBAAmB;SAAA;QAC5B,iBAAiB;YAAC,6CAA6C;SAAA;QAC/D,sCAAsC;YACpC;SACF;QACA,iBAAiB;YAAC,kDAAkD;SAAA;QACpE,mBAAmB;YAAC,yCAAyC;SAAA;QAC7D,eAAe;YAAC,mCAAmC;SAAA;QACnD,2BAA2B;YAAC,0CAA0C;SAAA;IACxE;IACA,UAAU;QACR,mCAAmC;YACjC;SACF;QACA,qBAAqB;YACnB;SACF;QACA,sBAAsB;YACpB;SACF;QACA,0CAA0C;YACxC;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,8CAA8C;YAC5C;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAY,2CAA2C;iBAAA;YAAE;SACvE;QACA,6DAA6D;YAC3D;YACA,CAAC;YACD;gBACE,SAAS;oBACP;oBACA;iBACF;YACF;SACF;QACA,yDAAyD;YACvD;SACF;QACA,2CAA2C;YACzC;SACF;QACA,4CAA4C;YAC1C;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,2BAA2B;YACzB;SACF;QACA,mBAAmB;YACjB;SACF;QACA,uCAAuC;YACrC;SACF;QACA,kCAAkC;YAChC;SACF;QACA,0BAA0B;YACxB;SACF;QACA,4DAA4D;YAC1D;SACF;QACA,uDAAuD;YACrD;SACF;QACA,+CAA+C;YAC7C;SACF;QACA,kCAAkC;YAAC,oBAAoB;SAAA;QACvD,6BAA6B;YAAC,0BAA0B;SAAA;QACxD,qBAAqB;YAAC,gCAAgC;SAAA;QACtD,oCAAoC;YAClC;SACF;QACA,sBAAsB;YACpB;SACF;QACA,uBAAuB;YACrB;SACF;QACA,2CAA2C;YACzC;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,8BAA8B;YAC5B;SACF;IACF;IACA,mBAAmB;QACjB,0BAA0B;YAAC,qCAAqC;SAAA;QAChE,0BAA0B;YACxB;SACF;QACA,uBAAuB;YAAC,kDAAkD;SAAA;QAC1E,iBAAiB;YAAC,+CAA+C;SAAA;QACjE,0BAA0B;YAAC,oCAAoC;SAAA;QAC/D,0BAA0B;YACxB;SACF;IACF;IACA,OAAO;QACL,eAAe;YAAC,qDAAqD;SAAA;QACrE,QAAQ;YAAC,kCAAkC;SAAA;QAC3C,6BAA6B;YAC3B;SACF;QACA,cAAc;YAAC,wDAAwD;SAAA;QACvE,qBAAqB;YACnB;SACF;QACA,qBAAqB;YACnB;SACF;QACA,qBAAqB;YACnB;SACF;QACA,eAAe;YACb;SACF;QACA,KAAK;YAAC,+CAA+C;SAAA;QACrD,WAAW;YACT;SACF;QACA,kBAAkB;YAAC,uDAAuD;SAAA;QAC1E,MAAM;YAAC,iCAAiC;SAAA;QACxC,uBAAuB;YACrB;SACF;QACA,aAAa;YAAC,uDAAuD;SAAA;QACrE,WAAW;YAAC,qDAAqD;SAAA;QACjE,wBAAwB;YACtB;SACF;QACA,oBAAoB;YAClB;SACF;QACA,2BAA2B;YAAC,0CAA0C;SAAA;QACtE,aAAa;YAAC,uDAAuD;SAAA;QACrE,OAAO;YAAC,qDAAqD;SAAA;QAC7D,0BAA0B;YACxB;SACF;QACA,kBAAkB;YAChB;SACF;QACA,cAAc;YACZ;SACF;QACA,QAAQ;YAAC,iDAAiD;SAAA;QAC1D,cAAc;YACZ;SACF;QACA,cAAc;YACZ;SACF;QACA,qBAAqB;YACnB;SACF;IACF;IACA,WAAW;QAAE,KAAK;YAAC,iBAAiB;SAAA;IAAE;IACtC,WAAW;QACT,wBAAwB;YACtB;SACF;QACA,gBAAgB;YACd;SACF;QACA,uBAAuB;YACrB;SACF;QACA,mCAAmC;YACjC;SACF;QACA,kBAAkB;YAChB;SACF;QACA,qCAAqC;YACnC;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,wBAAwB;YACtB;SACF;QACA,gBAAgB;YACd;SACF;QACA,uBAAuB;YACrB;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,kBAAkB;YAChB;SACF;QACA,yBAAyB;YACvB;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,sBAAsB;YACpB;SACF;QACA,cAAc;YAAC,2DAA2D;SAAA;QAC1E,qBAAqB;YACnB;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,gBAAgB;YACd;SACF;QACA,mCAAmC;YACjC;SACF;QACA,4BAA4B;YAC1B;SACF;IACF;IACA,OAAO;QACL,kBAAkB;YAChB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,sCAAsC;iBAAA;YAAE;SAC/D;QACA,sCAAsC;YACpC;SACF;QACA,0BAA0B;YACxB;YACA,CAAC;YACD;gBAAE,WAAW;YAAO;SACtB;QACA,iBAAiB;YAAC,oDAAoD;SAAA;QACtE,wBAAwB;YACtB;YACA,CAAC;YACD;gBAAE,WAAW;YAAW;SAC1B;QACA,2BAA2B;YACzB;YACA,CAAC;YACD;gBAAE,WAAW;YAAQ;SACvB;QACA,2BAA2B;YACzB;YACA,CAAC;YACD;gBAAE,WAAW;YAAQ;SACvB;QACA,uBAAuB;YACrB;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,mBAAmB;YAAC,oDAAoD;SAAA;QACxE,oCAAoC;YAClC;SACF;QACA,0BAA0B;YACxB;SACF;QACA,kBAAkB;YAAC,6CAA6C;SAAA;QAChE,gBAAgB;YAAC,mDAAmD;SAAA;QACpE,4BAA4B;YAC1B;SACF;QACA,mBAAmB;YAAC,yCAAyC;SAAA;QAC7D,gBAAgB;YAAC,sCAAsC;SAAA;QACvD,qBAAqB;YACnB;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,oBAAoB;YAAC,2CAA2C;SAAA;QAChE,iBAAiB;YAAC,iCAAiC;SAAA;QACnD,kBAAkB;YAAC,wCAAwC;SAAA;QAC3D,8BAA8B;YAC5B;SACF;QACA,gCAAgC;YAC9B;SACF;QACA,wBAAwB;YACtB;SACF;QACA,qBAAqB;YAAC,uCAAuC;SAAA;QAC7D,4BAA4B;YAAC,kBAAkB;SAAA;QAC/C,YAAY;YAAC,kCAAkC;SAAA;QAC/C,aAAa;YAAC,wBAAwB;SAAA;QACtC,sCAAsC;YACpC;SACF;QACA,2BAA2B;YACzB;SACF;QACA,4BAA4B;YAAC,2CAA2C;SAAA;QACxE,kBAAkB;YAAC,2BAA2B;SAAA;QAC9C,uBAAuB;YAAC,8CAA8C;SAAA;QACtE,iBAAiB;YAAC,kCAAkC;SAAA;QACpD,eAAe;YAAC,qCAAqC;SAAA;QACrD,mBAAmB;YAAC,qCAAqC;SAAA;QACzD,qBAAqB;YACnB;SACF;QACA,eAAe;YAAC,kCAAkC;SAAA;QAClD,mBAAmB;YACjB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,uCAAuC;iBAAA;YAAE;SAChE;QACA,uCAAuC;YACrC;SACF;QACA,QAAQ;YAAC,8BAA8B;SAAA;QACvC,0BAA0B;YACxB;SACF;QACA,6BAA6B;YAC3B;SACF;QACA,qBAAqB;YACnB;SACF;QACA,gBAAgB;YAAC,sDAAsD;SAAA;QACvE,wBAAwB;YACtB;SACF;QACA,qBAAqB;YAAC,oDAAoD;SAAA;QAC1E,iCAAiC;YAC/B;SACF;QACA,iBAAiB;YAAC,4CAA4C;SAAA;QAC9D,kBAAkB;YAChB;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,YAAY;YAAC,8CAA8C;SAAA;QAC3D,kBAAkB;YAChB;SACF;QACA,kBAAkB;YAAC,0CAA0C;SAAA;QAC7D,iBAAiB;YAAC,oCAAoC;SAAA;QACtD,mCAAmC;YACjC;SACF;QACA,eAAe;YAAC,oDAAoD;SAAA;QACpE,oBAAoB;YAClB;SACF;QACA,mBAAmB;YAAC,oDAAoD;SAAA;QACxE,eAAe;YAAC,8CAA8C;SAAA;QAC9D,+BAA+B;YAC7B;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,sCAAsC;YACpC;SACF;QACA,4BAA4B;YAC1B;SACF;QACA,iBAAiB;YACf;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,wBAAwB;iBAAA;YAAE;SACjD;QACA,wBAAwB;YAAC,yCAAyC;SAAA;QAClE,wBAAwB;YAAC,yCAAyC;SAAA;QAClE,8BAA8B;YAC5B;SACF;QACA,qCAAqC;YACnC;SACF;QACA,2BAA2B;YACzB;SACF;QACA,sBAAsB;YACpB;SACF;QACA,KAAK;YAAC,2BAA2B;SAAA;QACjC,uBAAuB;YACrB;SACF;QACA,0BAA0B;YACxB;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,oBAAoB;YAAC,wCAAwC;SAAA;QAC7D,2BAA2B;YACzB;SACF;QACA,cAAc;YAAC,kCAAkC;SAAA;QACjD,oCAAoC;YAClC;SACF;QACA,aAAa;YAAC,mDAAmD;SAAA;QACjE,WAAW;YAAC,6CAA6C;SAAA;QACzD,qBAAqB;YACnB;SACF;QACA,gBAAgB;YAAC,mDAAmD;SAAA;QACpE,WAAW;YAAC,0CAA0C;SAAA;QACtD,uBAAuB;YAAC,gDAAgD;SAAA;QACxE,gCAAgC;YAC9B;SACF;QACA,yBAAyB;YAAC,gDAAgD;SAAA;QAC1E,WAAW;YAAC,yCAAyC;SAAA;QACrD,wBAAwB;YAAC,iDAAiD;SAAA;QAC1E,kBAAkB;YAAC,iDAAiD;SAAA;QACpE,8BAA8B;YAC5B;SACF;QACA,4BAA4B;YAAC,6CAA6C;SAAA;QAC1E,YAAY;YAAC,2CAA2C;SAAA;QACxD,sBAAsB;YAAC,8CAA8C;SAAA;QACrE,mCAAmC;YACjC;SACF;QACA,2BAA2B;YAAC,6CAA6C;SAAA;QACzE,cAAc;YAAC,yCAAyC;SAAA;QACxD,eAAe;YAAC,uDAAuD;SAAA;QACvE,2BAA2B;YACzB;SACF;QACA,qBAAqB;YACnB;SACF;QACA,gBAAgB;YACd;SACF;QACA,qBAAqB;YAAC,+CAA+C;SAAA;QACrE,kBAAkB;YAAC,2CAA2C;SAAA;QAC9D,iBAAiB;YAAC,sDAAsD;SAAA;QACxE,kBAAkB;YAAC,sCAAsC;SAAA;QACzD,eAAe;YAAC,uCAAuC;SAAA;QACvD,gBAAgB;YAAC,0BAA0B;SAAA;QAC3C,UAAU;YAAC,iCAAiC;SAAA;QAC5C,eAAe;YAAC,mDAAmD;SAAA;QACnE,oBAAoB;YAClB;SACF;QACA,qBAAqB;YAAC,wCAAwC;SAAA;QAC9D,uBAAuB;YAAC,+CAA+C;SAAA;QACvE,gCAAgC;YAC9B;SACF;QACA,mBAAmB;YAAC,4CAA4C;SAAA;QAChE,WAAW;YAAC,kCAAkC;SAAA;QAC9C,sBAAsB;YAAC,wCAAwC;SAAA;QAC/D,YAAY;YAAC,iDAAiD;SAAA;QAC9D,iBAAiB;YAAC,sDAAsD;SAAA;QACxE,iBAAiB;YAAC,+CAA+C;SAAA;QACjE,kBAAkB;YAChB;SACF;QACA,mBAAmB;YAAC,gDAAgD;SAAA;QACpE,gBAAgB;YAAC,iDAAiD;SAAA;QAClE,uBAAuB;YACrB;SACF;QACA,uBAAuB;YACrB;SACF;QACA,iBAAiB;YAAC,oCAAoC;SAAA;QACtD,2BAA2B;YACzB;SACF;QACA,qCAAqC;YACnC;SACF;QACA,aAAa;YAAC,iDAAiD;SAAA;QAC/D,iBAAiB;YAAC,qDAAqD;SAAA;QACvE,qCAAqC;YACnC;SACF;QACA,UAAU;YAAC,yCAAyC;SAAA;QACpD,YAAY;YAAC,2CAA2C;SAAA;QACxD,yBAAyB;YACvB;SACF;QACA,oBAAoB;YAClB;SACF;QACA,gBAAgB;YAAC,oCAAoC;SAAA;QACrD,kBAAkB;YAChB;SACF;QACA,eAAe;YAAC,qCAAqC;SAAA;QACrD,cAAc;YAAC,oCAAoC;SAAA;QACnD,2BAA2B;YACzB;SACF;QACA,mBAAmB;YAAC,yCAAyC;SAAA;QAC7D,uBAAuB;YACrB;SACF;QACA,2BAA2B;YAAC,oCAAoC;SAAA;QAChE,0BAA0B;YACxB;SACF;QACA,aAAa;YAAC,mCAAmC;SAAA;QACjD,kBAAkB;YAAC,wCAAwC;SAAA;QAC3D,sCAAsC;YACpC;SACF;QACA,gBAAgB;YAAC,gCAAgC;SAAA;QACjD,8BAA8B;YAC5B;SACF;QACA,wBAAwB;YACtB;SACF;QACA,iBAAiB;YAAC,uCAAuC;SAAA;QACzD,0BAA0B;YAAC,iBAAiB;SAAA;QAC5C,YAAY;YAAC,uBAAuB;SAAA;QACpC,aAAa;YAAC,6BAA6B;SAAA;QAC3C,WAAW;YAAC,iCAAiC;SAAA;QAC7C,iBAAiB;YAAC,uCAAuC;SAAA;QACzD,qCAAqC;YAAC,kCAAkC;SAAA;QACxE,eAAe;YAAC,qCAAqC;SAAA;QACrD,iBAAiB;YAAC,wCAAwC;SAAA;QAC1D,YAAY;YAAC,mBAAmB;SAAA;QAChC,sCAAsC;YACpC;SACF;QACA,mBAAmB;YACjB;SACF;QACA,cAAc;YAAC,oCAAoC;SAAA;QACnD,UAAU;YAAC,gCAAgC;SAAA;QAC3C,WAAW;YAAC,iCAAiC;SAAA;QAC7C,uBAAuB;YACrB;SACF;QACA,cAAc;YAAC,iCAAiC;SAAA;QAChD,OAAO;YAAC,mCAAmC;SAAA;QAC3C,eAAe;YAAC,2CAA2C;SAAA;QAC3D,aAAa;YAAC,kDAAkD;SAAA;QAChE,0BAA0B;YACxB;SACF;QACA,6BAA6B;YAC3B;YACA,CAAC;YACD;gBAAE,WAAW;YAAO;SACtB;QACA,oBAAoB;YAClB;SACF;QACA,2BAA2B;YACzB;YACA,CAAC;YACD;gBAAE,WAAW;YAAW;SAC1B;QACA,6BAA6B;YAC3B;SACF;QACA,8BAA8B;YAC5B;YACA,CAAC;YACD;gBAAE,WAAW;YAAQ;SACvB;QACA,8BAA8B;YAC5B;YACA,CAAC;YACD;gBAAE,WAAW;YAAQ;SACvB;QACA,cAAc;YAAC,qDAAqD;SAAA;QACpE,kBAAkB;YAAC,kCAAkC;SAAA;QACrD,mBAAmB;YAAC,yCAAyC;SAAA;QAC7D,0BAA0B;YACxB;SACF;QACA,0BAA0B;YACxB;YACA,CAAC;YACD;gBAAE,WAAW;YAAO;SACtB;QACA,wBAAwB;YACtB;YACA,CAAC;YACD;gBAAE,WAAW;YAAW;SAC1B;QACA,2BAA2B;YACzB;YACA,CAAC;YACD;gBAAE,WAAW;YAAQ;SACvB;QACA,2BAA2B;YACzB;YACA,CAAC;YACD;gBAAE,WAAW;YAAQ;SACvB;QACA,iBAAiB;YAAC,kDAAkD;SAAA;QACpE,UAAU;YAAC,qCAAqC;SAAA;QAChD,QAAQ;YAAC,6BAA6B;SAAA;QACtC,wBAAwB;YACtB;SACF;QACA,qBAAqB;YAAC,mDAAmD;SAAA;QACzE,8BAA8B;YAC5B;SACF;QACA,iCAAiC;YAAC,iCAAiC;SAAA;QACnE,kBAAkB;YAChB;SACF;QACA,kBAAkB;YAAC,uCAAuC;SAAA;QAC1D,mCAAmC;YACjC;SACF;QACA,eAAe;YAAC,mDAAmD;SAAA;QACnE,oBAAoB;YAClB;SACF;QACA,mBAAmB;YAAC,iDAAiD;SAAA;QACrE,4BAA4B;YAC1B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,6BAA6B;iBAAA;YAAE;SACtD;QACA,6BAA6B;YAC3B;SACF;QACA,eAAe;YAAC,6CAA6C;SAAA;QAC7D,4BAA4B;YAC1B;SACF;QACA,oBAAoB;YAClB;YACA;gBAAE,SAAS;YAA6B;SAC1C;IACF;IACA,QAAQ;QACN,MAAM;YAAC,kBAAkB;SAAA;QACzB,SAAS;YAAC,qBAAqB;SAAA;QAC/B,uBAAuB;YACrB;YACA,CAAC;YACD;gBACE,YACE;YACJ;SACF;QACA,QAAQ;YAAC,oBAAoB;SAAA;QAC7B,OAAO;YAAC,0BAA0B;SAAA;QAClC,QAAQ;YAAC,oBAAoB;SAAA;QAC7B,OAAO;YAAC,mBAAmB;SAAA;IAC7B;IACA,gBAAgB;QACd,4BAA4B;YAC1B;SACF;QACA,UAAU;YACR;SACF;QACA,gBAAgB;YAAC,wDAAwD;SAAA;QACzE,yBAAyB;YACvB;SACF;QACA,kBAAkB;YAAC,wCAAwC;SAAA;QAC3D,mBAAmB;YAAC,kDAAkD;SAAA;QACtE,uBAAuB;YACrB;SACF;QACA,aAAa;YACX;SACF;IACF;IACA,oBAAoB;QAClB,YAAY;YACV;SACF;QACA,kCAAkC;YAChC;SACF;QACA,0BAA0B;YACxB;SACF;QACA,oCAAoC;YAClC;SACF;QACA,mBAAmB;YAAC,2BAA2B;SAAA;QAC/C,uBAAuB;YACrB;SACF;QACA,sBAAsB;YAAC,iBAAiB;SAAA;QACxC,6BAA6B;YAAC,qCAAqC;SAAA;QACnE,0BAA0B;YAAC,+CAA+C;SAAA;QAC1E,0BAA0B;YACxB;SACF;IACF;IACA,OAAO;QACL,mCAAmC;YACjC;SACF;QACA,iCAAiC;YAC/B;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,QAAQ;YAAC,wBAAwB;SAAA;QACjC,8BAA8B;YAC5B;SACF;QACA,uBAAuB;YAAC,gDAAgD;SAAA;QACxE,8BAA8B;YAC5B;SACF;QACA,uBAAuB;YACrB;SACF;QACA,aAAa;YAAC,sCAAsC;SAAA;QACpD,WAAW;YAAC,mCAAmC;SAAA;QAC/C,2BAA2B;YACzB;SACF;QACA,oBAAoB;YAClB;SACF;QACA,2BAA2B;YACzB;SACF;QACA,MAAM;YAAC,uBAAuB;SAAA;QAC9B,gBAAgB;YAAC,yCAAyC;SAAA;QAC1D,6BAA6B;YAC3B;SACF;QACA,sBAAsB;YAAC,+CAA+C;SAAA;QACtE,0BAA0B;YAAC,iBAAiB;SAAA;QAC5C,kBAAkB;YAAC,2CAA2C;SAAA;QAC9D,6BAA6B;YAC3B;SACF;QACA,gBAAgB;YAAC,yCAAyC;SAAA;QAC1D,8BAA8B;YAC5B;SACF;QACA,iBAAiB;YACf;SACF;QACA,8BAA8B;YAC5B;SACF;QACA,uBAAuB;YACrB;SACF;QACA,aAAa;YAAC,qCAAqC;SAAA;IACrD;IACA,OAAO;QACL,0BAA0B;YACxB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,8BAA8B;iBAAA;YAAE;SACvD;QACA,8BAA8B;YAAC,mBAAmB;SAAA;QAClD,sCAAsC;YAAC,4BAA4B;SAAA;QACnE,OAAO;YAAC,6BAA6B;SAAA;QACrC,cAAc;YAAC,6BAA6B;SAAA;QAC5C,uBAAuB;YAAC,+CAA+C;SAAA;QACvE,sCAAsC;YAAC,gCAAgC;SAAA;QACvE,8BAA8B;YAC5B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,kCAAkC;iBAAA;YAAE;SAC3D;QACA,kCAAkC;YAAC,qBAAqB;SAAA;QACxD,oCAAoC;YAClC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,wCAAwC;iBAAA;YAAE;SACjE;QACA,wCAAwC;YAAC,iBAAiB;SAAA;QAC1D,yCAAyC;YAAC,6BAA6B;SAAA;QACvE,6BAA6B;YAC3B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,iCAAiC;iBAAA;YAAE;SAC1D;QACA,iCAAiC;YAAC,qBAAqB;SAAA;QACvD,8BAA8B;YAC5B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,kCAAkC;iBAAA;YAAE;SAC3D;QACA,kCAAkC;YAAC,oCAAoC;SAAA;QACvE,oCAAoC;YAClC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,wCAAwC;iBAAA;YAAE;SACjE;QACA,wCAAwC;YAAC,4BAA4B;SAAA;QACrE,yCAAyC;YAAC,8BAA8B;SAAA;QACxE,yCAAyC;YACvC;SACF;QACA,QAAQ;YAAC,gCAAgC;SAAA;QACzC,kBAAkB;YAAC,WAAW;SAAA;QAC9B,SAAS;YAAC,wBAAwB;SAAA;QAClC,eAAe;YAAC,uBAAuB;SAAA;QACvC,mBAAmB;YAAC,iCAAiC;SAAA;QACrD,2BAA2B;YACzB;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,+BAA+B;iBAAA;YAAE;SACxD;QACA,+BAA+B;YAAC,iCAAiC;SAAA;QACjE,iCAAiC;YAC/B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,qCAAqC;iBAAA;YAAE;SAC9D;QACA,qCAAqC;YAAC,yBAAyB;SAAA;QAC/D,sCAAsC;YACpC;SACF;QACA,MAAM;YAAC,YAAY;SAAA;QACnB,kBAAkB;YAAC,qDAAqD;SAAA;QACxE,4BAA4B;YAC1B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,gCAAgC;iBAAA;YAAE;SACzD;QACA,gCAAgC;YAAC,kBAAkB;SAAA;QACnD,4BAA4B;YAC1B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,gCAAgC;iBAAA;YAAE;SACzD;QACA,gCAAgC;YAAC,kBAAkB;SAAA;QACnD,6BAA6B;YAC3B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,iCAAiC;iBAAA;YAAE;SAC1D;QACA,iCAAiC;YAAC,qBAAqB;SAAA;QACvD,mCAAmC;YAAC,qBAAqB;SAAA;QACzD,sBAAsB;YAAC,iCAAiC;SAAA;QACxD,sBAAsB;YAAC,iCAAiC;SAAA;QACxD,6BAA6B;YAC3B;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,iCAAiC;iBAAA;YAAE;SAC1D;QACA,iCAAiC;YAAC,oBAAoB;SAAA;QACtD,oBAAoB;YAAC,gCAAgC;SAAA;QACrD,kCAAkC;YAChC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,sCAAsC;iBAAA;YAAE;SAC/D;QACA,sCAAsC;YAAC,yBAAyB;SAAA;QAChE,uBAAuB;YAAC,4BAA4B;SAAA;QACpD,mCAAmC;YACjC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,uCAAuC;iBAAA;YAAE;SAChE;QACA,uCAAuC;YAAC,gBAAgB;SAAA;QACxD,wCAAwC;YAAC,2BAA2B;SAAA;QACpE,2BAA2B;YAAC,uCAAuC;SAAA;QACnE,wCAAwC;YAAC,4BAA4B;SAAA;QACrE,2BAA2B;YAAC,wCAAwC;SAAA;QACpE,2CAA2C;YACzC;YACA,CAAC;YACD;gBAAE,SAAS;oBAAC;oBAAS,+CAA+C;iBAAA;YAAE;SACxE;QACA,+CAA+C;YAC7C;SACF;QACA,SAAS;YAAC,gCAAgC;SAAA;QAC1C,UAAU;YAAC,mCAAmC;SAAA;QAC9C,qBAAqB;YAAC,aAAa;SAAA;IACrC;AACF;AAEA,IAAO,oBAAQ", "debugId": null}}, {"offset": {"line": 3259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/node_modules/%40octokit/src/endpoints-to-methods.ts"], "sourcesContent": ["import type { Octokit } from \"@octokit/core\";\nimport type { EndpointOptions, RequestParameters, Route } from \"@octokit/types\";\nimport ENDPOINTS from \"./generated/endpoints.js\";\nimport type { RestEndpointMethods } from \"./generated/method-types.js\";\nimport type { EndpointDecorations } from \"./types.js\";\n\n// The following code was refactored in: https://github.com/octokit/plugin-rest-endpoint-methods.js/pull/622\n// to optimise the runtime performance of Octokit initialization.\n//\n// This optimization involves two key changes:\n// 1. Pre-Computation: The endpoint methods are pre-computed once at module load\n//    time instead of each invocation of `endpointsToMethods()`.\n// 2. Lazy initialization and caching: We use a Proxy for each scope to only\n//    initialize methods that are actually called. This reduces runtime overhead\n//    as the initialization involves deep merging of objects. The initialized\n//    methods are then cached for future use.\n\nconst endpointMethodsMap = new Map();\nfor (const [scope, endpoints] of Object.entries(ENDPOINTS)) {\n  for (const [methodName, endpoint] of Object.entries(endpoints)) {\n    const [route, defaults, decorations] = endpoint;\n    const [method, url] = route.split(/ /);\n    const endpointDefaults = Object.assign(\n      {\n        method,\n        url,\n      },\n      defaults,\n    );\n\n    if (!endpointMethodsMap.has(scope)) {\n      endpointMethodsMap.set(scope, new Map());\n    }\n\n    endpointMethodsMap.get(scope).set(methodName, {\n      scope,\n      methodName,\n      endpointDefaults,\n      decorations,\n    });\n  }\n}\n\ntype ProxyTarget = {\n  octokit: Octokit;\n  scope: string;\n  cache: Record<string, (...args: any[]) => any>;\n};\n\nconst handler = {\n  has({ scope }: ProxyTarget, methodName: string) {\n    return endpointMethodsMap.get(scope).has(methodName);\n  },\n  getOwnPropertyDescriptor(target: ProxyTarget, methodName: string) {\n    return {\n      value: this.get(target, methodName), // ensures method is in the cache\n      configurable: true,\n      writable: true,\n      enumerable: true,\n    };\n  },\n  defineProperty(\n    target: ProxyTarget,\n    methodName: string,\n    descriptor: PropertyDescriptor,\n  ) {\n    Object.defineProperty(target.cache, methodName, descriptor);\n    return true;\n  },\n  deleteProperty(target: ProxyTarget, methodName: string) {\n    delete target.cache[methodName];\n    return true;\n  },\n  ownKeys({ scope }: ProxyTarget) {\n    return [...endpointMethodsMap.get(scope).keys()];\n  },\n  set(target: ProxyTarget, methodName: string, value: any) {\n    return (target.cache[methodName] = value);\n  },\n  get({ octokit, scope, cache }: ProxyTarget, methodName: string) {\n    if (cache[methodName]) {\n      return cache[methodName];\n    }\n\n    const method = endpointMethodsMap.get(scope).get(methodName);\n    if (!method) {\n      return undefined;\n    }\n\n    const { endpointDefaults, decorations } = method;\n\n    if (decorations) {\n      cache[methodName] = decorate(\n        octokit,\n        scope,\n        methodName,\n        endpointDefaults,\n        decorations,\n      );\n    } else {\n      cache[methodName] = octokit.request.defaults(endpointDefaults);\n    }\n\n    return cache[methodName];\n  },\n};\n\nexport function endpointsToMethods(octokit: Octokit): RestEndpointMethods {\n  const newMethods = {} as { [key: string]: object };\n\n  for (const scope of endpointMethodsMap.keys()) {\n    newMethods[scope] = new Proxy({ octokit, scope, cache: {} }, handler);\n  }\n\n  return newMethods as RestEndpointMethods;\n}\n\nfunction decorate(\n  octokit: Octokit,\n  scope: string,\n  methodName: string,\n  defaults: EndpointOptions,\n  decorations: EndpointDecorations,\n) {\n  const requestWithDefaults = octokit.request.defaults(defaults);\n\n  /* istanbul ignore next */\n  function withDecorations(\n    ...args: [Route, RequestParameters?] | [EndpointOptions]\n  ) {\n    // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n    let options = requestWithDefaults.endpoint.merge(...args);\n\n    // There are currently no other decorations than `.mapToData`\n    if (decorations.mapToData) {\n      options = Object.assign({}, options, {\n        data: options[decorations.mapToData],\n        [decorations.mapToData]: undefined,\n      });\n      return requestWithDefaults(options);\n    }\n\n    if (decorations.renamed) {\n      const [newScope, newMethodName] = decorations.renamed;\n      octokit.log.warn(\n        `octokit.${scope}.${methodName}() has been renamed to octokit.${newScope}.${newMethodName}()`,\n      );\n    }\n    if (decorations.deprecated) {\n      octokit.log.warn(decorations.deprecated);\n    }\n\n    if (decorations.renamedParameters) {\n      // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n      const options = requestWithDefaults.endpoint.merge(...args);\n\n      for (const [name, alias] of Object.entries(\n        decorations.renamedParameters,\n      )) {\n        if (name in options) {\n          octokit.log.warn(\n            `\"${name}\" parameter is deprecated for \"octokit.${scope}.${methodName}()\". Use \"${alias}\" instead`,\n          );\n          if (!(alias in options)) {\n            options[alias] = options[name];\n          }\n          delete options[name];\n        }\n      }\n      return requestWithDefaults(options);\n    }\n\n    // @ts-ignore https://github.com/microsoft/TypeScript/issues/25488\n    return requestWithDefaults(...args);\n  }\n  return Object.assign(withDecorations, requestWithDefaults);\n}\n"], "names": ["options"], "mappings": ";;;AAEA,OAAO,eAAe;;AAetB,MAAM,qBAAqB,aAAA,GAAA,IAAI,IAAI;AACnC,KAAA,MAAW,CAAC,OAAO,SAAS,CAAA,IAAK,OAAO,OAAA,+MAAQ,UAAS,EAAG;IAC1D,KAAA,MAAW,CAAC,YAAY,QAAQ,CAAA,IAAK,OAAO,OAAA,CAAQ,SAAS,EAAG;QAC9D,MAAM,CAAC,OAAO,UAAU,WAAW,CAAA,GAAI;QACvC,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAI,MAAM,KAAA,CAAM,GAAG;QACrC,MAAM,mBAAmB,OAAO,MAAA,CAC9B;YACE;YACA;QACF,GACA;QAGF,IAAI,CAAC,mBAAmB,GAAA,CAAI,KAAK,GAAG;YAClC,mBAAmB,GAAA,CAAI,OAAO,aAAA,GAAA,IAAI,IAAI,CAAC;QACzC;QAEA,mBAAmB,GAAA,CAAI,KAAK,EAAE,GAAA,CAAI,YAAY;YAC5C;YACA;YACA;YACA;QACF,CAAC;IACH;AACF;AAQA,MAAM,UAAU;IACd,KAAI,EAAE,KAAA,CAAM,CAAA,EAAgB,UAAA,EAAoB;QAC9C,OAAO,mBAAmB,GAAA,CAAI,KAAK,EAAE,GAAA,CAAI,UAAU;IACrD;IACA,0BAAyB,MAAA,EAAqB,UAAA,EAAoB;QAChE,OAAO;YACL,OAAO,IAAA,CAAK,GAAA,CAAI,QAAQ,UAAU;YAAA,iCAAA;YAClC,cAAc;YACd,UAAU;YACV,YAAY;QACd;IACF;IACA,gBACE,MAAA,EACA,UAAA,EACA,UAAA,EACA;QACA,OAAO,cAAA,CAAe,OAAO,KAAA,EAAO,YAAY,UAAU;QAC1D,OAAO;IACT;IACA,gBAAe,MAAA,EAAqB,UAAA,EAAoB;QACtD,OAAO,OAAO,KAAA,CAAM,UAAU,CAAA;QAC9B,OAAO;IACT;IACA,SAAQ,EAAE,KAAA,CAAM,CAAA,EAAgB;QAC9B,OAAO,CAAC;eAAG,mBAAmB,GAAA,CAAI,KAAK,EAAE,IAAA,CAAK,CAAC;SAAA;IACjD;IACA,KAAI,MAAA,EAAqB,UAAA,EAAoB,KAAA,EAAY;QACvD,OAAQ,OAAO,KAAA,CAAM,UAAU,CAAA,GAAI;IACrC;IACA,KAAI,EAAE,OAAA,EAAS,KAAA,EAAO,KAAA,CAAM,CAAA,EAAgB,UAAA,EAAoB;QAC9D,IAAI,KAAA,CAAM,UAAU,CAAA,EAAG;YACrB,OAAO,KAAA,CAAM,UAAU,CAAA;QACzB;QAEA,MAAM,SAAS,mBAAmB,GAAA,CAAI,KAAK,EAAE,GAAA,CAAI,UAAU;QAC3D,IAAI,CAAC,QAAQ;YACX,OAAO,KAAA;QACT;QAEA,MAAM,EAAE,gBAAA,EAAkB,WAAA,CAAY,CAAA,GAAI;QAE1C,IAAI,aAAa;YACf,KAAA,CAAM,UAAU,CAAA,GAAI,SAClB,SACA,OACA,YACA,kBACA;QAEJ,OAAO;YACL,KAAA,CAAM,UAAU,CAAA,GAAI,QAAQ,OAAA,CAAQ,QAAA,CAAS,gBAAgB;QAC/D;QAEA,OAAO,KAAA,CAAM,UAAU,CAAA;IACzB;AACF;AAEO,SAAS,mBAAmB,OAAA,EAAuC;IACxE,MAAM,aAAa,CAAC;IAEpB,KAAA,MAAW,SAAS,mBAAmB,IAAA,CAAK,EAAG;QAC7C,UAAA,CAAW,KAAK,CAAA,GAAI,IAAI,MAAM;YAAE;YAAS;YAAO,OAAO,CAAC;QAAE,GAAG,OAAO;IACtE;IAEA,OAAO;AACT;AAEA,SAAS,SACP,OAAA,EACA,KAAA,EACA,UAAA,EACA,QAAA,EACA,WAAA,EACA;IACA,MAAM,sBAAsB,QAAQ,OAAA,CAAQ,QAAA,CAAS,QAAQ;IAG7D,SAAS,gBAAA,GACJ,IAAA,EACH;QAEA,IAAI,UAAU,oBAAoB,QAAA,CAAS,KAAA,CAAM,GAAG,IAAI;QAGxD,IAAI,YAAY,SAAA,EAAW;YACzB,UAAU,OAAO,MAAA,CAAO,CAAC,GAAG,SAAS;gBACnC,MAAM,OAAA,CAAQ,YAAY,SAAS,CAAA;gBACnC,CAAC,YAAY,SAAS,CAAA,EAAG,KAAA;YAC3B,CAAC;YACD,OAAO,oBAAoB,OAAO;QACpC;QAEA,IAAI,YAAY,OAAA,EAAS;YACvB,MAAM,CAAC,UAAU,aAAa,CAAA,GAAI,YAAY,OAAA;YAC9C,QAAQ,GAAA,CAAI,IAAA,CACV,CAAA,QAAA,EAAW,KAAK,CAAA,CAAA,EAAI,UAAU,CAAA,+BAAA,EAAkC,QAAQ,CAAA,CAAA,EAAI,aAAa,CAAA,EAAA,CAAA;QAE7F;QACA,IAAI,YAAY,UAAA,EAAY;YAC1B,QAAQ,GAAA,CAAI,IAAA,CAAK,YAAY,UAAU;QACzC;QAEA,IAAI,YAAY,iBAAA,EAAmB;YAEjC,MAAMA,WAAU,oBAAoB,QAAA,CAAS,KAAA,CAAM,GAAG,IAAI;YAE1D,KAAA,MAAW,CAAC,MAAM,KAAK,CAAA,IAAK,OAAO,OAAA,CACjC,YAAY,iBAAA,EACX;gBACD,IAAI,QAAQA,UAAS;oBACnB,QAAQ,GAAA,CAAI,IAAA,CACV,CAAA,CAAA,EAAI,IAAI,CAAA,uCAAA,EAA0C,KAAK,CAAA,CAAA,EAAI,UAAU,CAAA,UAAA,EAAa,KAAK,CAAA,SAAA,CAAA;oBAEzF,IAAI,CAAA,CAAE,SAASA,QAAAA,GAAU;wBACvBA,QAAAA,CAAQ,KAAK,CAAA,GAAIA,QAAAA,CAAQ,IAAI,CAAA;oBAC/B;oBACA,OAAOA,QAAAA,CAAQ,IAAI,CAAA;gBACrB;YACF;YACA,OAAO,oBAAoBA,QAAO;QACpC;QAGA,OAAO,oBAAoB,GAAG,IAAI;IACpC;IACA,OAAO,OAAO,MAAA,CAAO,iBAAiB,mBAAmB;AAC3D", "debugId": null}}, {"offset": {"line": 3382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/BaseGraph/basegraph/node_modules/%40octokit/src/index.ts"], "sourcesContent": ["import type { Octokit } from \"@octokit/core\";\n\nexport type { RestEndpointMethodTypes } from \"./generated/parameters-and-response-types.js\";\nimport { VERSION } from \"./version.js\";\nimport type { Api } from \"./types.js\";\nimport { endpointsToMethods } from \"./endpoints-to-methods.js\";\n\n// Export the type for downstream users in order to fix a TypeScript error\n// The inferred type of 'Octokit' cannot be named without a reference to '../node_modules/@octokit/plugin-rest-endpoint-methods/dist-types/types.js'. This is likely not portable. A type annotation is necessary.\nexport type { Api };\n\nexport function restEndpointMethods(octokit: Octokit): Api {\n  const api = endpointsToMethods(octokit);\n  return {\n    rest: api,\n  };\n}\nrestEndpointMethods.VERSION = VERSION;\n\nexport function legacyRestEndpointMethods(octokit: Octokit): Api[\"rest\"] & Api {\n  const api = endpointsToMethods(octokit);\n  return {\n    ...api,\n    rest: api,\n  };\n}\nlegacyRestEndpointMethods.VERSION = VERSION;\n"], "names": [], "mappings": ";;;;AAGA,SAAS,eAAe;AAExB,SAAS,0BAA0B;;;AAM5B,SAAS,oBAAoB,OAAA,EAAuB;IACzD,MAAM,4NAAM,qBAAA,EAAmB,OAAO;IACtC,OAAO;QACL,MAAM;IACR;AACF;AACA,oBAAoB,OAAA,kMAAU,UAAA;AAEvB,SAAS,0BAA0B,OAAA,EAAqC;IAC7E,MAAM,4NAAM,qBAAA,EAAmB,OAAO;IACtC,OAAO;QACL,GAAG,GAAA;QACH,MAAM;IACR;AACF;AACA,0BAA0B,OAAA,kMAAU,UAAA", "debugId": null}}]}