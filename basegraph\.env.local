# AI Configuration for Code Analysis
# Enable AI-powered code analysis (set to 'true' to enable)
ENABLE_AI_ANALYSIS=true

# =============================================================================
# OpenAI Configuration (Default)
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# =============================================================================
# Alternative Providers (OpenAI Compatible)
# =============================================================================

# Anthropic Claude (via OpenAI-compatible proxy)
# OPENAI_API_KEY=your_anthropic_api_key
# OPENAI_BASE_URL=https://api.anthropic.com/v1
# OPENAI_MODEL=claude-3-haiku-20240307

# Google Gemini (via OpenAI-compatible proxy)
# OPENAI_API_KEY=your_google_api_key
# OPENAI_BASE_URL=https://generativelanguage.googleapis.com/v1beta
# OPENAI_MODEL=gemini-1.5-flash

# Ollama (Local)
# OPENAI_API_KEY=ollama
# OPENAI_BASE_URL=http://localhost:11434/v1
# OPENAI_MODEL=codellama:7b

# Groq
# OPENAI_API_KEY=your_groq_api_key
# OPENAI_BASE_URL=https://api.groq.com/openai/v1
# OPENAI_MODEL=llama3-8b-8192

# Together AI
# OPENAI_API_KEY=your_together_api_key
# OPENAI_BASE_URL=https://api.together.xyz/v1
# OPENAI_MODEL=meta-llama/Llama-2-7b-chat-hf

# Perplexity AI
# OPENAI_API_KEY=your_perplexity_api_key
# OPENAI_BASE_URL=https://api.perplexity.ai
# OPENAI_MODEL=llama-3-sonar-small-32k-chat

# =============================================================================
# Usage Instructions:
# =============================================================================
# 1. Uncomment the provider you want to use
# 2. Add your API key
# 3. Set the appropriate base URL and model
# 4. Make sure ENABLE_AI_ANALYSIS=true
#
# Note: Some providers may require additional setup or proxy services
# to be OpenAI-compatible. Check their documentation for details.
